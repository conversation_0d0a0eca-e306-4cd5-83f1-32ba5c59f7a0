<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <ul class="nav">
                                <li>
                                    <p>View Status</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <form [formGroup]="approvalForm">
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">
                                                Filter Approval Status
                                            </legend>
                                            <div class="row">
                                                <div class="col-3">
                                                    <mat-form-field>
                                                        <mat-label> Status </mat-label>
                                                        <mat-select formControlName="status" (ngModelChange)="onChangeStatus($event)">
                                                            <mat-option *ngFor="let stat of statuses" [value]="stat.id">
                                                                {{ stat.name }}
                                                            </mat-option>
                                                        </mat-select>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <app-datep-picker [dateConfiguration]="FromDate"
                                                        [control]="approvalForm.controls.from_date"
                                                        [fieldName]="FromDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate('from')">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-3">
                                                    <app-datep-picker [dateConfiguration]="ToDate"
                                                        [control]="approvalForm.controls.to_date"
                                                        [fieldName]="ToDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate('to')">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-3">
                                                    <button mat-raised-button type="submit"
                                                        [disabled]="isDisableActions"
                                                        class="btn btn-primary pull-right" (click)="takeAction('approve')">
                                                        Approve
                                                    </button>
                                                    <button mat-raised-button (click)="takeAction('reject')"
                                                        [disabled]="isDisableActions" class="btn btn-danger pull-right">
                                                        Reject
                                                    </button>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <div class="col-md-12">
                                    <div class="" style="overflow-x:auto;">
                                        <table id="towertable" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>

                                            <ng-container matColumnDef="select" *ngIf="approvalForm?.value.status == 0">
                                                <th id="" style="width: 5%;" mat-header-cell *matHeaderCellDef>
                                                    <mat-checkbox [checked]="selectAll" (click)="changeCheckbox($event, 'all')" [(ngModel)]="selectAll" ></mat-checkbox>
                                                </th>
                                                <td style="width: 5%;" mat-cell *matCellDef="let row">
                                                    <mat-checkbox [checked]="row.select" (click)="changeCheckbox(row, 'single')" [(ngModel)]="row.select"></mat-checkbox>
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="requestedDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Requested
                                                    Date </th>
                                                <td mat-cell *matCellDef="let row"> {{row?.requestedDate ? (row.requestedDate | localDateConversion: "date") : '--'}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="requestedBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Requested
                                                    By
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row?.requestedBy}}</td>
                                            </ng-container>

                                            <ng-container matColumnDef="module">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Module</th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.module}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="oldName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> {{approvalForm?.value.status == 0 ? 'Old' : 'Current'}} Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.displayOldCurrentName}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="oldStatus">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> {{approvalForm?.value.status == 0 ? 'Old' : 'Current'}} Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.displayOldCurrentStatus}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="newName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> {{approvalForm?.value.status == 0 ? 'New' : approvalForm?.value.status == 1 ? 'Approve' : 'Reject'}} Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.newName}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="newStatus">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> {{approvalForm?.value.status == 0 ? 'New' : approvalForm?.value.status == 1 ? 'Approve' : 'Reject'}} Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.newStatus == null ? '' : row?.newStatus ? 'Active' : 'In-Active'}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="reason">
                                                <th id="" style="min-width: 280px; max-width: 280px;" mat-header-cell *matHeaderCellDef mat-sort-header> Reason</th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.reason}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="displayStatus">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.displayStatus}} </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                                        </table>
                                        <div
                                            *ngIf="dataSource && dataSource.filteredData && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" [pageSize]="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>