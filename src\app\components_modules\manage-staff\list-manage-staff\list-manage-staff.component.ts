import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { ManageStaff } from "src/app/models/manageStaff";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from "@angular/router";

@Component({
  selector: "app-list-manage-staff",
  templateUrl: "./list-manage-staff.component.html",
  styleUrls: ["./list-manage-staff.component.scss", "../../../scss/table.scss"],
})
export class ListManageStaffComponent implements OnInit {
  displayedColumns: string[] = [
    "staff_id",
    "staff_name",
    "staff_type",
    "skill_level",
    "gender",
    "mobile_no",
    "status",
    "staff_id_edit",
  ];
  pdfData = [];
  dataSource: MatTableDataSource<ManageStaff>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly userService: UserService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getManageStaff();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "managestaff",
        "managestaff"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("managestaff", "managestaff");
    }
  }

  getManageStaff() {
    this.userService.getManageStaff().subscribe((res) => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Porter Name": key.staff_name,
              "Porter Type": key.staff_type,
              "Skill Level": key.skill_level,
              Gender: key.gender,
              Mobile: key.mobile_no,
              Status: key.status ? "Active" : "Inactive",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatemanagestaff/${data.staff_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatemanagestaff/${data.staff_id}`], { relativeTo: this.activatedRoute });
    }
  }

}
