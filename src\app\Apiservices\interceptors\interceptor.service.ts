import { Injectable } from "@angular/core";
import { StorageService } from "../stoargeService/storage.service";
import { ToastrService } from "ngx-toastr";
import { Router } from "@angular/router";
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpResponse,
  HttpErrorResponse,
} from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { map, catchError, switchMap } from "rxjs/operators";
import { LoaderService } from "../loader/loader.service";
import * as moment from "moment";
import { HttpService } from "../httpService/http.service";

@Injectable({
  providedIn: "root",
})
export class InterceptorService {
  private isRefreshing = false;
  constructor(
    private readonly storageService: StorageService,
    public toaster: ToastrService,
    private readonly router: Router,
    private readonly loaderSerice: LoaderService,
    private readonly httpService: HttpService
  ) { }

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>
  ): Observable<HttpEvent<any>> {
    this.loaderSerice.assignAllrequests(request.url);
    let token;
    const acesstoken = this.storageService.getData("access_token");
    const headers = Object.assign(
      {},
      {
        Authorization: "Bearer " + acesstoken,
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
        Expires: new Date().toString(),
      }
    );

    // const reqCloned = this.checkDate(request);
    token = request.clone({
      setHeaders: headers,
    });

    return next
      .handle(token)
      .pipe(
        map((event: HttpEvent<any>) => {
          if (event instanceof HttpResponse) {
            this.loaderSerice.removeUrlOnresponse(request.url);
            return event;
          }
        })
      )
      .pipe(
        catchError((err: HttpEvent<any>) => {
          if (err instanceof HttpErrorResponse) {
            this.loaderSerice.removeUrlOnresponse(request.url);
            if (err.error) {
              switch (err.status) {
                case 402:
                case 409:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    this.toaster.error("Duplicate");
                  }
                  break;
                case 500:
                  if (err.error && err.error.statusText) {
                    this.toaster.error(err.error.statusText);
                  } else {
                    this.toaster.error("Internal Server Error");
                  }
                  break;
                case 400:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    if (request.url.includes("download")) {
                      this.toaster.info("Image/file not found");
                    } else {
                      this.toaster.error("Bad Request");
                    }
                  }
                  break;
                case 401:
                  return this.handle401Error(request, next);
                  // if (
                  //   err.error &&
                  //   err.error.Message &&
                  //   !request.url.includes("refresh_token")
                  // ) {
                  //   this.idleService.stopTimer();
                  //   this.toaster.error(err.error.Message);
                  // } else {
                  //   if (!request.url.includes("refresh_token")) {
                  //     this.toaster.error(
                  //       "Authorization denied for the request."
                  //     );
                  //   }
                  // }
                  // this.storageService.removeAllData();
                  // this.router.navigate(["/"]);
                  break;
                case 403:
                  if (request.url.includes("refresh_token")) {
                    this.storageService.removeAllData();
                    this.router.navigate(["/"]);
                  }
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    this.toaster.error("Unauthorized action");
                  }
                  break;
                case 404:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    this.toaster.error(
                      "The resource you are looking for has been removed, had its name changed, or is temporarily unavailable."
                    );
                  }
                  break;
                case 412:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    this.toaster.error("Pre-condition failed");
                  }
                  break;
                case 503:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  } else {
                    this.toaster.error("Service not available");
                  }
                  break;
                default:
                  if (err.error && err.error.Message) {
                    this.toaster.error(err.error.Message);
                  }
                  return throwError(err);
              }
            }
          }
          return throwError(err);
        })
      );
  }
  checkDate(request: HttpRequest<any>) {
    let dateChangeRequest = {};
    const { body, params } = request;
    if (body) {
      Object.keys(body).forEach((data) => {
        const testSample = new RegExp(/(?:time|date)$/gm);
        if (moment(body[data]).isValid() && testSample.test(data)) {
          dateChangeRequest[data] = moment(body[data]).format(
            "YYYY/MM/DD HH:mm"
          );
        }
      });
    }
    request = request.clone({
      body: { ...body, ...dateChangeRequest },
    });
    return request;
  }

  private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      return this.httpService.refreshToken().pipe(
        switchMap((res: any) => {
          this.isRefreshing = false;
          localStorage.setItem('access_token', res.accessToken);
          return next.handle(req);
        }),
        catchError((error) => {
          this.isRefreshing = false;
          if (error && error.url.includes("refresh_token")) {
            this.toaster.error(
              "The session has been expired. Please try to login again."
            );
            console.log("The session has been expired. Please try to login again.");
            console.log(error);
          }

          this.storageService.removeAllData();
          this.router.navigate(["/"]);
          return throwError(error);
        })
      );
    }
    return next.handle(req);
  }
}
