import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-smart-assign',
  templateUrl: './smart-assign.component.html',
  styleUrls: ['./smart-assign.component.scss']
})
export class SmartAssignComponent implements OnInit {

  smartAssignForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    private readonly toaster: ToastrService,
    private readonly userService: UserService
  ) {
    this.smartAssignForm = this.fb.group({
      status: [''],
      id: [''],
      department_id: ['']
    });
  }

  ngOnInit() {
    this.smartAssignView();
  }

  smartAssignView() {
    this.userService.smartAssignView().subscribe(res => {
      res.status ? res.status = 'true' : res.status = 'false';
      this.smartAssignForm.patchValue(res);
    });
  }

  smartAssign() {
    const data = this.smartAssignForm.value;
    this.userService.smartAssign(this.smartAssignForm.value).subscribe(res => {
      this.toaster.success(`Successfully turned ${data.status === 'true' ? 'On' : 'Off'} smart assign`);
    }, err => {
      console.log(err);
    });
  }

}
