// .subList{
//     // margin-left: 40px;
//     font-weight: bold;
//     .btn{
//      margin-right: 20px;
//     }
//     .btn.btn-primary{
//         width: 170px !important;
//     }
//     .btn.btn-secondary{
//          background-color: grey;
//     }

// }
// .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
//     color: #ffffff;
//     border-color: #2f3d88;
//     background-color:#16078A;
// }
@import "../../../../assets/scss/navbar.scss";

.sidenav__css {
  width: 14%;
}

.sidenav__report__header {
  font-size: 18px;
  color: #1b2581;
  font-weight: 600;
}
.menu__option {
  cursor: pointer;
  font-size: 12px;
}

.menu__arrow {
  position: absolute;
  z-index: 4;
  min-width: 4px !important;
}

.arrow {
  position: absolute;
  top: 16px;
  left: 15px;
}

.active {
  background-color: #1b2581;
  color: white;
}
