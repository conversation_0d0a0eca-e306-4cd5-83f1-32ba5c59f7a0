import { Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { MatPaginator, MatTableDataSource } from "@angular/material";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";

@Component({
  selector: "app-audit-report",
  templateUrl: "./audit-report.component.html",
  styleUrls: ["./audit-report.component.scss", "../../../scss/table.scss"],
})
export class AuditReportComponent implements OnInit {
  auditReportForm: FormGroup;
  categoryList: any[] = ["Success", "Error", "Security"];
  dataSource: MatTableDataSource<any> = new MatTableDataSource([]);
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  displayedColumns: string[] = [
    "date_time",
    "user_id",
    "category",
    "requested_url",
    "terminal_identity",
    "event_type",
    "event_details",
    "exception",
  ];
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  tables: { heads: string[]; datas: any }[];
  constructor(
    private fb: FormBuilder,
    private apiService: FacilityConfigService,
    private toastr: ToastrService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.createAuditLogForm();
    this.searchByData();
  }

  createAuditLogForm() {
    this.auditReportForm = this.fb.group({
      category: [null],
      event_type: [null],
      from_date: [new Date()],
      to_date: [new Date()],
      username: [null],
      request_ip: [null],
    });
  }

  searchByData() {
    if (this.auditReportForm.valid) {
      this.fetchAuditReportList();
    }
  }

  restForm() {
    this.createAuditLogForm();
  }

  fetchAuditReportList() {
    const { to_date, from_date } = this.auditReportForm.value;
    const newJson = {
      ...this.auditReportForm.value,
      to_date: moment(to_date).format("YYYY/MM/DD"),
      from_date: moment(from_date).format("YYYY/MM/DD"),
    };
    this.apiService.getListAuditReport(newJson).subscribe((data) => {
      this.dataSource = new MatTableDataSource(data ? data : []);
      this.dataSource.paginator = this.paginator;
      this.createtables();
    });
  }
  getDate() {
    if (
      this.auditReportForm.get("from_date").value &&
      this.auditReportForm.get("to_date").value
    ) {
      if (
        this.auditReportForm.get("from_date").value >
        this.auditReportForm.get("to_date").value
      ) {
        this.auditReportForm.get("to_date").setValue("");
        this.toastr.error(
          "To date should be greater or equal to From date",
          "Error"
        );
      }
    }
  }

  createtables() {
    this.dataSource.connect().subscribe((data) => {
      this.tables = [
        {
          heads: [
            "date_time",
            "user_id",
            "category",
            "requested_url",
            "terminal_identity",
            "event_type",
            "files_accessed",
            "event_details",
            "exception",
          ],
          datas: data.map((data: any) => {
            return {
              date_time: moment(data.request_date).format("DD/MM/YYYY HH:mm"),
              user_id: data.username,
              category: data.category,
              requested_url: data.request_url,
              terminal_identity: data.terminal_identity,
              event_type: data.event_type,
              event_details: data.event_details,
              exception: data.exception,
            };
          }),
        },
      ];
    });
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "enhancedReport",
        "AuditReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("enhancedReport", "AuditReport_list");
    }
  }
}
