import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { JobRequestsRoutingModule } from "./job-requests-routing.module";
import { ListJobRequestsComponent } from "./list-job-requests/list-job-requests.component";
import { AddUpdateJobRequestsComponent } from "./add-update-job-requests/add-update-job-requests.component";
import { SharedModule } from "src/app/shared/shared.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { SearchJobStatusComponent } from "./search-job-status/search-job-status.component";
import { UploadJobsComponent } from "./upload-jobs/upload-jobs.component";
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatCardModule } from "@angular/material/card";
import { MatTabsModule } from "@angular/material/tabs";
import { MatTableModule } from "@angular/material/table";
import { MatTooltipModule } from "@angular/material/tooltip";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";
import { AssignJobComponent } from "./list-job-requests/assign-job/assign-job.component";
import { MatDialogModule } from "@angular/material/dialog";
import { BaseTimerComponent } from "src/app/shared-theme/base-timer/base-timer.component";

@NgModule({
  declarations: [
    ListJobRequestsComponent,
    AddUpdateJobRequestsComponent,
    SearchJobStatusComponent,
    UploadJobsComponent,
    AssignJobComponent, 
  ],
  imports: [
    CommonModule,
    JobRequestsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatToolbarModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatTooltipModule,
    NgxMatSelectSearchModule,
    MatDialogModule,
    SharedThemeModule
  ],
  entryComponents: [AssignJobComponent],
})
export class JobRequestsModule {}
