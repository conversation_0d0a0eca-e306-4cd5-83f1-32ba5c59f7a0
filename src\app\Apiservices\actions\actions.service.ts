import { Injectable } from '@angular/core';
import { HttpService } from '../httpService/http.service';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ActionsService {

  constructor(
    private readonly httpService: HttpService
  ) { }

  getMassageStaff(): Observable<any> {
    return this.httpService.get(`${environment.base_url}`);
  }

  getPorters(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/portermaps`);
  }

  portersBreakOnOff(id, action, data?): Observable<any> {
    const queryParam = data.duration && data.duration !== '' ? `?duration=${data.duration}` : ''
    return this.httpService.put(`${environment.base_url}api/portermaps/${id}/break/${action}${queryParam}`);
  }

  getUnassignedPhonesPorter(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/portermaps/unassigned/phones`);
  }

  getUnassignedPorter(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/portermaps/unassigned/porters`);
  }

  addUpadtePorter(data, url, action): Observable<any> {
    if (action === 'add') {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  getPorterById(id) {
    return this.httpService.get(`${environment.base_url}api/stationmappings/${id}`);
  }

  deletePorter(id): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/portermaps/delete/${id}`);
  }
}
