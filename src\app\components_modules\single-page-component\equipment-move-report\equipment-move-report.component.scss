@import "../../../../assets/scss/navbar.scss";
th.mat-header-cell,
td.mat-cell {
  text-align: center !important;
  border: 1px solid #ccc !important;
  padding: 0 !important;
}

td,
th {
  width: 200px !important;
  max-width: 200px;
  min-width: 200px;
}

fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000 !important;
  height: 152px;
}

::ng-deep .equipmentReport .mat-sort-header-container {
  display: flex;
  justify-content: center;
}

legend.scheduler-border {
  font-size: 1em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}
legend {
  display: block;
}
.btn-primary {
  margin-right: 10px;
}
.spacing {
  margin-bottom: 12px;
}
.equipmentReport tr:first-child th:first-child {
  border-bottom: none !important;
}

.equipmentReport tr:nth-child(2) th:first-child {
  border-top: none !important;
  padding-bottom: 42px !important;
}
