<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right;">

                                <button mat-raised-button color="primary" routerLink="./addroles">Add
                                    Roles </button>
                            </p>
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="nav">
                                    <p>View Roles
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <div class="row">
                                            <div class="col-3">
                                                <div class="col">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2">
                                                <button class="btn btn-sm  btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="rolestable" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="role_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Role Name
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.role_name}} </td>
                                            </ng-container>
                                            <ng-container matColumnDef="status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Status
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.status}} </td>
                                            </ng-container>
                                            <ng-container matColumnDef="user_count">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Active
                                                    Users
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.user_count}} </td>
                                            </ng-container>
                                            <ng-container matColumnDef="role_Id">
                                                <th id="" mat-header-cell *matHeaderCellDef> Edit </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        routerLink="./updateroles/{{row.role_id}}">edit</em>
                                                </td>
                                            </ng-container>
                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf [heads]="['Role Name', 'Status', 'Active Users']" [title]="'Roles'" [datas]="pdfData">
</app-table-for-pdf>