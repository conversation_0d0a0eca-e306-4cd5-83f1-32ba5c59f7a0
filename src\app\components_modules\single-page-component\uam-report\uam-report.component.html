<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('pdf')"
                    >
                      PDF
                    </p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Role Module</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin"></div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table class="table">
                      <tr>
                        <td style="border: none">Modules</td>
                        <td
                          style="border: none"
                          colspan="4"
                          class="text-center"
                        >
                          Roles
                        </td>
                      </tr>
                      <thead>
                        <tr>
                          <th scope="col"></th>
                          <th scope="col" *ngFor="let roles of roles$ | async">
                            {{ roles.role_name }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container
                          *ngFor="let module of roleModuleAllAssign$ | async"
                        >
                          <tr>
                            <td>
                              {{ module?.sub_menu }}
                            </td>
                            <ng-container
                              *ngFor="let roleModule of module?.roles"
                            >
                              <td>
                                <mat-icon>{{
                                  roleModule ? "done" : "clear"
                                }}</mat-icon>
                              </td>
                            </ng-container>
                          </tr>
                        </ng-container>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="['Modules', allRoleForExcel$ | async]"
  [title]="'UAM Report'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<!-- this table only to import excel there is no other use -->
<table class="table" style="display: none" id="uamReportDumb">
  <tr>
    <td>Modules</td>
    <td>Roles</td>
  </tr>
  <thead>
    <tr>
      <th scope="col" *ngFor="let roles of roles$ | async">
        {{ roles.role_name }}
      </th>
    </tr>
  </thead>
  <tbody>
    <ng-container *ngFor="let module of roleModuleAllAssign$ | async">
      <tr>
        <td>
          {{ module?.sub_menu }}
        </td>
        <ng-container *ngFor="let roleModule of module?.roles">
          <td>
            <mat-icon class="mat_icon_css">{{
              roleModule ? true : false
            }}</mat-icon>
          </td>
        </ng-container>
      </tr>
    </ng-container>
  </tbody>
</table>
