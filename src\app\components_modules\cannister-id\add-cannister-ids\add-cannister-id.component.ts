import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-add-cannister-id',
  templateUrl: './add-cannister-id.component.html',
  styleUrls: ['./add-cannister-id.component.scss']
})
export class AddCannisterIdComponent implements OnInit {
  cannisterForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) { }

  ngOnInit() {
    this.addCannisterForm()
  }


  addCannisterForm() {
    this.cannisterForm = this.fb.group({
      CannisterId: ['', Validators.required],
      Status: ['true', Validators.required]
    });
  }

  saveCannisterId(actiontype) {
    let Id: any;
    if (this.cannisterForm.valid) {
      const data = this.cannisterForm.value;
      data.Status = data.Status === 'true' ? true : false;
      if (actiontype === 'update') {
        Id = Number(this.activatedRoute.snapshot.params.id);
      }
      this.facilityConfig.addUpdateCannisterId(
        data, actiontype === 'save' ?
        'api/CannisterIds/add' : `api/CannisterIds/edit/${Id}`, actiontype)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} Cannister Id`, 'Success');
        }, err => {
          console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.cannisterForm.markAllAsTouched();
    }
  }

}
