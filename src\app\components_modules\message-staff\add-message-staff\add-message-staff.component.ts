import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { SettingsService } from "src/app/Apiservices/settings/settings.service";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-add-message-staff",
  templateUrl: "./add-message-staff.component.html",
  styleUrls: ["./add-message-staff.component.scss"],
})
export class AddMessageStaffComponent implements OnInit {
  messages: any = [];
  staffs: any = [];
  messageStaffForm: FormGroup;
  activeIndexStaff: any[] = [];
  activeIndexMessage: any;
  @Output() refreshMessageList: EventEmitter<boolean> = new EventEmitter();
  filteredStaff: Array<any> = [];
  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly settingService: SettingsService,
    private readonly userService: UserService,
    private route: Router
  ) {}

  createMessageForm() {
    this.messageStaffForm = this.fb.group({
      staff_list: ["", Validators.required],
      staff_mobile: [{ value: "", disabled: true }, Validators.required],
      message: ["", Validators.required],
      canned_message: ["", Validators.required],
    });
  }
  ngOnInit() {
    this.createMessageForm();
    this.getMessageTemplate();
    this.getStaffs();
  }
  getMessageTemplate() {
    this.settingService.getMessageTemplates().subscribe((res) => {
      res = (res && res.length && res.filter((val) => val.status === 1)) || [];
      this.messages = res;
    });
  }
  getStaffs() {
    this.userService.getMessageStaffActive().subscribe((res) => {
      this.staffs = res;
      this.filteredStaff = res;
    });
  }

  saveMessageStaff() {
    if (this.messageStaffForm.valid) {
      const data = this.messageStaffForm.get("staff_list").value;
      let staffData = {};
      const obj = [];
      data.forEach((val) => {
        obj.push({ staff_id: val.staff_id, phone_no: val.phone_no });
      });
      staffData = {
        staff_list: obj,
        message: this.messageStaffForm.get("message").value,
      };
      this.userService.addMessageStaff(staffData).subscribe(
        (res) => {
          this.refreshMessageList.emit(true);
          this.toastr.success(`Successfully added message`, "Success");
          this.createMessageForm();
          this.activeIndexMessage = undefined;
          this.activeIndexStaff = [];
        },
        (err) => {}
      );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.messageStaffForm.markAllAsTouched();
    }
  }
  mobileNoSelected() {
    const a = this.messageStaffForm.get("staff_list").value;
    const b = [];
    a.forEach((res) => {
      this.staffs.forEach((res2) => {
        if (res.staff_id === res2.staff_id) {
          if (res2.phone_no) {
            b.push(res2.phone_no);
          }
        }
      });
    });
    this.messageStaffForm.get("staff_mobile").setValue(b.join());
  }

  messageSelected(index, message) {
    this.messageStaffForm.get("message").setValue(message);
    this.messageStaffForm.get("canned_message").setValue(message);

    this.activeIndexMessage = index;
  }

  setActiveStaffName(index, staff) {
    const staffList = this.messageStaffForm.get("staff_list").value;
    if (this.activeIndexStaff.includes(staff.staff_id)) {
      const filterLocation = this.activeIndexStaff.filter(
        (data) => data !== staff.staff_id
      );
      this.activeIndexStaff = filterLocation;
      const newFilterStaff = staffList.filter(
        (data) => data.staff_id !== staff.staff_id
      );
      this.messageStaffForm.get("staff_list").setValue([...newFilterStaff]);
    } else {
      this.activeIndexStaff = [
        ...this.activeIndexStaff,
        this.staffs[index].staff_id,
      ];
      this.messageStaffForm.get("staff_list").setValue([...staffList, staff]);
    }
    this.mobileNoSelected();
  }

  filterStaffList(key) {
    this.filteredStaff = this.staffs.filter((data) => {
      return data.staff_name.toLowerCase().includes(key.toLowerCase());
    });
  }

  resetFormMessage() {
    this.createMessageForm();
    // this.messageStaffForm.reset(this.messageStaffForm.value)
    this.messageStaffForm.controls["message"].clearValidators();
    // this.messageStaffForm.controls['message'].patchValue('')
    this.activeIndexStaff = [];
    this.activeIndexMessage = undefined;
    this.messageStaffForm.markAsPristine();
    this.messageStaffForm.markAsUntouched();
    this.messageStaffForm.updateValueAndValidity();
  }
}
