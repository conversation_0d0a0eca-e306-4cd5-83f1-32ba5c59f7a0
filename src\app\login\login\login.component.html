<section style="height: 100%">
  <!-- fixed-top  -->
  <nav
    class="navbar navbar-color-on-scroll navbar-transparent navbar-expand-lg"
    color-on-scroll="100"
    id="sectionsNav"
    style="height: 72px"
  >
    <div class="container-fluid">
      <div class="navbar-translate">
        <img
          src="./assets/img/UEMS_Solutions_logo.jpg"
          alt=""
          style="height: 40px"
        />
        <button
          class="navbar-toggler"
          type="button"
          data-toggle="collapse"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="sr-only">Toggle navigation</span>
          <span class="navbar-toggler-icon"></span>
          <span class="navbar-toggler-icon"></span>
          <span class="navbar-toggler-icon"></span>
        </button>
      </div>
      <img [src]="logoUrl" alt="logo" style="max-height: 50px" />
    </div>
  </nav>
  <div class="page-header header-filter" style="height: calc(100% - 118px)">
    <div class="container" style="padding-top: 150px">
      <div class="row">
        <div class="col-lg-4 col-md-6 col-sm-8 ml-auto mr-auto">
          <form class="form" [formGroup]="loginForm">
            <div class="card card-login card-hidden">
              <div
                class="card-header card-header-primary text-center card__header__grey"
              >
                <blockquote class="blockquote">
                  <h4><em>UE</em>Track™ - Portering</h4>
                </blockquote>
              </div>
              <div class="card-body">
                <div class="input-group label-floating">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <em class="material-icons">account_box</em>
                    </span>
                  </div>
                  <input
                    type="text"
                    formControlName="username"
                    class="form-control"
                    placeholder="Username"
                  />
                </div>
                <div class="errormsg">
                  <app-error-message
                    [control]="loginForm.controls.username"
                    [fieldType]="'enter'"
                    [fieldName]="'Username'"
                  >
                  </app-error-message>
                </div>
                <span class="bmd-form-group">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <em class="material-icons">lock_outline</em>
                      </span>
                    </div>
                    <input
                      type="password"
                      formControlName="password"
                      class="form-control"
                      placeholder="Password..."
                    />
                  </div>
                  <div class="errormsg">
                    <app-error-message
                      [control]="loginForm.controls.password"
                      [fieldType]="'enter'"
                      [fieldName]="'Password'"
                    >
                    </app-error-message>
                  </div>
                </span>

                <!-- company -->
                <span class="bmd-form-group">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <em class="material-icons">domain</em>
                      </span>
                    </div>
                    <mat-form-field style="width: 82%">
                      <mat-label class="make_label">Domain </mat-label>
                      <mat-select
                        formControlName="ad_url"
                        panelClass="set__location__input"
                      >
                        <mat-option
                          class="mat__option__select"
                          *ngFor="let domain of domainList"
                          [value]="domain.ad_url"
                          >{{ domain.ad_name }}</mat-option
                        >
                      </mat-select>
                    </mat-form-field>
                  </div>
                </span>
              </div>
              <div class="card-footer justify-content-center">
                <button
                  href="#pablo"
                  type="submit"
                  class="btn btn-rose btn-link btn-lg"
                  (click)="login()"
                >
                  Login
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <footer class="footer">
    <div class="container-fluid">
      <app-footer></app-footer>
    </div>
  </footer>
</section>
