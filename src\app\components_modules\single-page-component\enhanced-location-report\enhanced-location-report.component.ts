import { TableUtil } from './../../../utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from './../../../Apiservices/facilityConfig/facility-config.service';
import { UserService } from './../../../Apiservices/userService/user.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatOption, MatPaginator, MatSelect, MatSort, MatTableDataSource } from '@angular/material';
import * as moment from "moment";
import { ToastrService } from 'ngx-toastr';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { isNumber } from 'util';

@Component({
	selector: 'app-enhanced-location-report',
	templateUrl: './enhanced-location-report.component.html',
	styleUrls: ['./enhanced-location-report.component.scss']
})
export class EnhancedLocationReportComponent implements OnInit {
	routes = [
		{ path: "./../enhancedReports", label: "Search (ACK)" },
		{ path: "./../busrouteSearch", label: "Bus Route Reports" },
		{ path: "./../porterReports", label: "Porter Workload" },
		{ path: './../pts-reports', label: 'PTS Reports' },
		{ path: './../enhanced-location-reports', label: 'Location Reports' }
	];
	displayedColumns: string[] = [
		"order_no",
		"request_type",
		"order_from",
		"task_time",
		"assign_time",
		"start_time",
		"completion_time",
		"cancel_time",
		"requestor",
		"patient_name",
		"nric",
		"from_location",
		"from_bed",
		"to_location",
		"to_bed",
		"return_location",
		"return_bed",
		"remarks",
		"job_status",
		"main_category",
		"sub_category",
		"transport_mode",
		"std_kpi",
		"req_resp",
		"resp_comp",
		"within_kpi",
		"isolation_precaution",
		"resource1",
		"resource2",
		"created_date",
		"created_by",
		"modified_by",
		"modified_date",
		"assigned_by",
		"delay_reason",
		"cancel_reason",
		"cancel_remarks",
		"cancelled_by"
	];
	dataSource: MatTableDataSource<any[]>;
	exportResult: any;
	locationType = [];
	statuss = [
		{ condition: "Assigned" },
		{ condition: "Completed" },
		{ condition: "In Queue" },
		{ condition: "Cancelled" },
		{ condition: "Responded" },
	];
	today = new Date().toUTCString();
	prevMonth = moment(this.today).subtract(1, "months");
	month = this.prevMonth["_d"];
	locations = [];
	locationReportForm: FormGroup;
	@ViewChild(MatSort, { static: true }) sort: MatSort;
	@ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
	// @ViewChild("ackImageRef", { static: false })
	// ackImageRef: TemplateRef<unknown>;
	locationCtrl = new FormControl();
	protected _onDestroy = new Subject<void>();
	public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
	public locationFilterCtrl: FormControl = new FormControl();
	@ViewChild(MatOption, { static: true }) allSelected: MatOption;
	@ViewChild(MatSelect, { static: true }) select: MatSelect;
	selected: any;
	FromDate = {
		rangePicker: false,
		collumns: "col-16",
		label: "From Date",
		required: false,
		minDateStart: "2000-12-12",
		maxDateStart: "",
		minDateEnd: "",
		maxDateEnd: "",
		readonly: true,
	};
	ToDate = {
		rangePicker: false,
		collumns: "col-16",
		label: "To Date",
		required: false,
		minDateStart: "2000-12-12",
		maxDateStart: "",
		minDateEnd: "",
		maxDateEnd: "",
		readonly: true,
	};
	count = 0;
	location_id = [];
	loc = [];
	from_date: any = new Date();
	to_date: any = new Date();
	pageSize = 50;
	constructor(
		private readonly fb: FormBuilder,
		private readonly toastr: ToastrService,
		private readonly userService: UserService
	) {
		this.createForm();
	}

	createForm() {
		this.locationReportForm = this.fb.group({
			from_date: [new Date()],
			to_date: [new Date()],
			location: [""],
			nric: [""]
			// ack_required: [false],
		});
	}

	ngOnInit() {
		this.getLocations();
		this.getCurrentDateTime();
		// real time search location
		this.filteredLocations.next(this.locations.slice());
		this.locationFilterCtrl.valueChanges
			.pipe(takeUntil(this._onDestroy))
			.subscribe(() => {
				this.filterLocations();
			});
	}

	getCurrentDateTime() {
		this.userService.currentDatetime().subscribe((res) => {
			this.today = res;
			this.prevMonth = moment(this.today).subtract(1, "months");
			this.month = this.prevMonth["_d"];
			this.createForm();
		});
	}

	applyFilter(filterValue: string) {
		this.dataSource.filter = filterValue.trim().toLowerCase();
		if (this.dataSource.paginator) {
			this.dataSource.paginator.firstPage();
		}
	}

	_filteredLocation(value: string) {
		const filterValue = value.toLowerCase();
		return this.locations.filter(
			(category) => category.location_name.toLowerCase().indexOf(filterValue) === 0
		);
	}

	filterLocations() {
		if (!this.locations) {
			return;
		}
		// get the search keyword
		let search = this.locationFilterCtrl.value;
		// DON'T REMOVE THIS CODE
		if (!search) {
			this.filteredLocations.next(<any[]>this.locations.slice());
			return;
		} else {
			search = search.toLowerCase();
		}
		// filter the banks
		this.filteredLocations.next(
			this.locations.filter(
				(x) => x.locationName.toLowerCase().indexOf(search) > -1
			)
		);
	}

	tosslePerOne(all) {
		if (this.allSelected.selected) {
			this.allSelected.deselect();
			return false;
		}
		if (this.location_id.length == this.locations.length) {
			this.allSelected.select();
		}
		if (this.location_id.length == 0) {
			this.location_id = ['All'];
			this.selected = ['All']
		}
	}
	toggleAllSelection() {
		if (this.selected.includes('All')) {
			this.locations.map(item => {
				this.location_id[0] = 'All';
				this.location_id.push(item.locationName);
				this.select.options.forEach((item: MatOption) => item.select());
			})
		} else {
			this.location_id = [];
			this.select.options.forEach((item: MatOption) => item.deselect());
		}
		// if (this.selected.length == 0) {
		// 	this.location_id = ['All'];
		// 	this.selected = ['All']
		// }
	}

	restForm() {
		this.paginator.pageIndex = 0;
		this.paginator.pageSize = 50;
		// if (this.displayedColumns.includes("ack")) {
		// 	this.displayedColumns.splice(this.displayedColumns.indexOf("ack"), 1);
		// }
		this.createForm();
		this.searchByData();
	}

	getLocations() {
		this.location_id = ['All'];
		this.userService.getLocations().subscribe((res) => {
			if (res) {
				res.filter(data => {
					this.locations.push(data);
					this.loc.push(data.locationId)
					this.location_id.push(data.locationId);
				})
				this.filterLocations();
				this.searchByData();
			}
		});
	}

	async exportTable(fileType) {
		window.scrollTo(0, 0);
		this.searchByData(fileType);
		// if (this.locationReportForm.get("ack_required").value === true) {
		// 	// this.showAck = true;
		// 	this.searchByData(fileType);
		// } else {
		// 	// this.showAck = false;
		// 	this.searchByData(fileType);
		// }
	}

	mapTableDataforReport(exportResult, type?) {
		let mappedData;
		console.log(exportResult);
		let exportObj: any[] = [];
		for (var i = 0; i < exportResult.length; i++) {
			mappedData = {
				'Job No': exportResult[i].order_no,
				'Request Type': exportResult[i].request_type,
				'Order From': exportResult[i].order_from,
				'Task Time': exportResult[i].task_time,
				'Assigned Time': exportResult[i].assign_time,
				'Start Time': exportResult[i].start_time,
				'Completion Time': exportResult[i].completion_time,
				'Cancel Time': exportResult[i].cancel_time,
				'Requestor': exportResult[i].requestor,
				'Patient': exportResult[i].patient_name,
				'NRIC': exportResult[i].nric,
				'From': exportResult[i].from_location ? exportResult[i].from_location.toString().replace("\r\n", "") : "--",
				'From Bed': exportResult[i].from_bed,
				'To': exportResult[i].to_location ? exportResult[i].to_location.toString().replace("\r\n", "") : "--",
				'To Bed': exportResult[i].to_bed,
				'Return': exportResult[i].return_location ? exportResult[i].return_location.toString().replace("\r\n", "") : "--",
				'Return Bed': exportResult[i].return_bed,
				'Remarks': exportResult[i].remarks,
				'Job Status': exportResult[i].job_status,
				'Main Category': exportResult[i].main_category,
				'Sub Category': exportResult[i].sub_category,
				'Transport Mode': exportResult[i].transport_mode,
				'Std KPI': exportResult[i].std_kpi,
				'Req-Resp(Mins)': exportResult[i].req_resp,
				'Resp-Comp(Mins)': exportResult[i].resp_comp,
				'Within KPI': exportResult[i].within_kpi,
				'Isolation Precaution': exportResult[i].isolation_precaution,
				'Resource 1': exportResult[i].resource1,
				'Resource 2': exportResult[i].resource2,
				'Creation Time': exportResult[i].created_date,
				'Created By': exportResult[i].created_by,
				'Modified By': exportResult[i].modified_by,
				'Modified Date': exportResult[i].modified_date,
				'Assigned By': exportResult[i].assigned_by,
				'Delay Reason': exportResult[i].delay_reason,
				'Cancel Reason': exportResult[i].cancel_reason,
				'Cancel Remarks': exportResult[i].cancel_remarks,
				'Cancelled By': exportResult[i].cancelled_by,
			};
			exportObj.push(mappedData);
		}
		if (type == 'csv') {
			TableUtil.exportArrayToCsvDynamically(exportObj, "enhancedLocationReport_list");
		} else {
			TableUtil.exportArrayToExcelDynamically(exportObj, "enhancedLocationReport_list");
		}
	}

	searchByData(type?, paginationData?) {
		const data = {
			"pageNo": type && type == 'next' ? Number(paginationData.offset) + 1 : 1,
			"pageSize": (type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xlsx') ? this.count : this.paginator.pageSize) || 50,
			"from_date": this.locationReportForm.value.from_date,
			"to_date": this.locationReportForm.value.to_date,
			"locations": this.location_id.includes('All') ? this.loc : this.location_id,
			"nric": this.locationReportForm.value.nric || ''
		}
		this.userService.getEnhancedLocationReports(data).subscribe(res => {
			if (res && res.length > 0) {
				this.exportResult = res;
				this.exportResult.filter(result => {
					if (result.request_type && (result.request_type === 'Normal' || result.request_type === 'Urgent')) {
						result['task_time'] = result.created_date || '';
					} else {
						result['task_time'] = result.due_time || '';
					}
				})
				if (type && type == 'next') {
					this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
					this.exportResult.push(...res);
					this.exportResult.length = res[0].count;
					this.dataSource = new MatTableDataSource<any>(this.exportResult);
					this.dataSource._updateChangeSubscription();
					this.dataSource.paginator = this.paginator;
				} else if (type && (type == 'xlsx' || type == 'csv')) {
					this.mapTableDataforReport(this.exportResult, type)
				} else {
					this.paginator.pageIndex = 0
					this.exportResult.length = res[0].count;
					this.count = res[0].count;
					this.paginator.length = res[0].count;
					this.dataSource = new MatTableDataSource(this.exportResult || []);
					this.dataSource.sort = this.sort;
					this.dataSource.paginator = this.paginator;
				}
			} else {
				this.paginator.length = 0;
				this.dataSource = new MatTableDataSource([]);
			}
		}, (err) => {
			this.paginator.length = 0;
			this.dataSource = new MatTableDataSource([]);
		})
	}

	getDate() {
		if (
			this.locationReportForm.get("from_date").value &&
			this.locationReportForm.get("to_date").value
		) {
			if (
				this.locationReportForm.get("from_date").value >
				this.locationReportForm.get("to_date").value
			) {
				this.locationReportForm.get("to_date").setValue("");
				this.toastr.error("To date should be greater then From date", "Error");
			}
		}
	}

	pageChanged(event) {
		let pageIndex = event.pageIndex;
		let pageSize = event.pageSize;
		let previousIndex = event.previousPageIndex;
		let previousSize = pageSize * pageIndex;
		this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
	}

	getNextData(currentSize, offset, limit) {
		const paginationData = {
			'currentSize': currentSize,
			'offset': offset,
			'limit': limit
		}
		this.searchByData('next', paginationData);
	}

	clearFormValue(formField: string) {
		this.locationReportForm.get([formField]).setValue('')
	}

}
