import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-delay-reasons',
  templateUrl: './add-update-delay-reasons.component.html',
  styleUrls: ['./add-update-delay-reasons.component.scss']
})
export class AddUpdateDelayReasonsComponent implements OnInit {
  delayReasonForm: FormGroup;
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly settingService: SettingsService
  ) {
    this.delayReasonForm = this.fb.group({
      delay_reason: ['', Validators.required],
      allow_remarks: 0,
      status: 1,
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getDelayReason();
  }

  getDelayReason() {
    if (this.activatedRoute.snapshot.params.id) {
      this.settingService.getDelayReason(this.activatedRoute.snapshot.params.id).subscribe(res => {
        this.editLogs = res.edit_logs || [];
        this.editLogs.sort(function (a: any, b: any) {
          let B: any = new Date(b.created_Date);
          let A: any = new Date(a.created_Date);
          return B - A;
        });
        this.editLogs.map(log => {
          log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
          log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
        })
        this.delayReasonForm.patchValue(res ? res : {});
      }, err => {
        this.toastr.error('Error occured in fetching delay reason', 'Error');
      });
    }
  }


  saveDelayReason(actiontype) {
    const data = this.delayReasonForm.value;
    data.allow_remarks = data.allow_remarks === true || data.allow_remarks === 1 || data.allow_remarks === '1' ? 1 : 0;
    data.status = data.status === true || data.status === 1 || data.status === '1' ? 1 : 0;
    if (this.delayReasonForm.valid) {
      if (data.approval_status && data.approval_status == 'Pending') {
        Swal.fire({
          title: 'This request is already under process',
          text: `Are you sure, You want to update?`,
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: 'No',
          confirmButtonText: 'Yes'
        }).then((result) => {
          if (result.value) {
            this.updateDelayReason(data, actiontype);
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            return;
          }
        });
      } else {
        this.updateDelayReason(data, actiontype);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.delayReasonForm.markAllAsTouched();
    }
  }

  updateDelayReason(data, actiontype) {
    this.settingService.addUpdateDelayReason(data, actiontype, this.activatedRoute.snapshot.params.id).subscribe(() => {
      this.location.back();
      this.toastr.success(`Successfully ${actiontype === 'add' ? 'added' : 'updated'} delay reason`, 'Success');
    }, err => {
      console.log(err);
    });
  }

}
