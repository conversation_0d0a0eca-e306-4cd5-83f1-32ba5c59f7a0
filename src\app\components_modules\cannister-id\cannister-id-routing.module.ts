import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AddCannisterIdComponent } from './add-cannister-ids/add-cannister-id.component';
import { CannisterIdComponent } from './list-cannister-ids/cannister-id.component';


const routes: Routes = [
  {path: '', component: CannisterIdComponent},
  {path: 'add-cannister-id', component: AddCannisterIdComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CannisterIdRoutingModule { }
