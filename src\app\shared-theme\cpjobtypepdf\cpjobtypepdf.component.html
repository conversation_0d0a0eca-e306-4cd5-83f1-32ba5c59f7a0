<div class="container-table" id="pdf-download">
    <br>
    <div class="row">
        <div class="col-4">
            <img src="./assets/img/UEMS_Solutions_logo.jpg" alt="" style="height: 40px" />
        </div>
        <div class="col-4" style="text-align: center;">
            <h3> <strong>{{title}}</strong></h3>
        </div>
        <div class="col-4" *ngIf="logoUrl">
            <img [src]="logoUrl" alt="logo" style="max-height: 50px; float: right; margin-right: 20px;" />
        </div>
    </div>
    <table class="responstable" class="responstable" style="table-layout: fixed;">
        <caption>
        </caption>
        <thead>
            <tr>
                <th scope="colgroup" id="">Job Type</th>
                <th scope="colgroup" id="" *ngFor="let h of headers">
                    {{h}}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <th scope="colgroup" id="middele-header" colspan="14">
                    Urgent
                </th>
            </tr>
            <ng-container *ngIf="pdfData.length && pdfData[0]">
                <tr *ngFor="let data of pdfData[0]">
                    <th scope="colgroup" id="">
                        {{data.item}}
                    </th>
                    <td *ngFor="let r of rows">
                        {{data[r]}}
                    </td>
                </tr>
            </ng-container>
            <tr>
                <th scope="colgroup" id="middele-header" colspan="14">
                    Patient Move
                </th>
            </tr>
            <ng-container *ngIf="pdfData.length && pdfData[1]">
                <tr *ngFor="let data of pdfData[1]">
                    <th scope="colgroup" id="">
                        {{data.item}}
                    </th>
                    <td *ngFor="let r of rows">
                        {{data[r]}}
                    </td>
                </tr>
            </ng-container>
            <tr>
                <th scope="colgroup" id="middele-header" colspan="14">
                    Non Patient Move
                </th>
            </tr>
            <ng-container *ngIf="pdfData.length && pdfData[2]">
                <tr *ngFor="let data of pdfData[2]">
                    <th scope="colgroup" id="">
                        {{data.item}}
                    </th>
                    <td *ngFor="let r of rows">
                        {{data[r]}}
                    </td>
                </tr>
            </ng-container>
            <tr>
                <th scope="colgroup" id="middele-header" colspan="14">
                    Grand Total
                </th>
            </tr>
            <ng-container *ngIf="pdfData.length && pdfData[3]">
                <tr *ngFor="let data of pdfData[3]">
                    <th scope="colgroup" id="">
                        {{data.item}}
                    </th>
                    <td *ngFor="let r of rows">
                        {{data[r]}}
                    </td>
                </tr>
            </ng-container>
        </tbody>
    </table>
    <div *ngIf="pdfData.length === 0">
        No records to display.
    </div>
</div>