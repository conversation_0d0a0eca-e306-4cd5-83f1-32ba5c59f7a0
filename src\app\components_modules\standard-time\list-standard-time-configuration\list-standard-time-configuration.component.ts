import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { StandardTime } from 'src/app/models/standaedtime';
import { ToastrService } from 'ngx-toastr';
import Swal from 'sweetalert2';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';

@Component({
  selector: 'app-list-standard-time-configuration',
  templateUrl: './list-standard-time-configuration.component.html',
  styleUrls: ['./list-standard-time-configuration.component.scss', '../../../scss/table.scss']
})
export class ListStandardTimeConfigurationComponent implements OnInit {
  routes = [
    { path: '../stationReport', label: 'Station Porter Report'},
    { path: '../stationSpecificReport', label: 'Station Porter Specific Report'},
  ];
  pdfData = [];
  displayedColumns: string[] = ['std_code', 'start_time', 'end_time', 'total_time', 'edit', 'delete'];
  dataSource: MatTableDataSource<StandardTime>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly toaster: ToastrService,
    private readonly loader: LoaderService
  ) {
  }

  ngOnInit() {
    this.getStandardT();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('standardTimetable', 'standardTimetable_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('standardTimetable', 'standardTimetable_list');
    }
  }
  getStandardT() {
    this.facilityConfig.getStandardTime().subscribe(res => {
      this.pdfData = res.slice();
      this.pdfData = this.pdfData.filter(key => {
        const b = {
          'Std Time Code': key.std_code,
          'Start Time (Min)': key.start_time,
          'End Time (Min)': key.end_time,
          'Total Time (Min)': key.total_time
        };
        Object.assign(key, b);
        return key;
      });
      this.dataSource = new MatTableDataSource(res || []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deletestandardTime(id) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.facilityConfig.deleteStandardTime(id).subscribe(res => {
          this.toaster.success('Successfully deleted std time code', 'Success');
          this.getStandardT();
        }, err => {
          console.log(err);
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }
}
