import { Injectable } from "@angular/core";
import { HttpService } from "../httpService/http.service";
import { Observable, Subject, BehaviorSubject, of } from "rxjs";
import { environment } from "src/environments/environment";
import { map } from "rxjs/operators";
import { StorageService } from "../stoargeService/storage.service";
import * as _moment from "moment";

@Injectable({
  providedIn: "root",
})
export class UserService {
  menus = new Subject();
  menulist: any;
  isldap: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  logoUrl: BehaviorSubject<string> = new BehaviorSubject<string>("");

  constructor(
    private readonly httpService: HttpService,
    private readonly storageService: StorageService
  ) {}

  login(data): Observable<any> {
    return this.httpService.post(`${environment.base_url}api/login`, data);
  }

  logout(): Observable<any> {
    return this.httpService.post(`${environment.base_url}api/logout`);
  }

  menu(): Observable<any> {
    const userId = this.storageService.getData("user_id");
    return this.httpService
      .get(`${environment.base_url}api/menus/${atob(userId)}`)
      .pipe(
        map((res) => {
          this.menus.next(res);
          res.filter(data => {
            if (data.id == 16) {
              this.storageService.setData("view-status-sub-menus", JSON.stringify(data.submenu_list));
            }
          })
          return res;
        })
      );
  }

  currentDatetime(): Observable<any> {
    return of(_moment().format("YYYY-MM-DD HH:mm:ss"));
  }

  currentDateTimeInUTC(): Observable<any> {
    return of(_moment.utc(new Date().toUTCString()));
  }

  fullMenu(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/menus`);
  }

  smartAssignView() {
    return this.httpService.get(`${environment.base_url}api/smartassigns`);
  }

  smartAssign(data) {
    return this.httpService.put(
      `${environment.base_url}api/smartassign/edit`,
      data
    );
  }

  getRoles(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/roles`);
  }
  getSingleRole(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/roles/${id}`);
  }

  getUsersList(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/users`);
  }

  addUpadteRoleUserSTff(data, url, action): Observable<any> {
    if (action === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  getUserbyId(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/users/${id}`);
  }

  getPortersById(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/managestaffs/${id}`
    );
  }

  getManageStaff(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/managestaffs`);
  }

  getPorterLiveStatus(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/viewstatus/porterlivestatus`);
  }

  getManageStaffBasedOnlocation(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/managestaffs/location/${id}`
    );
  }

  getDashbOrdAnalytics(year, month, day): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/dashboard/kpi/${year}/${month}/${day}`
    );
  }

  getDashbOrdAnalyticsHourly(year, month, day): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/dashboard/hourly/${year}/${month}/${day}`
    );
  }

  downloadImages(id): Observable<any> {
    return this.httpService.getFiles(
      `${environment.base_url}api/download/images?filename=${id}`
    );
  }

  uploadImges(image): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/uploadimages`,
      image
    );
  }

  checkIsLDAP(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/ADStatus`);
  }

  checkUserName(name): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/users/checkname?username=${name}`
    );
  }

  /*=======*  removes empty keys which has null values =========*/
  getOnlyFilledObjects(val) {
    
    const mainData = Object.keys(val);
    let newData = {};
    mainData.forEach((key) => {
      if (val[key] || val[key] === false || val[key] === 0) {
        const value = {};
        value[key] = val[key];
        newData = Object.assign(newData, value);
      }
    });
    return newData;
  }

  getMessageStaff() {
    return this.httpService.get(`${environment.base_url}/api/messagestaff`);
  }

  // api is replica of @getMessageStaff
  getMessageStaffActive() {
    return this.httpService.get(
      `${environment.base_url}/api/messagestaff/staff`
    );
  }

  addMessageStaff(data) {
    return this.httpService.post(
      `${environment.base_url}/api/messagestaff/add`,
      data
    );
  }

  getDashboardData(url) {
    return this.httpService.get(`${environment.base_url + url}`);
  }

  getAds() {
    return this.httpService.get(`${environment.base_url}api/ADSettings`);
  }

  addUpdateAds(data) {
    return this.httpService.put(
      `${environment.base_url}api/ADSettings/edit`,
      data
    );
  }

  getViewStatusGrids() {
    return this.httpService.get(`${environment.base_url}api/gridview/grids`);
  }

  getViewStatusGridsByRole(roleId) {
    return this.httpService.get(
      `${environment.base_url}api/gridview/grids?roleId=${roleId}`
    );
  }

  getViewStatusGridsByUser(userId) {
    return this.httpService.get(
      `${environment.base_url}api/gridview/grids?userId=${userId}`
    );
  }

  updateViewStatusGrids(roleId, data) {
    return this.httpService.put(
      `${environment.base_url}api/gridview/configure/${roleId}`,
      data
    );
  }

  getDepartments() {
    return this.httpService.get(`${environment.base_url}api/departments`)
  }

  updateDepartmentById(id, data) {
    return this.httpService.put(`${environment.base_url}api/departments/edit/${id}`, data);
  }

  deleteDepartment(id): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/departments/delete/${id}`)
  }

  addUpdateDepartment(data) {
    return this.httpService.post(`${environment.base_url}api/departments/add`, data)
  }

  getjobTypes() {
    return this.httpService.get(`${environment.base_url}api/user/jobtypes`)
  }

  getLocations() {
    return this.httpService.get(`${environment.base_url}api/user/locations`)
  }

  getStationDepartments(){
    return this.httpService.get(`${environment.base_url}api/station/departments`);
  }
  getStationDepartmentById(id){
    return this.httpService.get(`${environment.base_url}api/station/departments/${id}`);
  }
  addStationDepartments(data){
    return this.httpService.post(`${environment.base_url}api/station/departments/add`, data);
  }
  updateStationDepartments(id, data){
    return this.httpService.put(`${environment.base_url}api/station/departments/${id}/edit`, data);
  }
  getCoSyDepartments(){
    return this.httpService.get(`${environment.base_url}api/CostCenter/departments`);
  }
  getCoSyDepartmentById(id){
    return this.httpService.get(`${environment.base_url}api/CostCenter/departments/${id}`);
  }
  addCoSyDepartments(data){
    return this.httpService.post(`${environment.base_url}api/CostCenter/departments/add`, data);
  }
  updateCoSyDepartments(id, data){
    return this.httpService.put(`${environment.base_url}api/CostCenter/departments/${id}/edit`, data);
  }
  getEnhancedLocationReports(data) {
    return this.httpService.post(`${environment.base_url}api/enhancedreports/location/search`, data);
  }

  checkLoginCodeExists(loginCode) {
    return this.httpService.get(`${environment.base_url}api/managestaff/checkLoginCodeExists/${loginCode}`);
  }
}
