table th {
  height: 0;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.72857;
  color: #000 !important;
  background-color: #ededed;
}

::ng-deep .mat-sort-header-button {
  font-weight: 600;
  font-size: 14px !important;
  line-height: 1.72857;
  color: #000 !important;
}

.btn .material-icons,
.btn:not(.btn-just-icon):not(.btn-fab) .fa {
  margin-right: 5px;
}

::ng-deep .mat-sort-header-container {
  display: flex;
}

::ng-deep .mat-sort-header-button {
  height: 21px;
  color: #16078a;
  font-size: 18px;
  line-height: 21px;
}

table tbody {
  font-size: 0.9em;
  letter-spacing: 0.03em;
}

table td {
  border: transparent;
  color: #17123b;
  margin: 10px 0px;
  font-size: 16px;
  height: 19px;
  font-weight: 400;
  text-align: left;
}

tbody tr:nth-child(odd) {
  background-color: rgb(230, 230, 230);
}

.card {
  // margin: 50px;
}

table {
  border-radius: 10px;
  width: 100%;
}

.mat-form-field {
  font-size: 14px;
  color: #7373a9;
  height: 28px;
}

::ng-deep .mat-form-field-appearance-legacy .mat-form-field-label {
  color: #7373a9;
}

::ng-deep .mat-form-field-appearance-legacy .mat-form-field-underline {
  color: #7373a9 !important;
}

.nav li {
  width: auto;
}

.mat-menu-panel {
  min-width: 85px !important;
  margin-top: 8px !important;
}

::ng-deep .mat-paginator-page-size-label {
  height: 19px;
  color: #170a3f;
  font-size: 16px;
  line-height: 19px;
}

::ng-deep .mat-paginator-range-actions {
  margin-top: 15px;
}

::ng-deep .mat-paginator-icon {
  height: 30px;
  width: 30px;
}

::ng-deep .mat-paginator-range-label {
  display: block;
  color: #170a3f;
}

td.mat-cell,
td.mat-footer-cell,
th.mat-header-cell {
  // padding-left: 15px;
}

.filter-margin {
  margin-bottom: 5px;
}

.border-remover {
  border-top: 0px !important;
}

/* ------------------------------------------------------------------
---------------------------------------------------------------------
---------------------------------------------------------------*/

table {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}

table caption {
  font-size: 1em;
  margin: 0.25em 0 0.5em;
}

table tr {
  background-color: #ffffff;
  border: 1px solid #ddd;
  padding: 0.45em;
}

table th,
table td {
  padding: 0.45em;
  // text-align: center;
  /* white-space: pre-wrap; */
}

table th {
  font-size: 14px;
  line-height: 1.72857;
  color: black;
  /* padding: 8px; */
  background-color: #ededed;
}

table tbody {
  font-size: 12px;
  /* letter-spacing: .03em; */
}

table td {
  // border: 0.7px solid black;
  word-break: break-all;
}

@media screen and (max-width: 600px) {
  table {
    border: 0;
  }

  table caption {
    font-size: 1.3em;
  }

  table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    font-weight: bold !important;
  }

  table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }

  table td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 0.8em;
    text-align: right;
    padding: 2px;
  }

  table td::before {
    content: attr(data-label);
    float: left;
    font-weight: bold;
    text-transform: uppercase;
  }

  table td:last-child {
    border-bottom: 0;
  }

  .perfect-scrollbar-on .sidebar {
    display: none !important;
  }
}

/* tbody tr:nth-child(odd) {
          background-color: rgb(230, 230, 230);
      } */

.table-responsive {
  display: inline-table !important;
}

.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
}

legend.scheduler-border {
  font-size: 0.7em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}

.pull-right {
  float: right;
  margin-left: 0.3em;
}

.pop__over__css {
  background-color: #ececec;
  height: 44px;
  width: auto;
}

.btn__css__custom {
  font-size: 10px;
  padding: 8px 9px !important;
}

.canister__class {
  cursor: pointer;
}

.canister__class:hover {
  text-decoration: underline;
  color: blue;
}

// hover css
.pts__view {
  text-align: right;
  cursor: pointer;
}

.label__form {
  font-size: 14px;
  p {
    margin: 0;
  }
}
// .example-full-width {
//   font-size: 12px;
// }

.view_task {
  font-size: 14px;
}
::ng-deep .mat-checkbox-label {
  font-size: 12px !important;
}

.custom__gutter {
  .col-2,
  .col-1 {
    padding: 0 0.5%;
  }
}
.custom__gutter_button {
  .col {
    padding: 0 0.5%;
  }
}
.canister_pts {
  cursor: pointer;
  float: left;
  margin-left: 2px;
}

.button__pts_view {
  width: 166px;
}

::ng-deep .mat-form-field-label {
  color: #000000 !important;
  font-weight: 600 !important;
}

td {
  font-size: 13px !important;
}

::ng-deep .mat-elevation-z8 {
  box-shadow: none !important;
}

@import "~@angular/material/theming";
$custom-typography: mat-typography-config(
  $font-family: '"Verdana",sans-serif',
);

@include angular-material-typography($custom-typography);

td:nth-child(1) {
  border: 1px solid #e0e0e0 !important;
}
table {
  border: 1px;
}
td,
th {
  border-right: 1px solid #e0e0e0 !important;
}

td {
  vertical-align: top;
}
