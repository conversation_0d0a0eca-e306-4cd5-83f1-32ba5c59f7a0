import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RolesRoutingModule } from './roles-routing.module';
import { ListRolesComponent } from './list-roles/list-roles.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateRolesComponent } from './add-update-roles/add-update-roles.component';


@NgModule({
  declarations: [ListRolesComponent, AddUpdateRolesComponent],
  imports: [
    CommonModule,
    RolesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class RolesModule { }
