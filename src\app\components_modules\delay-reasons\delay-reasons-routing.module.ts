import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListDelayReasonsComponent } from './list-delay-reasons/list-delay-reasons.component';
import { AddUpdateDelayReasonsComponent } from './add-update-delay-reasons/add-update-delay-reasons.component';


const routes: Routes = [
  {path: '', component: ListDelayReasonsComponent},
  {path: 'adddelayreason', component: AddUpdateDelayReasonsComponent},
  {path: 'updatedelayreason/:id', component: AddUpdateDelayReasonsComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DelayReasonsRoutingModule { }
