import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DelayReasonsRoutingModule } from './delay-reasons-routing.module';
import { ListDelayReasonsComponent } from './list-delay-reasons/list-delay-reasons.component';
import { AddUpdateDelayReasonsComponent } from './add-update-delay-reasons/add-update-delay-reasons.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';


@NgModule({
  declarations: [ListDelayReasonsComponent, AddUpdateDelayReasonsComponent],
  imports: [
    CommonModule,
    DelayReasonsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule
  ]
})
export class DelayReasonsModule { }
