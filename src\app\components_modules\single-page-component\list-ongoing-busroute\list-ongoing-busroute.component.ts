import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import {
  Component,
  OnInit,
  ViewChild,
  OnDestroy,
  ViewChildren,
  QueryList,
} from "@angular/core";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  DateAdapter,
  MAT_DATE_FORMATS,
  NativeDateAdapter,
  MatSelect,
  MatOption,
} from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { OnGoingBusRoutes } from "src/app/models/onGoingBusRoutes";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import * as moment from "moment";
import * as _moment from "moment";
import { ToastrService } from "ngx-toastr";
declare var $: any;
export const MY_FORMATS = {
  parse: {
    dateInput: "LL",
  },
  display: {
    dateInput: "YYYY-MM-DD",
    monthYearLabel: "YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "YYYY",
  },
};
export class AppDateAdapterTime extends NativeDateAdapter {
  format(date: Date, displayFormat: Object): string {
    if (displayFormat === "input") {
      let day: string = date.getDate().toString();
      day = +day < 10 ? "0" + day : day;
      let month: string = (date.getMonth() + 1).toString();
      month = +month < 10 ? "0" + month : month;
      let year = date.getFullYear();
      return `${day}-${month}-${year}`;
    }

    return moment(date).format("DD/MM/YYYY HH:mm");
  }
}
@Component({
  selector: "app-list-ongoing-busroute",
  templateUrl: "./list-ongoing-busroute.component.html",
  styleUrls: [
    "./list-ongoing-busroute.component.scss",
    "../../../scss/table.scss",
  ],
  providers: [
    {
      provide: DateAdapter,
      useClass: AppDateAdapterTime,
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class ListOngoingBusrouteComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = [
    "route_no",
    "route_name",
    "created_date",
    "start_time",
    "actual_start_time",
    "end_time",
    "location_count",
    "cancel_time",
    "resource1",
    "resource2",
    "remarks",
  ];
  dataSource: MatTableDataSource<OnGoingBusRoutes>;
  timeAndStaffAction: FormGroup;
  staff = [];
  pdfData = [];
  dateVal: any;
  presentRouteId: any;
  adHocdisabled: any;
  adoCDate = new Date();
  actionType: any;
  locationNames: any = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatOption, { static: true }) allSelected: MatOption;
  @ViewChild(MatSelect, { static: true }) select: MatSelect;
  dateOfJoiningDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "Start Date",
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    disabledTimePicker: true,
  };
  staffList = [];
  rowData: any;
  methodOfStartEndBusRoute: any;
  onGoingLocationForm: FormGroup;
  triggerTimeValidation: boolean;
  startTimeShow: any;
  departmentList = []  // {'departmentName': 'All', 'departmentId': 'All'}
  selectedDepartments = ['All']
  departmentsIds = [];
  isFirstLoad = true;
  @ViewChildren("myPickerRef") NgxTimepicker: QueryList<any>;
  constructor(
    private readonly userService: UserService,
    private readonly loader: LoaderService,
    private readonly report: ReportsService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) {
    this.DurationForm();
  }
  DurationForm() {
    this.timeAndStaffAction = this.fb.group({
      date: [{ value: "", disabled: true }, Validators.required],
      time: [{ value: "", disabled: true }, Validators.required],
      staff_id: ["", Validators.required],
      location: this.fb.array([]),
      actualDate: [""],
    });
  }

  createEndLocationForm() {
    this.onGoingLocationForm = this.fb.group({
      time: [""],
      location_id: ["", Validators.required],
      end: [false],
      start_loc: [""],
      end_loc: [""],
    });

    return this.onGoingLocationForm;
  }

  ngOnInit() {
    this.getDepartments();
    this.getStaff();
    // this.getOngoingBus();
    this.getCurrentDateTime();
  }

  ngAfterViewChecked() {
    if (this.select && this.select.options && this.select.options.length > 1 && this.isFirstLoad) {
      this.select.options.forEach((item: MatOption) => item.select());
    }
  }

  tosslePerOne(all){ 
    if (this.allSelected.selected) {  
     this.allSelected.deselect();
     return false;
    }
   if(this.selectedDepartments.length==this.departmentList.length) {
    this.allSelected.select();
   }
  }
   toggleAllSelection() {
    this.isFirstLoad = false;
     if (this.allSelected.selected) {
      this.departmentList.map(item => {
        this.selectedDepartments[0] = 'All';
        this.selectedDepartments.push(item['departmentName']);
        this.select.options.forEach((item: MatOption) => item.select());
      })
     } else {
       this.selectedDepartments= [];
     }
   }

  setDate(rowData, method) {
    this.DurationForm();
    this.triggerTimeValidation = false;
    this.rowData = rowData.location_list;

    const formLocation = this.timeAndStaffAction.get("location") as FormArray;

    this.rowData.forEach((data, i) => {
      formLocation.push(this.createEndLocationForm());
      formLocation.controls[i].patchValue({
        start_loc: data.location_name,
        end_loc: data.location_name,
        time: data.arrival_time
          ? moment(data.arrival_time).format("hh:mm a")
          : "",
        location_id: data.location_id,
      });
    });
    setTimeout(() => {
      this.NgxTimepicker.toArray().forEach((data, i) => {
        if (data.defaultTime == null) {
          data.hour = undefined;
          data.minute = undefined;
        }
        $(document).ready(function () {
          $(`#picker${i}`).timepicker({ timeFormat: "h:i a", interval: 10 });
        });

        $(`#picker${i}`).on("change paste keyup", function () {
          formLocation.controls[i].patchValue({
            time: $(this).val(),
          });
        });
      });
    }, 200);

    // this to check if the location is last and make the value end as true
    formLocation.controls[this.rowData.length - 1]
      .get("time")
      .setValidators(Validators.required);

    formLocation.controls[this.rowData.length - 1].get("end").patchValue(true);

    this.methodOfStartEndBusRoute = method == "start" ? false : true;

    // select location then bind time
    const date =
      rowData.location_list &&
      rowData.location_list.find((data) => data.arrival_time);

    const dateTime = date
      ? new Date(date.arrival_time)
      : rowData["Start Time"] !== "--"
      ? new Date(rowData["Start Time"])
      : new Date();
    this.timeAndStaffAction.get("date").setValue(dateTime);
    this.timeAndStaffAction
      .get("time")
      .setValue(moment(dateTime).format("hh:mm a"));
    this.startTimeShow = this.rowData["Start Time"]
      ? this.rowData["Start Time"]
      : new Date();
    if (method == "start") {
      this.timeAndStaffAction.controls["date"].disable();
      this.timeAndStaffAction.controls["time"].disable();
      this.dateOfJoiningDate.disabledTimePicker = true;
    } else {
      this.dateOfJoiningDate.disabledTimePicker = false;
      this.timeAndStaffAction.controls["date"].enable();
      this.timeAndStaffAction.controls["time"].enable();
    }
  }

  getCurrentDateTime() {
    this.userService.currentDateTimeInUTC().subscribe((res) => {
      // this.adoCDate = res;
    });
  }

  getOngoingBus() {
    const data = {
      'departments': this.selectedDepartments.includes('All') ? this.departmentsIds : this.selectedDepartments
    }
    this.report.getOngoingBusRoutes(data).subscribe((res) => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  getDepartments() {
    this.facilityConfig.getUserDepartments().subscribe((res) => {
      if (res){
        res.filter(data => {
          this.departmentList.push(data);
          this.departmentsIds.push(data.departmentId)
        })
        this.getOngoingBus();
      }
    });
  }

  getStaff() {
    this.userService.getMessageStaffActive().subscribe((res) => {
      this.staff = res;
    });
  }
  applyFilter(filterValue: string, type?) {
    if (type) {
      // this.selectedDepartments = ['All'];
      this.select.options.forEach((item: MatOption) => item.select());
    }
    this.getOngoingBus();
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "onGoingBusRouteTable",
        "onGoingBus_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("onGoingBusRouteTable", "onGoingBus_list");
    }
  }
  getDateTime() {
    if (
      this.timeAndStaffAction.get("time").value &&
      this.timeAndStaffAction.get("date").value
    ) {
      const time = this.timeAndStaffAction.get("time").value;
      const formattedDate = moment(
        this.adHocdisabled === "Ad-hoc"
          ? this.adoCDate
          : this.timeAndStaffAction.get("date").value
      ).format("YYYY-MM-DD");
      const isEndsWithPm = time.endsWith("pm");
      this.dateVal =
        formattedDate +
        "T" +
        (isEndsWithPm
          ? String(
              Number(time.slice(0, 2) === "12" ? 0 : time.slice(0, 2)) + 12
            )
          : time.slice(0, 2) === "12"
          ? "00"
          : time.slice(0, 2)) +
        ":" +
        time.slice(3, 5) +
        ":00";

      this.timeAndStaffAction.get("actualDate").setValue(this.dateVal);
    }
  }

  takeAction(action?, id?) {
    // this.actionType = action;

    const a = {
      time: _moment(this.adoCDate).format("YYYY/MM/DD HH:mm:ss"),
      staff_id: this.timeAndStaffAction.get("staff_id").value,
    };

    if (action === "cancel") {
      this.report.assignreassignBusRoute(a, id, "cancel").subscribe(
        () => {
          this.actionSuccessEvent("cancelled");
        },
        (err) => {
          console.log(err);
        }
      );
    } else if (this.actionType === "assign" || this.actionType === "reassign") {
      if (this.timeAndStaffAction.get("staff_id").valid) {
        this.report
          .assignreassignBusRoute(a, this.presentRouteId, this.actionType)
          .subscribe(
            () => {
              this.actionSuccessEvent(`${this.actionType}ed`);
              this.actionType = "";
            },
            (err) => {
              console.log(err);
            }
          );
      } else {
        this.timeAndStaffAction.get("staff_id").markAsTouched();
        this.toastr.warning(
          "Please enter all highlighted fields",
          "Validation failed!"
        );
      }
    } else if (this.actionType === "end" || this.actionType === "start") {
      const formLocation = this.timeAndStaffAction.get("location") as FormArray;

      if (this.actionType === "end") {
        if (formLocation.valid) {
          const jsonEnd = formLocation.value.reduce(
            (finalJson, everyTime) => [
              ...finalJson,
              {
                time: everyTime.time
                  ? moment(
                      moment(this.timeAndStaffAction.get("date").value).format(
                        "YYYY/MM/DD"
                      ) +
                        " " +
                        everyTime.time
                    ).format("YYYY/MM/DD HH:mm:ss")
                  : null,
                location_id: everyTime.location_id,
                end: everyTime.end,
              },
            ],
            []
          );

          this.report
            .assignReassignBusRouteLocation(
              { locations: jsonEnd },
              this.presentRouteId,
              this.actionType,
              this.timeAndStaffAction.controls["location"].value
            )
            .subscribe(
              (res) => {
                this.actionSuccessEvent(`${this.actionType}ed`);
                this.actionType = "";
              },
              (err) => {
                this.toastr.error(err.Message);
              }
            );
        } else {
          this.toastr.warning("End location time is required");
          this.triggerTimeValidation = true;
        }
      } else if (
        this.timeAndStaffAction.get("date").value &&
        this.timeAndStaffAction.get("time").value
      ) {
        a.staff_id = "";
        a.time = _moment(this.timeAndStaffAction.get("date").value).format(
          "YYYY/MM/DD HH:mm:ss"
        );
        this.report
          .assignreassignBusRoute(a, this.presentRouteId, this.actionType)
          .subscribe(
            (res) => {
              this.actionSuccessEvent(`${this.actionType}ed`);
              this.actionType = "";
            },
            (err) => {
              console.log(err);
            }
          );
      } else {
        this.timeAndStaffAction.get("date").markAsTouched();
        this.timeAndStaffAction.get("time").markAsTouched();
        this.toastr.warning(
          "Please enter all highlighted fields",
          "Validation failed!"
        );
      }
    }
  }

  actionSuccessEvent(action) {
    this.timeAndStaffAction.reset();
    this.toastr.success(`Successfully ${action}ed bus route`, "Success");
    this.getOngoingBus();
    this.getStaff();
    this.adHocdisabled = "";
    $("#resourceModal").modal("hide");
    $("#StartTimeModal").modal("hide");
  }

  resetdata() {
    this.timeAndStaffAction.reset();
    this.presentRouteId = "";
    this.actionType = "";
    this.adHocdisabled = "";
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Route No": key.route_no ? key.route_no : "--",
              "Route Name": key.route_name ? key.route_name : "--",
              "Creation Time": key.created_date ? key.created_date : "--",
              "Start Loc": key.start_location_name
                ? key.start_location_name
                : "--",
              "End Loc": key.end_location_name ? key.end_location_name : "--",
              "No of Locs": key.location_count ? key.location_count : "",
              "Sch. Start Time": key.start_time ? key.start_time : "--",
              "Assigned Time": key.assign_time ? key.assign_time : "--",
              "Start Time": key.actual_start_time
                ? key.actual_start_time
                : "--",
              "Comp Time": key.end_time ? key.end_time : "--",
              "Cancel Time": key.cancel_time ? key.cancel_time : "--",
              "Resource 1": key.resource1 ? key.resource1 : "--",
              "Resource 2": key.resource2 ? key.resource2 : "--",
              Remarks: key.remarks ? key.remarks : "--",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  locationDisplay(locations, is_sequential) {
    if (!is_sequential) {
      this.locationNames = [...locations];
    } else {
      this.locationNames = [];
      locations.forEach((loc) => {
        this.locationNames.splice(loc.order, 0, loc);
      });
    }
  }

  getNewStaffList(row) {
    // this.staffList = row['Resource 2'] ?
    //   this.staff.filter(staffData => staffData['Resource 2'] != row['Resource 2']) :
    //   row['Resource 1'] ? this.staff.filter(resourceTwo => resourceTwo['Resource 1'] != row['Resource 1']) :
    //     this.staff.filter(resourceTwo => resourceTwo['Resource 2'] != row['Resource 2']).filter(resourceTwoList => resourceTwoList['Resource 1'] != row['Resource 1'])
  }

  ngOnDestroy() {
    $("#resourceModal").modal("hide");
    $("#StartTimeModal").modal("hide");
    $("#exampleModal").modal("hide");
  }
}
