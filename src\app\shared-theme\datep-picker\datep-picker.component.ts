import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ValidationService } from "src/app/Apiservices/validation/validation.service";
import {
  DateAdapter,
  MatDateFormats,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  NativeDateAdapter,
} from "@angular/material/core";
import * as moment from "moment";
import { ActivatedRoute } from "@angular/router";
export const MY_FORMATS = {
  parse: {
    dateInput: "LL",
  },
  display: {
    dateInput: "YYYY-MM-DD",
    monthYearLabel: "YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "YYYY",
  },
};

export class AppDateAdapterDate extends NativeDateAdapter {
  format(date: Date, displayFormat: Object): string {
    if (displayFormat === "input") {
      let day: string = date.getDate().toString();
      day = +day < 10 ? "0" + day : day;
      let month: string = (date.getMonth() + 1).toString();
      month = +month < 10 ? "0" + month : month;
      let year = date.getFullYear();
      return `${day}-${month}-${year}`;
    }

    return moment(date).format("DD/MM/YYYY ");
  }
}

@Component({
  selector: "app-datep-picker",
  templateUrl: "./datep-picker.component.html",
  styleUrls: ["./datep-picker.component.scss"],
  providers: [
    {
      provide: DateAdapter,
      useClass: AppDateAdapterDate,
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class DatepPickerComponent implements OnInit {
  @Input() dateConfiguration = {
    rangePicker: false,
    collumns: "col-12",
    label: "",
    required: true,
    minDateStart: "",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    disabledTimePicker: false,
    readonly: false,
  };

  @Input() control: FormControl;
  @Input() fieldName: string;
  @Input() fieldType: string;
  @Input() completeError: string;
  @Output() getDate = new EventEmitter<any>();

  constructor(
    private readonly validationService: ValidationService,
    private readonly activateRoute: ActivatedRoute
  ) {}

  ngOnInit() {}

  datePatch(date) {
    this.getDate.emit(date);
  }

  get errorMessage() {
    for (const propertyName in this.control && this.control.errors) {
      if (
        (this.control && this.control.errors).hasOwnProperty(propertyName) &&
        this.control.touched
      ) {
        return this.validationService.getValidatorErrorMessage(
          propertyName,
          this.fieldType,
          this.fieldName,
          (this.control && this.control.errors)[propertyName],
          this.completeError
        );
      }
    }
    return null;
  }
}
