import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LocationsRoutingModule } from './locations-routing.module';
import { ListLocationComponent } from './list-location/list-location.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateLocationComponent } from './add-update-location/add-update-location.component';




@NgModule({
  declarations: [ListLocationComponent, AddUpdateLocationComponent],
  imports: [
    CommonModule,
    LocationsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class LocationsModule { }
