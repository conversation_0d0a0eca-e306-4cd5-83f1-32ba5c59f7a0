<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <!-- <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p>Add Message Staff</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> -->
        <div class="card-body">
          <div class="tab-content">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form
                    [formGroup]="messageStaffForm"
                    #formMessageStaff="ngForm"
                  >
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-4">
                          <mat-label class="font_bolder"
                            >Staff Name
                            <span class="error-css"
                              ><span class="error-css">*</span></span
                            >
                          </mat-label>
                          <mat-form-field>
                            <input
                              matInput
                              placeholder="Search staff..."
                              (keyup)="filterStaffList($event.target.value)"
                            />
                          </mat-form-field>
                          <main class="common__height">
                            <div
                              class="list-group mobile__number__list text-left"
                            >
                              <button
                                type="button"
                                class="list-group-item list-group-item-action border"
                                *ngFor="let val of filteredStaff; let i = index"
                                (click)="setActiveStaffName(i, val)"
                                [ngClass]="{
                                  active:
                                    activeIndexStaff &&
                                    activeIndexStaff.includes(val.staff_id)
                                }"
                              >
                                {{ val.staff_name }}
                              </button>
                            </div>
                          </main>
                          <mat-error class="pull-left error-css">
                            <app-error-message
                              [control]="messageStaffForm.controls.staff_list"
                              [fieldName]="'staff list'"
                              [fieldType]="'enter'"
                            >
                            </app-error-message>
                          </mat-error>
                        </div>

                        <div class="col-3">
                          <mat-label class="font_bolder"
                            >Canned Message
                            <span class="error-css"
                              ><span class="error-css">*</span></span
                            >
                          </mat-label>
                          <main class="common__height">
                            <div
                              class="list-group mobile__number__list text-left"
                            >
                              <button
                                type="button"
                                class="list-group-item list-group-item-action border"
                                *ngFor="let val of messages; let i = index"
                                [value]="val"
                                (click)="
                                  messageSelected(i, val.message_template)
                                "
                                [ngClass]="{ active: activeIndexMessage == i }"
                              >
                                {{ val.message_template }}
                              </button>
                            </div>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="
                                  messageStaffForm.controls.canned_message
                                "
                                [fieldName]="'Canned Message'"
                                [fieldType]="'enter'"
                              >
                              </app-error-message>
                            </mat-error>
                          </main>
                        </div>
                        <div class="col-2">
                          <main class="common__height">
                            <mat-form-field>
                              <mat-label class="font_bolder">Phone </mat-label>
                              <textarea
                                matInput
                                placeholder="staff mobile"
                                formControlName="staff_mobile"
                                rows="10"
                              ></textarea>
                              <!-- <mat-error class="pull-left error-css">
                                                                <app-error-message
                                                                    [control]="messageStaffForm.controls.staff_mobile"
                                                                    [fieldName]="'mobile no'" [fieldType]="'enter'">
                                                                </app-error-message>
                                                            </mat-error> -->
                            </mat-form-field>
                          </main>
                        </div>
                        <div class="col-3">
                          <main class="common__height">
                            <mat-form-field>
                              <mat-label class="font_bolder"
                                >Message
                                <span class="error-css"
                                  ><span class="error-css">*</span></span
                                >
                              </mat-label>
                              <textarea
                                matInput
                                placeholder="message"
                                maxlength="300"
                                formControlName="message"
                                rows="10"
                              ></textarea>
                              <mat-hint style="text-align: end">
                                {{
                                  this.messageStaffForm.controls["message"]
                                    .value.length
                                }}
                                / 300
                              </mat-hint>
                              <mat-error class="pull-left error-css">
                                <app-error-message
                                  [control]="messageStaffForm.controls.message"
                                  [fieldName]="'Message'"
                                  [fieldType]="'enter'"
                                >
                                </app-error-message>
                              </mat-error>
                            </mat-form-field>
                          </main>
                        </div>
                      </div>
                    </fieldset>
                    <div class="row from-submit">
                      <div class="col-12">
                        <button
                          mat-raised-button
                          class="btn btn-primary pull-right float-right"
                          (click)="saveMessageStaff()"
                        >
                          Submit
                        </button>
                        <button
                          mat-raised-button
                          (click)="resetFormMessage()"
                          class="btn btn-white pull-right float-right"
                        >
                          Reset
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
