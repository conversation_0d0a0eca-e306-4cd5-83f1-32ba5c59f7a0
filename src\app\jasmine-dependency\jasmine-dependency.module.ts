import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../shared/shared.module';
import { HttpClientModule } from '@angular/common/http';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { RouterTestingModule } from '@angular/router/testing';
import { MatNativeDateModule, MatDatepickerModule, MatInputModule, MatTableModule, MatMenuModule } from '@angular/material';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ObserversModule } from '@angular/cdk/observers';
import { CookieService } from 'ngx-cookie-service';
import { JwtHelperService, JwtModule } from '@auth0/angular-jwt';
import { CdkTableModule } from '@angular/cdk/table';



@NgModule({
  declarations: [],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    HttpClientModule,
    ToastrModule.forRoot(),
    RouterTestingModule,
    MatDatepickerModule,
    MatNativeDateModule,
    BrowserAnimationsModule,
    MatTableModule,
    CdkTableModule,
    ObserversModule,
    MatMenuModule,
    MatInputModule,
    JwtModule.forRoot({}),
  ],
  providers: [
    ToastrService,
    CookieService,
    JwtHelperService,
  ],
  exports: [
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    HttpClientModule,
    ToastrModule,
    MatMenuModule,
    RouterTestingModule,
    MatTableModule,
    JwtModule,
    CdkTableModule,
    MatDatepickerModule,
    MatNativeDateModule,
    BrowserAnimationsModule,
    ObserversModule,
    MatInputModule
  ],
})
export class JasmineDependencyModule { }
