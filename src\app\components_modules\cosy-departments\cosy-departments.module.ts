import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ListCosyDepartmentsComponent } from './list-cosy-departments/list-cosy-departments.component';
import { AddUpdateCosyDepartmentsComponent } from './add-update-cosy-departments/add-update-cosy-departments.component';
import { CosyDepartmentsRoutingModule } from './cosy-departments-routing.module';



@NgModule({
  declarations: [ListCosyDepartmentsComponent, AddUpdateCosyDepartmentsComponent],
  imports: [
    CommonModule,
    CosyDepartmentsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    NgxMatSelectSearchModule
  ]
})
export class CosyDepartmentsModule { }
