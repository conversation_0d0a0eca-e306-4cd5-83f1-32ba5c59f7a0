<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li>
                                    <p>
                                        Total Cancellation
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12">
                                    <app-searchfilter></app-searchfilter>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="totalcancellation_table" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="item">
                                                <th id="" mat-header-cell *matHeaderCellDef>
                                                    Cancellation
                                                </th>
                                                <td mat-cell *matCellDef="let row">{{row.item}}</td>
                                            </ng-container>

                                            <ng-container matColumnDef="ytd">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> YTD AVG-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.ytd}}</td>
                                            </ng-container>

                                            <ng-container matColumnDef="jan">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Jan-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.jan}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="feb">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Feb-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.feb}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="mar">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Mar-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.mar}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="apr">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Apr-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.apr}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="may">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> May-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.may}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="jun">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Jun-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.jun}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="jul">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Jul-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.jul}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="aug">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Aug-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.aug}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="sep">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Sep-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.sep}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="oct">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Oct-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.oct}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="nov">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Nov-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.nov}}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="dec">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Dec-
                                                    {{(dates && dates.year) ? dates.year : currYear}}
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.dec}}</td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                                <br>
                                <div class="col-md-12 chart-card">
                                    <div class="chart-wrapper">
                                        <canvas *ngIf="chartsGraph" baseChart height="100px" width="300px"
                                            [datasets]="barChartData" [labels]="['Cancellation Type']"
                                            [options]="barChartOptions" [plugins]="barChartPlugins"
                                            [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf
    [heads]="['Cancellation', 'Ytd Avg', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']"
    [title]="'Total Cancellation report for ' + ((dates && dates.year) ? dates.year : currYear)" [datas]="pdfData">
</app-table-for-pdf>