import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import * as moment from 'moment';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-searchfilter',
  templateUrl: './searchfilter.component.html',
  styleUrls: ['./searchfilter.component.scss']
})
export class SearchfilterComponent implements OnInit {

  kpiForm: FormGroup;
  months = [{ id: '01', val: 'January' },
  { id: '02', val: 'February' },
  { id: '03', val: 'March' },
  { id: '04', val: 'April' },
  { id: '05', val: 'May' },
  { id: '06', val: 'June' },
  { id: '07', val: 'July' },
  { id: '08', val: 'August' },
  { id: '09', val: 'September' },
  { id: '10', val: 'October' },
  { id: '11', val: 'November' },
  { id: '12', val: 'December' },
  ];
  today = new Date();
  currentYear = this.today.getFullYear();
  years = [this.currentYear, this.currentYear - 1];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');

  constructor(
    private readonly fb: FormBuilder,
    private readonly reportService: ReportsService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }

  createForm() {
    this.kpiForm = this.fb.group({
      month: [this.currMonth],
      year: [Number(this.currYear)]
    });
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }

  getCurrentDateTime() {
    this.reportService.searchDates.subscribe(res1 => {
      if (res1.month) {
        this.kpiForm.patchValue(res1);
      } else {
        this.userService.currentDatetime().subscribe(res => {
          this.today = res;
          this.currentYear = new Date(this.today).getFullYear();
          this.years = [this.currentYear, this.currentYear - 1];
          this.currMonth = moment(this.today).format('MM');
          this.currYear = moment(this.today).format('YYYY');
          this.createForm();
          this.reportService.searchDates.next(this.kpiForm.value);
        });
      }
    });
  }

  resetClicked() {
    this.userService.currentDatetime().subscribe(res => {
      this.today = res;
      this.currentYear = new Date(this.today).getFullYear();
      this.years = [this.currentYear, this.currentYear - 1];
      this.currMonth = moment(this.today).format('MM');
      this.currYear = moment(this.today).format('YYYY');
      this.createForm();
      this.kpiForm.reset();
      this.kpiForm.patchValue({ month: this.currMonth, year: Number(this.currYear) });
      this.reportService.searchDates.next(this.kpiForm.value);
    });
  }

  selectedDateValue() {
    this.reportService.searchDates.next(this.kpiForm.value);
  }

}
