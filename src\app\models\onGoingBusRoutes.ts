export interface OnGoingBusRoutes {
      route_id: number;
      route_no: string;
      route_name: string;
      route_type: string;
      created_date: string;
      start_location: string;
      end_location: string;
      start_location_name: string;
      end_location_name: string;
      location_count: string;
      location_list: string;
      start_time: string;
      assign_time: string;
      actual_start_time: string;
      end_time: string;
      cancel_time: string;
      resource1: string;
      resource2: string;
      remarks: string;
      requestor: string;
   }
