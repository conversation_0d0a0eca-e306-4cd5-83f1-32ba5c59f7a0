import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';

@Component({
  selector: 'app-add-update-category-mode-transport',
  templateUrl: './add-update-category-mode-transport.component.html',
  styleUrls: ['./add-update-category-mode-transport.component.scss']
})
export class AddUpdateCategoryModeTransportComponent implements OnInit {

  mainJobCategories: any = [];
  subJobCategories: any = [];
  modesOfTransport: any = [];
  categoryMapingForm: FormGroup;


  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly masterDataService: MasterDataService
  ) {
    this.categoryMapingForm = this.fb.group({
      transport_mode_ids: [[], Validators.required],
      mj_category_id: ['', Validators.required],
      sj_category_id: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.getMianJobCategory();
    this.getLevelsById();
    this.getModesOfTransport();
  }

  getModesOfTransport() {
    this.masterDataService.getModeOfTransports().subscribe(res => {
      res = res && res.length && res.filter(val => {
        if (val.status) {
          return val;
        }
      });
      this.modesOfTransport = res || [];
    });
  }

  getMianJobCategory() {
    this.facilityConfig.getMianJobCategory().subscribe(res => {
      res = res && res.length && res.filter(values => {
        if (values.status) {
          return values;
        }
      });
      this.mainJobCategories = res || [];
    });
  }

  getsubJobCategory(id) {
    this.categoryMapingForm.get('sj_category_id').reset();
    if (id) {
      this.facilityConfig.getSubJobCategoryMainJobBased(id).subscribe(res => {
        res = res && res.length && res.filter(vals => {
          if (vals.status) {
            return vals;
          }
        });
        this.subJobCategories = res || [];
      });
    }
  }

  getLevelsById() {
    if (this.activatedRoute.snapshot.params.id && this.activatedRoute.snapshot.params.id2) {
      this.categoryMapingForm.get('mj_category_id').disable();
      this.categoryMapingForm.get('sj_category_id').disable();
      this.facilityConfig.getCategoryById(this.activatedRoute.snapshot.params.id, this.activatedRoute.snapshot.params.id2).
        subscribe(res => {
          const transportModes = res.transport_mode_list && res.transport_mode_list.map(a => a.transport_mode_id) || [];
          this.categoryMapingForm.patchValue(res);
          this.categoryMapingForm.get('transport_mode_ids').setValue(transportModes);
        });
    }
  }

  save(actiontype) {
    if (this.categoryMapingForm.valid) {
      this.facilityConfig.addUpadteCategoryTransport(this.categoryMapingForm.getRawValue(), actiontype)
        .subscribe(() => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} category`, 'Success');
        }, err => {
          console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.categoryMapingForm.markAllAsTouched();
    }
  }

}
