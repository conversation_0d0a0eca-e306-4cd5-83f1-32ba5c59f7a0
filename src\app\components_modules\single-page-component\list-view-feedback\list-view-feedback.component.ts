import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { FeedBackList } from "src/app/models/feedBackList";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { ToastrService } from "ngx-toastr";
import * as moment from "moment";

@Component({
  selector: "app-list-view-feedback",
  templateUrl: "./list-view-feedback.component.html",
  styleUrls: [
    "./list-view-feedback.component.scss",
    "../../../scss/table.scss",
  ],
})
export class ListViewFeedbackComponent implements OnInit {
  displayedColumns: string[] = [
    "requestor_name",
    "contact_no",
    "location",
    "time",
    "feedback",
    "FeedBack_id",
  ];
  dataSource: MatTableDataSource<FeedBackList>;
  pdfData = [];
  viewData: any;
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  feedbckForm: FormGroup;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  constructor(
    private readonly loader: LoaderService,
    private readonly report: ReportsService,
    private readonly fb: FormBuilder,
    private readonly userService: UserService,
    private readonly toastr: ToastrService
  ) {
    this.createForm();
  }

  createForm() {
    this.feedbckForm = this.fb.group({
      from_date: [this.month, Validators.required],
      to_date: [this.today, Validators.required],
    });
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.searchFeedBack(this.month, this.today);
    });
  }

  searchFeedBack(from, to) {
    if (this.feedbckForm.valid) {
      const data = {
        from_date: from,
        to_date: to,
      };
      this.report.getFeedBackReport(data).subscribe(
        (res) => {
          res =
            res &&
            res.filter((val) => {
              const t = val.time.match("T");
              if (t) {
                val.time = val.time.replace(t, "  ");
              }
              return val;
            });
          this.dataSource = new MatTableDataSource(res ? res : []);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          this.setPageSizeOptions();
        },
        (err) => {
          this.toastr.warning("Search Failed", "failed!");
        }
      );
    }
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "feedBackTable",
        "feedBackTable_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("feedBackTable", "feedBackTable_list");
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Requestor Name": key.requestor_name,
              "Route Name": key.contact_no,
              Location: key.location,
              Time: key.time,
              FeedBack: key.feedback,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  ViewFeedBack(data) {
    this.viewData = data;
  }
  getDate() {
    if (
      this.feedbckForm.get("from_date").value &&
      this.feedbckForm.get("to_date").value
    ) {
      if (
        this.feedbckForm.get("from_date").value >=
        this.feedbckForm.get("to_date").value
      ) {
        this.feedbckForm.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
