import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import Swal from 'sweetalert2';
import { CannisterId } from '../interface/cannister-id.interface';

@Component({
  selector: 'app-cannister-id',
  templateUrl: './cannister-id.component.html',
  styleUrls: ['./cannister-id.component.scss']
})
export class CannisterIdComponent implements OnInit {
  displayedColumns: string[] = ['Id', 'CannisterId', 'cannister_id'];
  dataSource: MatTableDataSource<CannisterId>;
  pdfData: CannisterId[] =[];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly loader: LoaderService,
    private readonly toastr: ToastrService
  ) {
   }

  ngOnInit() {
    this.getCannisterId();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('cannisterIdTable', 'cannisterId_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('cannisterIdTable', 'cannisterId_list');
    }
  }

  getCannisterId() {
    this.facilityConfig.getCannisterId().subscribe(res => {
      res = res.filter(val => {
        if (val.Status) {
          return val;
        }
      }) || [];
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Id': key.Id,
          'Cannister Id': key.CanniserId,
          Status: key.Status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deleteTransport({ Id }) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.facilityConfig.deleteCannisterId(Id).subscribe(res => {
          if (res) {
            this.toastr.success('Successfully deleted Station Transport', 'Success');
            this.getCannisterId();
          } else {
            this.toastr.error('Error occured in deleting Station Transport', 'Error');
          }
        }, err => {
          this.toastr.error('Error occured in deleting Station Transport', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }
}
