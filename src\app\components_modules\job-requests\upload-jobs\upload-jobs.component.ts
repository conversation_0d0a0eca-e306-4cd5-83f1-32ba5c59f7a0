import { Component, OnInit } from "@angular/core";
import { FormBuilder, Validators } from "@angular/forms";
import { ExportToCsv } from "export-to-csv";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service.js";
import sampleData from "../../../shared/sample-files/job-request-sample.js";
@Component({
  selector: "app-upload-jobs",
  templateUrl: "./upload-jobs.component.html",
  styleUrls: ["./upload-jobs.component.scss"],
})
export class UploadJobsComponent implements OnInit {
  error: string;
  fileForm: any;
  csvFile: any;
  tabIndex: number = 0;
  displayedColumns: string[] = [
    "valid",
    "due_date",
    "equipment",
    "from_location",
    "isolation_precaution",
    "job_category",
    "job_type",
    "nric",
    "patient_name",
    "request_type",
    "to_location",
  ];
  dataSource = [];
  dataSourceSuccess: any[];
  constructor(
    private facilityService: FacilityConfigService,
    private fb: FormBuilder,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    this.fileForm = this.fb.group({
      file: ["", Validators.required],
    });
  }
  downloadSample() {
    var data = sampleData;

    const options = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalSeparator: ".",
      showLabels: false,
      showTitle: false,
      useTextFile: false,
      useBom: true,
      useKeysAsHeaders: true,
    };
    const csvExporter = new ExportToCsv(options);

    csvExporter.generateCsv(data);
  }

  getMediaFile(file) {
    this.csvFile = file;
  }
  getUploadedFile() {
    if (this.fileForm.valid && this.csvFile) {
      const formData = new FormData();
      formData.append("file", this.csvFile);
      this.facilityService.previewJobRequest(formData).subscribe(
        (data: any[]) => {
          if (data.length > 0) {
            this.dataSource = data;
            this.tabIndex = 1;
          }
        },
        (error) => this.toastr.error(error)
      );
    } else {
      this.error = "Please select CSV file";
    }
  }

  uploadCsv() {
    const upoadData = this.dataSource.filter((data) => data.valid);
    this.facilityService.uploadJobRequestData(upoadData).subscribe(
      (uploaded) => {
        if (uploaded) {
          this.toastr.success("job created successfully", "Success!!");
          this.tabIndex = 2;
          this.dataSourceSuccess = this.dataSource;
        } else {
          this.toastr.error("something went wrong, please try again");
          this.tabIndex = 0;
        }
      },
      (error) => this.toastr.error("something went wrong, please try again")
    );
  }

  uploadNewJob() {
    this.tabIndex = 0;
  }
}
