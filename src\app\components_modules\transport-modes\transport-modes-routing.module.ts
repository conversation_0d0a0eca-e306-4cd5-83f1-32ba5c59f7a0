import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListTransportModesComponent } from './list-transport-modes/list-transport-modes.component';
import { AddUpdateTransportModesComponent } from './add-update-transport-modes/add-update-transport-modes.component';

const routes: Routes = [
  {path: '', component: ListTransportModesComponent},
  {path: 'addtransportMode', component: AddUpdateTransportModesComponent},
  {path: 'updatetransportMode/:id', component: AddUpdateTransportModesComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TransportModesRoutingModule { }
