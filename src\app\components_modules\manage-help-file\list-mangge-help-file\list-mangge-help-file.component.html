<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  routerLink="./addmanagehelpfile"
                >
                  Add Mange Help File
                </button>
              </p>
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>View Manage Help Files</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <fieldset class="scheduler-border">
                    <legend></legend>
                    <div class="row">
                      <div class="col-3">
                        <div class="col">
                          <mat-form-field>
                            <input
                              matInput
                              placeholder="Search..."
                              #filter
                              (keydown)="applyFilter($event.target.value)"
                            />
                            <mat-icon matSuffix>search</mat-icon>
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="col-2">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="filter.value = ''; applyFilter(filter.value)"
                        >
                          <em class="fa fa-minus-square-o"></em>Reset
                        </button>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="managehelptable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>
                      <ng-container matColumnDef="filename">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          File Name
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.topic }}</td>
                      </ng-container>

                      <ng-container matColumnDef="download_id">
                        <th id="" mat-header-cell *matHeaderCellDef>PDF/PPT</th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            href="javascript:void(0);"
                            #downloadZipLink
                            data-toggle="modal"
                            (click)="
                              downloadHelpFile(
                                showMediaFile(row.files, 'pdf').filename,
                                'pdf'
                              )
                            "
                            >{{ showMediaFile(row.files, "pdf")?.filename }}</a
                          >
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="media">
                        <th id="" mat-header-cell *matHeaderCellDef>Media</th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            href="javascript:void(0);"
                            #downloadZipLink
                            data-toggle="modal"
                            data-target="#exampleModal1"
                            (click)="
                              downloadHelpFile(
                                showMediaFile(row.files, 'media').filename,
                                isVideoOrImage(row.files)
                              )
                            "
                            >{{
                              showMediaFile(row.files, "media")?.filename
                            }}</a
                          >
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="delete_id">
                        <th id="" mat-header-cell *matHeaderCellDef>Delete</th>
                        <td mat-cell *matCellDef="let row">
                          <em
                            class="material-icons"
                            style="cursor: pointer"
                            (click)="deleteHelpFile(row.id)"
                            >delete</em
                          >
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="['File Name']"
  [title]="'Help Files'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<div
  class="modal fade bd-example-modal-lg"
  id="exampleModal1"
  tabindex="-1"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <ng-container
          *ngIf="mediaUrl?.type.includes('image'); else videoMediaBox"
        >
          <img [src]="mediaUrl?.url" alt="Loading..." class="img-fluid" />
        </ng-container>
        <ng-template #videoMediaBox>
          <mat-video
            [src]="mediaUrl?.url"
            [autoplay]="true"
            [preload]="false"
            [fullscreen]="true"
            [download]="true"
            color="accent"
            spinner="spin"
          >
          </mat-video>
        </ng-template>
      </div>
    </div>
  </div>
</div>
