import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SpWorkLoadByLocationComponent } from './sp-work-load-by-location.component';

describe('SpWorkLoadByLocationComponent', () => {
  let component: SpWorkLoadByLocationComponent;
  let fixture: ComponentFixture<SpWorkLoadByLocationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SpWorkLoadByLocationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SpWorkLoadByLocationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
