<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id"> Add Distance Configuration</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id"> Update Distance Configuration</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="distanceForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>From<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="from_tower"
                                                            (ngModelChange)="blockSelected($event)">
                                                            <mat-option *ngFor="let data of blocks"
                                                                [value]="data.level_id">{{data.tower_level_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="distanceForm.controls.from_tower"
                                                                [fieldName]="'From'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>To<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="to_tower">
                                                            <mat-option *ngFor="let data of blocksSecond"
                                                                [value]="data.level_id">{{data.tower_level_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="distanceForm.controls.to_tower"
                                                                [fieldName]="'To'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Total Time(Min)<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput type="number" placeholder="Total Time"
                                                            (keyup)="standardTotalTime()" (click)="standardTotalTime()"
                                                            (keydown)="inputValidation.onlyNumbers($event)"
                                                            autocomplete="off" formControlName="total_time">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="distanceForm.controls.total_time"
                                                                [fieldName]="'Total Time'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="savedistance('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="savedistance('update')">Update</button>
                                            <button mat-raised-button (click)="distanceForm.reset(); getDistanceById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>