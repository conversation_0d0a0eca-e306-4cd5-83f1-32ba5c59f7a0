<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right;">
                                <button mat-raised-button color="primary" routerLink="./addupdateStationDepartments"> Add Station Departments </button>
                            </p>
                            <ul class="nav">
                                <li class="w-100">
                                    <p>View Station Departments</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <!-- <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <div class="row">
                                            <div class="col-3">
                                                <div class="col">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2">
                                                <button class="btn btn-sm  btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div> -->
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="manageDepartmentTable" mat-table [dataSource]="dataSource" matSort
                                            class="w-100">
                                            <caption></caption>

                                            <ng-container matColumnDef="Department">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> Department </th>
                                                <td mat-cell *matCellDef="let row"> {{row.department_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="Status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> No. of Station Location </th>
                                                <td mat-cell *matCellDef="let row"> {{row.station_locations.length}} </td>
                                            </ng-container>
                                            <ng-container matColumnDef="action">
                                                <th id="" mat-header-cell *matHeaderCellDef> Edit </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        (click)="updateStationDepartment(row)">edit</em>
                                                </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>