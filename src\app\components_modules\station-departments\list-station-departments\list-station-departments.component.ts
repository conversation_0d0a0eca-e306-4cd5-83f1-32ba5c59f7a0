import { Router, ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../Apiservices/userService/user.service';

@Component({
  selector: 'app-list-station-departments',
  templateUrl: './list-station-departments.component.html',
  styleUrls: ['./list-station-departments.component.scss']
})
export class ListStationDepartmentsComponent implements OnInit {
  displayedColumns: string[] = ['Department', 'Status', 'action'];
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  stationDepartmentsList: any;
  
  constructor(
    private readonly userService: UserService,
    // private readonly toastr: ToastrService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getStationDepartmentList();
  }

  getStationDepartmentList() {
    this.userService.getStationDepartments().subscribe(res => {
      if (res) {
        this.stationDepartmentsList = res;
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }
    })
  }

  updateStationDepartment(data) {
    this.router.navigate([`./addupdateStationDepartments`, { 'id': data.department_id}], { relativeTo: this.activatedRoute })
    // this.userService.getStationDepartmentById(data.id).subscribe(res => {
    //   if (res) {

    //   }
    // })
  }

}
