<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Level</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Level</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="levelsForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Tower Name<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="towerid">
                                                            <mat-option *ngFor="let tower of towers"
                                                                [value]="tower.tower_id">{{tower.tower_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="levelsForm.controls.towerid"
                                                                [fieldName]="'Tower Name'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Level Name <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Level Name"
                                                            formControlName="levelname">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="levelsForm.controls.levelname"
                                                                [fieldName]="'Level Name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Short Description <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <textarea matInput #desc placeholder="Descripton"
                                                            maxlength="300" formControlName="shortdescription"
                                                            rows="1"></textarea>
                                                        <mat-hint style="text-align: end;">{{desc.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="levelsForm.controls.shortdescription"
                                                                [fieldName]="'Description'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <mat-radio-group formControlName="status">
                                                        <mat-radio-button class="example-margin" value="true">Active
                                                        </mat-radio-button>
                                                        <mat-radio-button class="example-margin" value="false">
                                                            Inactive</mat-radio-button>
                                                    </mat-radio-group>
                                                    <mat-error class="pull-left error-css">
                                                        <app-error-message [control]="levelsForm.controls.status"
                                                            [fieldName]="'Status'" [fieldType]="'select'">
                                                        </app-error-message>
                                                    </mat-error>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Reason <span class="error-css">
                                                                <!-- <span class="error-css">*</span> -->
                                                            </span>
                                                        </mat-label>
                                                        <textarea matInput #reason placeholder="Reason" maxlength="300"
                                                            formControlName="reason" rows="1"></textarea>
                                                        <mat-hint style="text-align: end;">{{reason.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="levelsForm.controls.reason"
                                                                [fieldName]="'Reason'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveLevel('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveLevel('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="levelsForm.reset(); levelsForm.controls.status.setValue('true'); getLevelsById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                    <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Edit Logs
                                        </legend>
                                        <div class="row">
                                            <div class="col-12">
                                                <label class="col-4">Edited By</label>
                                                <label class="col-4">Status</label>
                                                <label class="col-4">Edited On</label>
                                            </div>
                                        </div>
                                        <div class="row" *ngFor="let log of editLogs">
                                            <div class="col-4">
                                                {{log.edited_by}}
                                            </div>
                                            <div class="col-4">
                                                {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                                            </div>
                                            <div class="col-4">
                                                {{log.edited_date | localDateConversion: "full"}}
                                            </div>
                                        </div>
                                    </fieldset> -->
                                    <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Audit Logs
                                          </legend>
                                          <div class="mb-8">
                                            <div class="logs-header row">
                                              <div style="max-width: 150px;min-width: 150px;" class="text-center">Level Name</div>
                                              <div class="col-1 text-center">Status</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                                              <div class="col-2 text-center">Request By</div>
                                              <div class="col-2 text-center">Request Date</div>
                                              <div class="col-2 text-center">Approved By</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                                            </div>
                                          </div>
                                        <div class="" *ngFor="let log of editLogs; let i = index">
                                            <div class="card card-xl-stretch">
                                                <div class="card-body card-body-log pt-2 row m-2">
                                                    <div style="max-width: 150px;min-width: 150px;" class="text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                                                    <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                                                        'In-Active'}}</div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approvalStatus}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                                                    </div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                                                        (log?.formattedCreatedDate) : '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                                                    </div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approved_Date ?
                                                        (log?.formattedApprovedDate) : '--'}}</div>
                                                </div>
                                            </div>
                                            <span *ngIf="i !== editLogs.length-1" style='font-size:35px;'
                                                class="text-muted">&#8593;</span>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>