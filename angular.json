{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"uems": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/uems", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": false, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/perfect-scrollbar/css/perfect-scrollbar.css", "src/assets/scss/material-dashboard.scss", "src/assets/css/demo.css", "src/assets/external/timepicker/jquery.timepicker.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/popper.js/dist/umd/popper.js", "node_modules/bootstrap-material-design/dist/js/bootstrap-material-design.min.js", "node_modules/arrive/src/arrive.js", "node_modules/moment/moment.js", "node_modules/perfect-scrollbar/dist/perfect-scrollbar.min.js", "node_modules/bootstrap-notify/bootstrap-notify.js", "node_modules/chartist/dist/chartist.js", "node_modules/jspdf/dist/jspdf.min.js", "src/assets/external/highchart/highcharts.js", "src/assets/external/highchart/highcharts-more.js", "src/assets/external/highchart/solid-gauge.js", "src/assets/external/timepicker/jquery.timepicker.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "uems:build"}, "configurations": {"production": {"browserTarget": "uems:build:production"}, "uat": {"browserTarget": "uems:build:uat"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "uems:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "uems:serve"}, "configurations": {"production": {"devServerTarget": "uems:serve:production"}}}}}}, "defaultProject": "uems"}