import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListManggeHelpFileComponent } from './list-mangge-help-file/list-mangge-help-file.component';
import { AddUpdateManggeHelpFileComponent } from './add-update-mangge-help-file/add-update-mangge-help-file.component';


const routes: Routes = [
  { path: '', component: ListManggeHelpFileComponent },
  { path: 'addmanagehelpfile', component: AddUpdateManggeHelpFileComponent },
  { path: 'updatemanagehelpfile/:id', component: AddUpdateManggeHelpFileComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageHelpFileRoutingModule { }
