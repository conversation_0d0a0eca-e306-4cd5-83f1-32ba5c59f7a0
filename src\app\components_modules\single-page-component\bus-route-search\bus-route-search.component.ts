import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { BusRouteSearch } from 'src/app/models/busRouteSearch';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import * as moment from 'moment';

@Component({
  selector: 'app-bus-route-search',
  templateUrl: './bus-route-search.component.html',
  styleUrls: ['./bus-route-search.component.scss', '../../../scss/table.scss']
})
export class BusRouteSearchComponent implements OnInit {
  routes = [
    { path: './../enhancedReports', label: 'Search (ACK)' },
    { path: './../busrouteSearch', label: 'Bus Route Reports' },
    { path: './../porterReports', label: 'Porter Workload' },
    { path: './../pts-reports', label: 'PTS Reports' },
    { path: './../enhanced-location-reports', label: 'Location Reports' }
  ];

  displayedColumns: string[] = ['route_no', 'route_name', 'route_type', 'created_date', 'start_location', 'end_location', 'location_count',
    'start_time', 'assign_time', 'actual_start_time', 'end_time', 'cancel_time', 'resource1', 'resource2', 'remarks', 'requestor'];
  dataSource: MatTableDataSource<BusRouteSearch>;
  locationType = [];
  pdfData = [];
  routeType = [{ displayName: 'Transaction', value:'Ad-hoc'}, { displayName: 'Master', value:'Scheduled'}]; //'Ad-hoc', 'Scheduled'
  busRouteForm: FormGroup;
  today = new Date().toUTCString();
  locationNames = [];
  prevMonth = moment(this.today).subtract(1, 'months');
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth['_d'];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: 'col-16',
    label: 'From Date',
    required: true,
    minDateStart: '2000-12-12',
    maxDateStart: '',
    minDateEnd: '',
    maxDateEnd: '',
  };
  ToDate = {
    rangePicker: false,
    collumns: 'col-16',
    label: 'To Date',
    required: true,
    minDateStart: '2000-12-12',
    maxDateStart: '',
    minDateEnd: '',
    maxDateEnd: '',
  };
  exportResult = [];
  count = 0;
  constructor(
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    public activatedRoute: ActivatedRoute,
    private readonly report: ReportsService,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly toastr: ToastrService,
  ) {
    this.createForm();
  }

  createForm() {
    this.busRouteForm = this.fb.group({
      route_type: [''],
      route_name: [''],
      from_date: [this.month],
      to_date: [this.today],
      start_time: [''],
      start_location: [''],
      end_location: [''],
      pageNo: [null],
      pageSize: [null]
    });
  }


  ngOnInit() {
    this.getLocations();
    this.getCurrentDateTime();
  }
  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe(res => {
      this.today = res;
       this.today = new Date().toISOString();
      this.prevMonth = moment(this.today).subtract(1, 'months');
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth['_d'];
      this.createForm();
      this.searchBusRoute();
    });
  }
  getLocations() {
    this.facilityConfig.getLocations().subscribe(res => {
      res = res && res.length && res.filter(val => {
        if (val.status === 1) {
          return val;
        }
      }) || [];
      this.locationType = res;
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('enhancedBusRoute', 'enhancedBusRoute_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.searchBusRoute(fileType)
      // TableUtil.exportToExcel('enhancedBusRoute', 'enhancedBusRoute_list');
    }
  }

  mapTableDataforReport(exportResult, type?) {
    let mappedData;
    let exportObj: any[]=[];
    for(var i=0;i<exportResult.length;i++)
    {
      mappedData = {
        'Route No': exportResult[i].route_no,
        'Route Name': exportResult[i].route_name,
        'Route Type': exportResult[i].route_type,
        'Creation Time': exportResult[i].created_date,
        'Start Location': exportResult[i].start_location ? exportResult[i].start_location.toString().replace("\r\n","") : "--",
        'End Location': exportResult[i].end_location ? exportResult[i].end_location.toString().replace("\r\n","") : "--",
        'No of Locations': exportResult[i].location_count,
        'Scheduled Start Time': exportResult[i].start_time,
        'Assigned Time': exportResult[i].assign_time,
        'Start Time': exportResult[i].actual_start_time,
        'Completion Time': exportResult[i].end_time,
        'Cancel Time': exportResult[i].cancel_time,
        'Resource 1': exportResult[i].resource1,
        'Resource 2': exportResult[i].resource2,
        'Remarks': exportResult[i].remarks,
        'Requestor': exportResult[i].requestor,
      };
      exportObj.push(mappedData);
    }
    if (type == 'csv') {
      TableUtil.exportArrayToCsvDynamically(exportObj, "enhancedBusRoute_list");
    } else {
      TableUtil.exportArrayToExcelDynamically(exportObj, "enhancedBusRoute_list");
    }
  }
  searchBusRoute(type?, paginationData?) {
    if (this.busRouteForm.valid) {
      const value = this.busRouteForm.value;
      value.from_date = moment(value.from_date).format('YYYY-MM-DD');
      value.to_date = moment(value.to_date).format('YYYY-MM-DD');
      value.pageNo = type && type == 'next' ? Number(paginationData.offset) + 1 : 1;
      value.pageSize = type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xsls') ? this.count : this.paginator.pageSize || 50;
      const data = this.userService.getOnlyFilledObjects(value);
      this.report.enhancedBusRoute(data).subscribe(res => {
        if (res && res.length > 0) {
          if (type && type == 'next') {
            this.exportResult = res;
            this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
            this.exportResult.push(...res);
            this.exportResult.length = res[0].count;
            this.dataSource = new MatTableDataSource<any>(this.exportResult);
            this.dataSource._updateChangeSubscription();
            this.dataSource.paginator = this.paginator;
          } else if (type == 'csv' || type == 'xsls') {
            this.exportResult = res;
            this.mapTableDataforReport(this.exportResult, type);
          } else {
            this.exportResult = res;
            this.exportResult.length = res[0].count;
            this.count = res[0].count;
            this.dataSource = new MatTableDataSource(this.exportResult || []);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
            this.setPageSizeOptions();
          }
        } else {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
        }
      }, err => {
        this.toastr.warning('Search Failed', 'failed!');
        this.paginator.length = 0;
        this.dataSource = new MatTableDataSource([]);
      });
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Route No': key.route_no,
          'Route Name': key.route_name,
          'Route Type': key.route_type,
          'Creation Time': key.created_date,
          'Start Location': key.start_location,
          'End Location': key.end_location,
          'No of Locations': key.location_count,
          'Scheduled Start Time': key.start_time,
          'Assigned Time': key.assign_time,
          'Start Time': key.actual_start_time,
          'Completion Time': key.end_time,
          'Cancel Time': key.cancel_time,
          'Resource 1': key.resource1,
          'Resource 2': key.resource2,
          Remarks: key.remarks,
          Requestor: key.requestor,
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  pageChanged(event){
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit){
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.searchBusRoute('next', paginationData);
  }

  restForm() {
    this.createForm();
    this.searchBusRoute();
  }

  getDate() {
    if (this.busRouteForm.get('from_date').value && this.busRouteForm.get('to_date').value) {
      if (this.busRouteForm.get('from_date').value >= this.busRouteForm.get('to_date').value) {
        this.busRouteForm.get('to_date').setValue('');
        this.toastr.error('To date should be less then From date', 'Error');
      }
    }
  }
}
