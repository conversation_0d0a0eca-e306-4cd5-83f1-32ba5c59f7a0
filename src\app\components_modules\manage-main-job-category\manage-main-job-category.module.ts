import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageMainJobCategoryRoutingModule } from './manage-main-job-category-routing.module';
import { ListMianJobCategoryComponent } from './list-mian-job-category/list-mian-job-category.component';
import { AddUpdateMianJobCategoryComponent } from './add-update-mian-job-category/add-update-mian-job-category.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListMianJobCategoryComponent, AddUpdateMianJobCategoryComponent],
  imports: [
    CommonModule,
    ManageMainJobCategoryRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class ManageMainJobCategoryModule { }
