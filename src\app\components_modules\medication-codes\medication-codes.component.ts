import { FormGroup } from '@angular/forms';
import { FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import Swal from 'sweetalert2';
import { MedicationCodes } from './interface/medication-codes.interface';

@Component({
  selector: 'app-medication-codes',
  templateUrl: './medication-codes.component.html',
  styleUrls: ['./medication-codes.component.scss']
})
export class MedicationCodesComponent implements OnInit {
  displayedColumns: string[] = ['MedicationName', 'MedicationCode', 'Status', 'medication_action'];
  dataSource: MatTableDataSource<MedicationCodes>;
  pdfData: MedicationCodes[] = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  fileForm: FormGroup;
  xlsxFile: any;
  constructor(
    private readonly loader: LoaderService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly toastr: ToastrService,
    private masterDataService: MasterDataService,
    private fb: FormBuilder
  ) {
    this.fileForm = this.fb.group({
      file: ["", Validators.required],
    });
  }

  ngOnInit() {
    this.getMedicationCodes();
  }

  getMedicationCodes() {
    this.facilityConfig.getMedicationCodes().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('Medication_codes_table Code list', 'Medication_codes_table');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('Medication_codes_table', 'Medication_table');
    }
  }

  applyFilter(filterValue: string) {
    this.fileForm.reset();
    this.xlsxFile = undefined
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter((key: MedicationCodes) => {
        const a = {
          'Medication Name': key.MedicationName,
          'Medication Code': key.MedicationCode,
          Status: key.Status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  getMediaFile(file) {
    this.xlsxFile = file;
  }

  uploadFile() {
    const uploadData = new FormData();
    uploadData.append("file", this.xlsxFile);
    this.masterDataService.uploadMedicationCodes(uploadData).subscribe(
      (uploaded) => {
        if (uploaded) {
          this.toastr.success("Medication Codes Added Successfully", "Success!!");
          this.getMedicationCodes();
        } else {
          this.toastr.error("something went wrong, please try again");
        }
      },
      (error) => this.toastr.error("something went wrong, please try again")
    );
  }

  deleteMedication({ Id }) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.masterDataService.deleteMedicationCode(Id).subscribe(res => {
          if (res) {
            this.toastr.success('Successfully Deleted Medication Code', 'Success');
            this.getMedicationCodes();
          } else {
            this.toastr.error('Error occurred in deleting Station Transport', 'Error');
          }
        }, err => {
          this.toastr.error('Error occurred in deleting Station Transport', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

  clearMedications() {
    // const ids = [...this.dataSource.data.map(item => item.Id)];
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to clear all medications!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes'
    }).then((result) => {
      if (result.value) {
        this.masterDataService.deleteAllMedicationCodes().subscribe(res => {
          if (res) {
            this.toastr.success('Successfully deleted Medication Codes', 'Success');
            this.getMedicationCodes();
          } else {
            this.toastr.error('Error occurred in deleting Station Transport', 'Error');
          }
        }, err => {
          this.toastr.error('Error occurred in deleting Station Transport', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

}
