import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListColorCodesComponent } from './list-color-codes/list-color-codes.component';
import { AddUpdateColorCodesComponent } from './add-update-color-codes/add-update-color-codes.component';


const routes: Routes = [
  { path: '', component: ListColorCodesComponent },
  { path: 'addcolorcode', component: AddUpdateColorCodesComponent },
  { path: 'updatecolorcode/:id', component: AddUpdateColorCodesComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ColorCodesRoutingModule { }
