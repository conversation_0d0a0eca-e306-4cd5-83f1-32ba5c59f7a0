import { filter } from 'rxjs/operators';
import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatSort, MatPaginator, MatOption, MatSelect } from "@angular/material";
import { BusRoutes } from "../../../models/busRoutes";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-list-bus-routes",
  templateUrl: "./list-bus-routes.component.html",
  styleUrls: ["./list-bus-routes.component.scss", "../../../scss/table.scss"],
})
export class ListBusRoutesComponent implements OnInit {
  displayedColumns: string[] = [
    "route_no",
    "route_name",
    "start_time",
    "week_telly",
    "no_of_locations",
    "staff_name",
    "remarks",
    "status",
    "action",
  ];
  dataSource: MatTableDataSource<BusRoutes>;
  locationNames = [];
  pdfData = [];
  departmentList = [] //{'departmentName': 'All', 'departmentId': 'All'}
  selectedDepartments = ['All'];
  departmentsIds = [];
  isFirstLoad = true;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatOption, { static: true }) allSelected: MatOption;
  @ViewChild(MatSelect, { static: true }) select: MatSelect;
  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly loader: LoaderService,
    private toastr: ToastrService
  ) {}
  ngOnInit() {
    this.getDepartments();
  }

  ngAfterViewChecked() {
    if (this.select && this.select.options && this.select.options.length > 1 && this.isFirstLoad) {
      this.select.options.forEach((item: MatOption) => item.select());
    }
  }

  tosslePerOne(all){ 
    if (this.allSelected.selected) {  
     this.allSelected.deselect();
     return false;
    }
   if(this.selectedDepartments.length==this.departmentList.length) {
     this.allSelected.select();
    }
  }
   toggleAllSelection() {
    this.isFirstLoad = false;
     if (this.allSelected.selected) {
      this.departmentList.map(item => {
        this.selectedDepartments[0] = 'All';
        this.selectedDepartments.push(item.departmentName);
        this.select.options.forEach((item: MatOption) => item.select());
      })
     } else {
       this.selectedDepartments= [];
     }
   }

  getBusRoutes() {
    const data = {
      'departments': this.selectedDepartments.includes('All') ? this.departmentsIds : this.selectedDepartments
    }
    this.facilityConfig.getBusRoute(data).subscribe((res) => {
      res =
        res &&
        res.filter((val) => {
          const t = val.created_date.match("T");
          if (t) {
            val.created_date = val.created_date.replace(t, "  ");
          }
          return val;
        });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string, type?) {
    if (type) {
      // this.selectedDepartments = ['All'];
      this.select.options.forEach((item: MatOption) => item.select());
    }
    this.getBusRoutes();
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "busroutetable",
        "busroute_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("busroutetable", "busroute_list");
    }
  }

  getDepartments() {
    this.facilityConfig.getUserDepartments().subscribe((res) => {
      if (res){
        res.filter(data => {
          this.departmentList.push(data);
          this.departmentsIds.push(data.departmentId)
        })
        this.getBusRoutes();
      }
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Route No": key.route_no,
              "Bus Route Name": key.route_name,
              "Creation Time": key.created_date,
              "Start Time": key.start_time,
              Monday: key.monday,
              Tuesday: key.tuesday,
              Wednesday: key.wednesday,
              Thursday: key.thursday,
              Friday: key.friday,
              Saturday: key.saturday,
              Sunday: key.sunday,
              "Start Location": key.start_location_name,
              "End Location": key.end_location_name,
              "No of Locations": key.no_of_locations,
              "Porter Name": key.staff_name,
              Remarks: key.remarks,
              Requestor: key.requestor,
              Status: key.status,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  activateBusRoute(id: number, status: boolean) {
    if (status) {
      this.facilityConfig.activateBusRoute(id).subscribe(
        (data) => {
          if (data) {
            this.toastr.success("Bus route successfully activated", "Success");
          }
        },
        () => this.toastr.error("Error while activating bus route", "Error")
      );
    } else {
      this.toastr.error("Inactive bus routes cannot be activated", "Error");
    }
  }

  locationDisplay(locations, is_sequential) {
    if (!is_sequential) {
      this.locationNames = [...locations];
    } else {
      this.locationNames = [];
      locations.forEach((loc) => {
        this.locationNames.splice(loc.order, 0, loc);
      });
    }
  }
}
