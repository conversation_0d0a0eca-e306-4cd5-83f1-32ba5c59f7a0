import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { IDropdownSettings } from 'ng-multiselect-dropdown';

@Component({
  selector: 'app-add-update-transport-mapping',
  templateUrl: './add-update-transport-mapping.component.html',
  styleUrls: ['./add-update-transport-mapping.component.scss']
})
export class AddUpdateTransportMappingComponent implements OnInit {
  transportmappingForm: FormGroup;
  subListTransport: any = [];
  locationVal: any = [];
  dropdownSettings: IDropdownSettings = {};
  dropdownJobCategorySettings: IDropdownSettings = {};
  stationTransportData: any;
  disabled = false;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly masterDataSErvice: MasterDataService,
    private readonly facilityConfig: FacilityConfigService
  ) {
    this.transportmappingForm = this.fb.group({
      location_id: ['', Validators.required],
      transport_type_subIds: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.transportSubList();
    this.getLocations();
    this.getStationTransportById();
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'locationId',
      textField: 'locationName',
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      showSelectedItemsAtTop: true,
      defaultOpen: true
    };
    this.dropdownJobCategorySettings = {
      singleSelection: false,
      idField: 'sj_category_id',
      textField: 'sub_main_cat',
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      showSelectedItemsAtTop: true,
      defaultOpen: true
    }
  }

  onItemSelect(item: any) {
    console.log(item);
  }
  onSelectAll(items: any) {
    // console.log(items);
  }

  getStationTransportById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.masterDataSErvice.getStationTransportById(this.activatedRoute.snapshot.params.id)
        .subscribe(res => {
          this.transportmappingForm.patchValue({
            location_id: res.loc_id,
            transport_type_subIds: res.transport_type_subIds
          });
          this.stationTransportData = res;
          if(this.activatedRoute.snapshot.params.id) {
            this.locationVal.push({'locationId': this.stationTransportData.loc_id, 'locationName': this.stationTransportData.location_name})
            this.disabled = true;
            const filteredData = {
              location_id: [],
              transport_type_subIds: []
            }
            this.locationVal.filter((loc) => {
            //  this.stationTransportData.loc_id.some(data => {
               if(loc.locationId == this.stationTransportData.loc_id){
                 filteredData.location_id.push(loc)
               }
            //  })
            })
            this.subListTransport.filter((loc) => {
              this.stationTransportData.transport_type_subIds.some(data => {
                if(loc.sj_category_id == data){
                  filteredData.transport_type_subIds.push(loc)
                }
              })
             })
            this.transportmappingForm.get('location_id').setValue([]);
            this.transportmappingForm.get('transport_type_subIds').setValue([]);
            this.transportmappingForm.get('location_id').patchValue(filteredData.location_id)
            this.transportmappingForm.get('transport_type_subIds').patchValue(filteredData.transport_type_subIds)
          }
        });
    }
  }

  transportSubList() {
    this.masterDataSErvice.getTransportSubList().subscribe(res => {
      this.subListTransport = res;
    });
  }
  async getLocations() {
    await this.facilityConfig.getStationMappingLocations().subscribe(res => {
      // res = res && res.length && res.filter(val => {
      //   if (val.status) {
      //     return val;
      //   }
      // }) || [];
      this.locationVal = res || [];
    });
  }
  saveTransportMapping(action) {
    const apiData = {
      location_id: [...this.transportmappingForm.getRawValue().location_id.map(item => item.locationId)],
      transport_type_subIds: [...this.transportmappingForm.getRawValue().transport_type_subIds.map(item => item.sj_category_id)]
    }
    if (this.transportmappingForm.valid) {
      this.masterDataSErvice.addUpadteStationTransport(
        apiData, 'api/stationmappings/add')
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${action === 'save' ? 'added' : 'updated'} subjob Category`, 'Success');
        }, err => {
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.transportmappingForm.markAllAsTouched();
    }
  }

}
