@import "../../../../../assets/scss/navbar.scss";

table {
  font-size: 18px;
  height: 67px;
  text-align: left !important;
  color: #16078a;
  margin: 28px;
  font-weight: 500;
  background-color: #ffffff;
}

td,
th {
  text-align: center !important;
  border: 1px solid #ccc !important;
  padding: 0 !important;
  width: 200px !important;
  max-width: 200px;
  min-width: 200px;
}

::ng-deep .equipmentReport .mat-sort-header-container {
  display: flex;
  justify-content: center;
}

.btn-primary {
  margin-right: 10px;
}
.spacing {
  margin-bottom: 12px;
}
.equipmentReport tr:first-child th:first-child {
  border-bottom: none !important;
}

#middele-header {
  text-align: left !important;
  padding-left: 50px !important;
  background: lightblue;
}

.chart-wrapper {
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  background: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
