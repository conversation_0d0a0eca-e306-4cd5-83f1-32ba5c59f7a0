import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TowerRoutingModule } from './tower-routing.module';
import { ListTowersComponent } from './list-towers/list-towers.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateTowerComponent } from './add-update-tower/add-update-tower.component';


@NgModule({
  declarations: [ListTowersComponent, AddUpdateTowerComponent],
  imports: [
    CommonModule,
    TowerRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class TowerModule { }
