import { Injectable } from '@angular/core';
import { Resolve, Router } from '@angular/router';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Injectable({
  providedIn: 'root'
})
export class MenuReolverService implements Resolve<any> {

  constructor(
    private readonly userService: UserService,
    private readonly router: Router
  ) { }

  resolve() {
    let data;
    if (!data) {
      data = this.userService.menu();
    }
    if (data) {
      return data;
    } else {
      this.router.navigateByUrl('/');
    }
  }
}
