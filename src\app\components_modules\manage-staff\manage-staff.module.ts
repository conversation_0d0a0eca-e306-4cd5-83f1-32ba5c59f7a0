import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageStaffRoutingModule } from './manage-staff-routing.module';
import { ListManageStaffComponent } from './list-manage-staff/list-manage-staff.component';
import { AddUpdateManageStaffComponent } from './add-update-manage-staff/add-update-manage-staff.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { PorterLiveStatusComponent } from './porter-live-status/porter-live-status.component';


@NgModule({
  declarations: [ListManageStaffComponent, AddUpdateManageStaffComponent, PorterLiveStatusComponent],
  imports: [
    CommonModule,
    ManageStaffRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class ManageStaffModule { }
