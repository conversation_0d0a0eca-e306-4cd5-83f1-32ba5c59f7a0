import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  allRequests: any = [];
  isLoading = new Subject<boolean>();

  show() {
    this.isLoading.next(true);
  }
  hide() {
    this.isLoading.next(false);
  }

  assignAllrequests(url) {
    this.allRequests.push(url);
    this.show();
  }

  removeUrlOnresponse(url) {
    const index = this.allRequests.indexOf(url);
    if (index > -1) {
      this.allRequests.splice(index, 1);
    }
    if (this.allRequests.length === 0) {
      this.hide();
    }
  }

  checkForDuplicateUrl(url) {
    const index = this.allRequests.indexOf(url);
    if (index > -1) {
      return false;
    } else {
      return true;
    }
  }
}
