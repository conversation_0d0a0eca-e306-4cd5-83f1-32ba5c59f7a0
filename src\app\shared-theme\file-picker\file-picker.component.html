<ng-container>
  <ng-container>
    <label class="form-control-label"
      >{{ fieldName ? fieldName : "Upload"
      }}<span *ngIf="required" class="red">*</span></label
    >
    <div class="custom-file">
      <input
        type="file"
        class="custom-file-input"
        (change)="documnet($event)"
        [formControl]="control"
      />

      <label class="custom-file-label" for="customFile">
        <span *ngIf="fileupload; else chooseFile">{{ fileupload.name }}</span>
        <ng-template #chooseFile> <span>Choose File</span> </ng-template>
      </label>
      <mat-error
        class="pull-left error-css"
        [ngClass]="{ 'float-right': showErrorRight }"
      >
        <div class="errormsg">
          <div *ngIf="errorMessage !== null">{{ errorMessage }}</div>
        </div>
      </mat-error>
    </div>
  </ng-container>
</ng-container>
