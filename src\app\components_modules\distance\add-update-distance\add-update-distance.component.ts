import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';

@Component({
  selector: 'app-add-update-distance',
  templateUrl: './add-update-distance.component.html',
  styleUrls: ['./add-update-distance.component.scss']
})
export class AddUpdateDistanceComponent implements OnInit {
  distanceForm: FormGroup;
  blocks = [];
  blocksSecond = [];
  modesOfTransport = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService
  ) {
    this.distanceForm = this.fb.group({
      from_tower: ['', Validators.required],
      to_tower: ['', Validators.required],
      total_time: ['', Validators.required],
    });
  }
  ngOnInit() {
    this.getDistanceConfigTowers();
    this.getDistanceById();
  }

  getDistanceById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getDistanceListById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.distanceForm.patchValue({
            from_tower: res.from_tower,
            to_tower: res.to_tower ,
            total_time: res.total_time,
          });
        });
    }
  }

  standardTotalTime() {
    if (this.distanceForm.get('total_time').value < 1) {
      this.distanceForm.get('total_time').setValue('');
    }
  }

  getDistanceConfigTowers() {
    this.facilityConfig.getDistanceConfigTowers().subscribe(res => {
      this.blocks = res;
      this.blocksSecond = res;
    });
  }
  blockSelected(event) {
    this.distanceForm.get('to_tower').reset();
    this.blocksSecond = this.blocks.slice();
    const findIndex = this.blocksSecond.findIndex(res => res.level_id === event);
    if (findIndex !== -1) {
      this.blocksSecond.splice(findIndex, 1);
    }
  }

  savedistance(actiontype) {
    let distanceId: any;
    if (this.distanceForm.valid) {
      const data = this.distanceForm.value;
      if (actiontype === 'update') {
        distanceId = Number(this.activatedRoute.snapshot.params.id);
      }
      this.facilityConfig.addDistanceConfiguration(
        data, actiontype === 'save' ?
        'api/distanceconfigurations/add' : `api/distanceconfigurations/edit/${distanceId}`, actiontype)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} level`, 'Success');
        }, err => {
        });

    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.distanceForm.markAllAsTouched();
    }
  }

}
