# Uems

## Build Status

[![Build Status](http://builds.spurtreetech.com:8080/buildStatus/icon?job=STT_UEMS_UI_Dev)](http://builds.spurtreetech.com:8080/job/STT_UEMS_UI_Dev/)

## Sonarqube Metrics

[![Quality Gate Status](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=alert_status)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Bugs](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=bugs)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Code Smells](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=code_smells)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Duplicated Lines (%)](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=duplicated_lines_density)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Maintainability Rating](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=sqale_rating)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Reliability Rating](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=reliability_rating)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Security Rating](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=security_rating)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Technical Debt](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=sqale_index)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI) [![Vulnerabilities](https://ci.spurtreetech.com/sonar/api/project_badges/measure?project=UEMSPortering-UEMS_UI&metric=vulnerabilities)](https://ci.spurtreetech.com/sonar/dashboard?id=UEMSPortering-UEMS_UI)

## Description

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 8.3.20.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).