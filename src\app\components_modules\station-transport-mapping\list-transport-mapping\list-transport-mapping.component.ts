import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import Swal from 'sweetalert2';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { ToastrService } from 'ngx-toastr';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';


export interface TransportType {
  loc_id: string;
  location_name: string;
  transport_type_subIds;
}


@Component({
  selector: 'app-list-transport-mapping',
  templateUrl: './list-transport-mapping.component.html',
  styleUrls: ['./list-transport-mapping.component.scss', '../../../scss/table.scss']
})

export class ListTransportMappingComponent implements OnInit {

  displayedColumns: string[] = ['location_name', 'job_category_list', 'loc_id', 'delete_id'];
  dataSource: MatTableDataSource<TransportType>;
  transportData: any = [];
  pdfData = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly masterDataSErvice: MasterDataService,
    private readonly loader: LoaderService,
    private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.getTransportMapping();
  }
  getTransportMapping() {
    this.masterDataSErvice.getStationTransport().subscribe(res => {
      const a = [];
      res.forEach((val: any) => {
        if (val && val.transport_type_subIds && val.transport_type_subIds.length) {
          val.job_category_list.forEach(x => {
            const b = {
              loc_id: val.loc_id,
              location_name: val.location_name,
              job_category_list: x
            };
            a.push(b);
          });
        }
      });
      this.pdfData = a.slice();
      this.pdfData = this.pdfData.filter(key => {
        const b = {
          Location: key.location_name,
          'Transport Type': key.transport_type_subIds,
        };
        Object.assign(key, b);
        return key;
      });
      this.dataSource = new MatTableDataSource(res);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('transportTable', 'transportTable_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('transportTable', 'transportTable_list');
    }
  }

  deleteTransport({ loc_id, job_category_list }) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.masterDataSErvice.deleteStationTransport(loc_id, job_category_list).subscribe(res => {
          if (res) {
            this.toastr.success('Successfully deleted Station Transport', 'Success');
            this.getTransportMapping();
          } else {
            this.toastr.error('Error occured in deleting Station Transport', 'Error');
          }
        }, err => {
          this.toastr.error('Error occured in deleting Station Transport', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }
}

