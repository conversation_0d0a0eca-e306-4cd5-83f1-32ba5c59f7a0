import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListCancelTemplatesComponent } from './list-cancel-templates/list-cancel-templates.component';
import { AddUpdateCancelTemplatesComponent } from './add-update-cancel-templates/add-update-cancel-templates.component';


const routes: Routes = [
  { path: '', component: ListCancelTemplatesComponent },
  { path: 'addcanceltemplate', component: AddUpdateCancelTemplatesComponent },
  { path: 'updatecanceltemplate/:id', component: AddUpdateCancelTemplatesComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CancelTemplatesRoutingModule { }
