import { Directive, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { NgModel } from '@angular/forms';

@Directive({
  selector: '[appSelectFirstOption]',
  providers: [NgModel]
})
export class SelectFirstOptionDirective implements OnChanges {

  @Input() allOption: Array<any> = []
  constructor(private ngModel: NgModel) {
  }

  @Output() isFirst: EventEmitter<boolean> = new EventEmitter()

  ngOnChanges(changes: SimpleChanges): void {
    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    //Add '${implements OnChanges}' to the class.
    const valueOnlyArray = Object.keys(this.allOption).map(data => this.allOption[data])
    if (valueOnlyArray.length == 1) {
      setTimeout(() => {
        this.ngModel.control.patchValue(valueOnlyArray[0])
        this.isFirst.emit(true);
      }, 200);
    }

  }

}
