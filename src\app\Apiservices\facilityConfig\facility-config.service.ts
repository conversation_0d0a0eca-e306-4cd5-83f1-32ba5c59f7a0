import { Injectable } from "@angular/core";
import { HttpService } from "../httpService/http.service";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class FacilityConfigService {
  constructor(private readonly httpService: HttpService) { }

  getIcons(): Observable<any> {
    return this.httpService.get("../../assets/icon.json");
  }

  getServerTimeCurrent(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/CurrentDatetime`);
  }

  fetchDepartmentList() {
    return this.httpService.get(`${environment.base_url}api/departments`);
  }
  previewJobRequest(formData): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/jobrequests/upload/preview`,
      formData
    );
  }
  uploadJobRequestData(allJobs): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/jobrequests/upload/`,
      allJobs
    );
  }

  fetchReportMenu(): Observable<any> {
    return this.httpService.get(`${environment.base_url}/api/menus/reports`);
  }

  getTowers(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/towers`);
  }
  getStationStatus(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/viewstatus/station`
    );
  }

  activateBusRoute(route_id): Observable<any> {
    return this.httpService.put(
      `${environment.base_url}api/busroutes/scheduled/${route_id}/activate`
    );
  }

  // medication codes api fetch
  getMedicationCodes(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/MedicationCodes`);
  }

  getMianJobCategory(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/mjcategories`);
  }

  getSubJobCategoryMainJobBased(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/categorymappings/${id}/sjcategories`
    );
  }

  getSubJobCategoryBasedOnRequestType(id, requestTypeValue): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api//jobrequests/${id}/sjcategories`
    );
  }

  getLastEmployeeNumber(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/managestaff/lastEmployeeNo`
    );
  }

  getCategoryById(id, id2): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/categorymappings/${id}/sjcategories/${id2}`
    );
  }

  getCategoryTranportMappingList(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/categorymappings`);
  }

  addUpadteCategoryTransport(data, action): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/categorymappings/add`,
      data
    );
  }

  addUpadteTower(data, url, action): Observable<any> {
    if (action === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  getTowerById(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/towers/${id}`);
  }

  getMedicationById(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/MedicationCodes/${id}`
    );
  }
  getLevels(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/levels`);
  }

  addUpadteLevels(data, url, action): Observable<any> {
    if (action === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  getLevelsById(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/levels/${id}`);
  }
  getLocations(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/locations`);
  }
  getCoSyDepLocations(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/costCenter/locations`);
  }
  getLocationsBySubjob(externalLoc): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/locations?externalLoc=${externalLoc}`
    );
  }
  getLocationType(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/location-management/type`
    );
  }
  getPriority(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/location-management/priority`
    );
  }
  getLocationById(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/locations/${id}`);
  }

  getLevelsByTowerId(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/towers/${id}/levels`
    );
  }

  getStandardTime(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/timeconfigurations`
    );
  }

  getStandardTimeById(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/timeconfigurations/${id}`
    );
  }

  addStandardTime(data, actiontype, url): Observable<any> {
    if (actiontype === "add") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  deleteStandardTime(id): Observable<any> {
    return this.httpService.delete(
      `${environment.base_url}api/timeconfigurations/delete/${id}`
    );
  }

  getDistanceConfigTowers(): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/distanceconfigurations/towerlevels`
    );
  }

  addDistanceConfiguration(data, url, actiontype): Observable<any> {
    if (actiontype === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  getDistanceList() {
    return this.httpService.get(
      `${environment.base_url}api/distanceconfigurations`
    );
  }
  getDistanceListById(id) {
    return this.httpService.get(
      `${environment.base_url}api/distanceconfigurations/${id}`
    );
  }
  addBusRoute(data, url, actiontype): Observable<any> {
    if (actiontype === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  getBusRoute(data?) {
    return this.httpService.post(
      `${environment.base_url}api/busroutes/scheduled`, data
    );
  }
  getBusRouteById(id, data?) {
    return this.httpService.post(
      `${environment.base_url}api/busroutes/scheduled/${id}`, data
    );
  }
  getManageMobile() {
    return this.httpService.get(`${environment.base_url}api/managephones`);
  }
  getManageMobileById(id) {
    return this.httpService.get(
      `${environment.base_url}api/managephones/${id}`
    );
  }
  addUpdateManageMobile(data, url, actiontype): Observable<any> {
    if (actiontype === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  deleteManageMobile(id): Observable<any> {
    return this.httpService.delete(
      `${environment.base_url}api/managephones/delete/${id}`
    );
  }
  getCannisterId() {
    return this.httpService.get(`${environment.base_url}api/CannisterIds`);
  }
  deleteCannisterId(id): Observable<any> {
    return this.httpService.delete(
      `${environment.base_url}api/CannisterIds/delete/${id}`
    );
  }
  addUpdateCannisterId(data, url, actiontype): Observable<any> {
    if (actiontype === "save") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  getListAuditReport(value): Observable<any> {
    return this.httpService.post(
      `${environment.base_url + "/api/auditreports"}`,
      value
    );
  }

  getUserDepartments() {
    return this.httpService.get(`${environment.base_url}api/user/departments`)
  }

  getPorterMapLocations(isMapped?) {
    return this.httpService.get(`${environment.base_url}api/portermaps/locations?isMapped=${isMapped || false}`);
  }

  getStationMappingLocations(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/stationmappings/locations`);
  }

  getApprovalStatus(filterData) {
    return this.httpService.get(`${environment.base_url}api/approvals?fromDate=${filterData.fromDate}&toDate=${filterData.toDate}&status=${filterData.status}`);
  }

  updateApprovals(type: any, data?: any) {
    data.approvals.map(res => {
      delete res['module'];
      delete res['moduleId'];
      delete res['newName'];
      delete res['newStatus'];
      delete res['oldName'];
      delete res['oldStatus'];
      delete res['reason'];
      delete res['requestedBy'];
      delete res['requestedDate'];
      delete res['select'];
      res['status'] = type == 'approve' ? 1 : 2;
    });
    return this.httpService.put(`${environment.base_url}api/approvals/update`, data);
  }
}
