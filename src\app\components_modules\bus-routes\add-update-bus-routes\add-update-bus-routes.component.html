<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()"
                >keyboard_backspace</em
              >
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">
                    Add Bus Route
                  </p>
                  <p *ngIf="activatedRoute.snapshot.params.id">
                    Update Bus Route
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="busRouteForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-6">
                          <br />
                          <mat-label
                            >Bus Route Type<span class="error-css"
                              ><span class="error-css">*</span></span
                            ></mat-label
                          >
                          <mat-radio-group
                            formControlName="route_type"
                            class="alignRadioBtn"
                          >
                            <mat-radio-button
                              class="example-margin"
                              value="Ad-hoc"
                              (click)="adhocClicked()"
                              >Ad-hoc
                            </mat-radio-button>
                            <mat-radio-button
                              class="example-margin"
                              value="Scheduled"
                            >
                              Scheduled</mat-radio-button
                            >
                          </mat-radio-group>
                          <br />
                          <mat-error class="pull-left error-css">
                            <app-error-message
                              [control]="busRouteForm.controls.route_type"
                              [fieldName]="'Bus Route Type'"
                              [fieldType]="'select'"
                            >
                            </app-error-message>
                          </mat-error>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label
                              >Department<span class="error-css"
                                ><span class="error-css">*</span></span
                              ></mat-label
                            >
                            <mat-select formControlName="department_id">
                              <mat-option
                                *ngFor="
                                  let department of departmentList$"
                                [value]="department.departmentId"
                                >{{ department.departmentName }}</mat-option
                              >
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="busRouteForm.controls.department_id"
                                [fieldName]="'Department'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6 Sequential">
                          <mat-label style="float: left"
                            ><b>Is Sequential :</b>
                          </mat-label>
                          <mat-checkbox
                            class="align-checkbox"
                            formControlName="is_sequential"
                          >
                          </mat-checkbox>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label
                              >Bus Route Name<span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <input
                              matInput
                              placeholder="Bus Route Name"
                              formControlName="route_name"
                            />
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="busRouteForm.controls.route_name"
                                [fieldName]="'Bus Route Name'"
                                [fieldType]="'enter'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker
                            [rangePicker]="true"
                            [label]="'Start Time'"
                            [control]="busRouteForm.controls.start_time"
                            [fieldName]="'start time'"
                            [fieldType]="'select'"
                          >
                          </app-time-range-picker>
                        </div>
                        <div class="col-12">
                          <hr />
                        </div>
                        <div class="col-12">
                          <div cdkDropListGroup>
                            <div class="row">
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Locations</h4>
                                  <div
                                    cdkDropList
                                    [cdkDropListData]="locations"
                                    class="example-list"
                                    (cdkDropListDropped)="drop($event)"
                                  >
                                    <div
                                      class="example-box"
                                      *ngFor="let item of locations"
                                      cdkDrag
                                    >
                                      {{ item.location_name }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="col-2">
                                <div class="forward-example">
                                  <p class="border-forward">></p>
                                  <p class="border-forward">>></p>
                                </div>
                                <div class="forward-example">
                                  <p class="border-forward">{{ "<" }}</p>
                                  <p class="border-forward">{{ "<<" }}</p>
                                </div>
                              </div>
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Selected Locations</h4>
                                  <div
                                    cdkDropList
                                    [cdkDropListData]="selectedLocations"
                                    class="example-list"
                                    (cdkDropListDropped)="drop($event)"
                                  >
                                    <ng-container
                                      *ngFor="
                                        let item of selectedLocations;
                                        let first = first;
                                        let last = last
                                      "
                                    >
                                      <div
                                        class="example-box"
                                        cdkDrag
                                        [ngClass]="{ blue: last, red: first }"
                                      >
                                        {{ item.location_name }}
                                      </div>
                                    </ng-container>
                                  </div>
                                </div>
                                <mat-error
                                  class="pull-left error-css"
                                  *ngIf="selectedLocations.length < 2"
                                >
                                  <p>Please drag minimum two location</p>
                                </mat-error>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-12">
                          <hr />
                        </div>
                        <div
                          class="col-6"
                          *ngIf="
                            busRouteForm.get('route_type').value === 'Scheduled'
                          "
                        >
                          <mat-form-field>
                            <mat-label> Working Weekdays </mat-label>
                            <mat-select
                              formControlName="working_weekdays"
                              multiple
                              class="filter-select"
                            >
                              <mat-option (click)="selectAll()" value="check"
                                >CheckAll
                              </mat-option>
                              <mat-option
                                *ngFor="let days of weeks"
                                [value]="days"
                              >
                                {{ days | titlecase }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label> Staff </mat-label>
                            <mat-select formControlName="staff">
                              <mat-option
                                *ngFor="let val of staff"
                                [value]="val.staff_id"
                                >{{ val.staff_name }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-6 example-margin-btn">
                          <br />
                          <mat-radio-group formControlName="active">
                            <mat-radio-button value="true"
                              >Active
                            </mat-radio-button>
                            <mat-radio-button value="false">
                              Inactive</mat-radio-button
                            >
                          </mat-radio-group>
                          <mat-error class="pull-left error-css">
                            <app-error-message
                              [control]="busRouteForm.controls.active"
                              [fieldName]="'Status'"
                              [fieldType]="'select'"
                            >
                            </app-error-message>
                          </mat-error>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Remarks </mat-label>
                            <textarea
                              matInput
                              #desc
                              maxlength="300"
                              placeholder="Remarks"
                              formControlName="remarks"
                              rows="1"
                            ></textarea>
                            <mat-hint style="text-align: end"
                              >{{ desc.value.length }} / 300
                            </mat-hint>
                          </mat-form-field>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button
                        mat-raised-button
                        *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right"
                        (click)="saveBusRoute('save')"
                      >
                        Submit
                      </button>
                      <button
                        mat-raised-button
                        *ngIf="activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right"
                        (click)="saveBusRoute('update')"
                      >
                        Update
                      </button>
                      <button
                        mat-raised-button
                        (click)="
                          busRouteForm.reset();
                          busRouteForm.controls.active.setValue('true');
                          getBusRouteById()
                        "
                        class="btn btn-white pull-right"
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
