$table-breakpoint: 480px;
$table-background-color: #fff;
$table-text-color: #024457;
$table-outer-border: 1px solid #167f92;
$table-cell-border: 1px solid #d9e4e6;

$table-border-radius: 10px;
$table-highlight-color: #eaf3f3;
$table-header-background-color: #167f92;
$table-header-text-color: #fff;
$table-header-border: 1px solid #fff;

.container-table {
  position: fixed;
  background: #ffffff;
  margin-top: 20px;
  margin-left: 30px;
  margin-right: 30px;
  margin-bottom: 20px;
  padding: 10px;
}

@mixin responstable(
  $breakpoint: $table-breakpoint,
  $background-color: $table-background-color,
  $text-color: $table-text-color,
  $outer-border: $table-outer-border,
  $cell-border: $table-cell-border,
  $border-radius: none,
  $highlight-color: none,
  $header-background-color: $table-background-color,
  $header-text-color: $table-text-color,
  $header-border: $table-cell-border
) {
  .responstable {
    width: 100%;
    overflow: hidden;
    background: $background-color;
    color: $text-color;
    border-radius: $border-radius;
    border: $outer-border;

    tr {
      border: $cell-border;

      &:nth-child(odd) {
        background-color: $highlight-color;
      }
    }

    th {
      display: none;
      border: $header-border;
      background-color: $header-background-color;
      color: $header-text-color;
      padding: 1em;

      &:first-child {
        display: table-cell;
        text-align: center;
      }

      &:nth-child(2) {
        display: table-cell;

        span {
          display: none;
        }

        &:after {
          content: attr(data-th);
        }
      }

      @media (min-width: $breakpoint) {
        &:nth-child(2) {
          span {
            display: block;
          }

          &:after {
            display: none;
          }
        }
      }
    }

    td {
      display: block;
      word-wrap: break-word;
      max-width: 7em;

      &:first-child {
        display: table-cell;
        text-align: center;
        border-right: $cell-border;
      }

      @media (min-width: $breakpoint) {
        border: $cell-border;
      }
    }

    th,
    td {
      text-align: left;
      margin: 0.5em 1em;

      @media (min-width: $breakpoint) {
        display: table-cell;
        padding: 1em;
      }
    }
  }
}

#pdf-download {
  margin-top: 40% !important;
}

@include responstable(
  $border-radius: $table-border-radius,
  $highlight-color: $table-highlight-color,
  $header-background-color: $table-header-background-color,
  $header-text-color: $table-header-text-color,
  $header-border: $table-header-border
);

body {
  padding: 0 2em;
  color: #024457;
  background: #f2f2f2;
}

h1 {
  font-weight: normal;
  color: #024457;

  span {
    color: #167f92;
  }
}

.font-class {
  font-size: x-small;
}
