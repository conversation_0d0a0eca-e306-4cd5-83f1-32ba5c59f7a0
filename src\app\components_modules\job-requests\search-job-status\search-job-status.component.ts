import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatSort } from "@angular/material";
import { JobRequests } from "src/app/models/JobRequests";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { ToastrService } from "ngx-toastr";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import * as moment from "moment";
import { FormControl } from "@angular/forms";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
  selector: "app-search-job-status",
  templateUrl: "./search-job-status.component.html",
  styleUrls: ["../../../scss/table.scss", "./search-job-status.component.scss"],
})
export class SearchJobStatusComponent implements OnInit {
  displayedColumns: string[] = [
    "order_no",
    "request_time",
    "from_location",
    "to_location",
    "transport_mode_name",
    "task",
    "patient_name",
    "porter_name",
    "request_type",
  ];
  public locationFilterCtrl: FormControl = new FormControl();

  pdfData = [];
  jobSearch = {
    jobType: "Normal",
    fromDate: new Date().toUTCString(),
    toDate: new Date().toUTCString(),
  };
  taskRequest = {
    patient: null,
    location_id: null,
    date: "",
    job_category: null,
  };
  dataSource: MatTableDataSource<JobRequests>;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  locationsList: any = [];
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  constructor(
    private readonly jobService: JobsService,
    private readonly loader: LoaderService,
    private readonly toastr: ToastrService,
    public facilityConfig: FacilityConfigService
  ) {}

  ngOnInit() {
    this.dataSource = new MatTableDataSource([]);
    this.getLocations();
    // real time search location
    this.filteredLocations.next(this.locationsList.slice());
    this.locationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterLocations();
      });
  }
  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    // this.taskRequest.fromLocationId =
    //   this.locations.length > 1 ? this.locations.slice()[0].id : "";
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "jobrequesttable",
        "jobrequesttable"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("jobrequesttable", "jobrequesttable");
    }
  }

  getjobRequests() {
    var values = Object.keys(this.taskRequest).map((e) => this.taskRequest[e]);
    if (values.length) {
      const requestData = {} as any;
      requestData.location_id = this.taskRequest.location_id
        ? Number(this.taskRequest.location_id)
        : null;
      requestData.date = this.taskRequest.date
        ? moment(this.taskRequest.date).format("YYYY-MM-DD")
        : "";
      requestData.patient = this.taskRequest.patient
        ? this.taskRequest.patient
        : null;
      requestData.job_category = this.taskRequest.job_category;
      this.jobService.getJobRequestByidSearch(requestData).subscribe((res) => {
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.sort = this.sort;
        this.setPageSizeOptions();
        // this.applyFilter(id);
      });
    } else {
      this.dataSource = new MatTableDataSource([]);
    }
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  dateStructure(date) {
    return date.replace("T", " ");
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Job No": key.order_no ? key.order_no : "--",
              Creation: key.request_time ? key.request_time : "--",
              From: key.from_location ? key.from_location : "--",
              To: key.to_location ? key.to_location : "--",
              Equipment: key.transport_mode_name
                ? key.transport_mode_name
                : "--",
              Task: key.task ? key.task : "--",
              Urgent: key.request_type === "emergency" ? "Yes" : "No",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locationsList = res;
      this.filterLocations();
    });
  }
  clearFormValue(formField: string) {
    this.taskRequest[formField] = "";
  }
}
