import { Component, OnInit, OnDestroy } from '@angular/core';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import * as moment from 'moment';
import { ChartOptions, ChartDataSets } from 'chart.js';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';

@Component({
  selector: 'app-cp-job-type',
  templateUrl: './cp-job-type.component.html',
  styleUrls: ['../../../../scss/table.scss', './cp-job-type.component.scss']
})
export class CpJobTypeComponent implements OnInit, OnDestroy {
  dates;
  chartsGraph = false;
  headers = ['YTD AVG', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  rows = ['ytd', 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec',
  ];
  pdfData = [];
  subscription: any;
  today = new Date();
  currentYear = this.today.getFullYear();
  years = [this.currentYear, this.currentYear - 1];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');
  barChartOptions: ChartOptions = {
    title: {
      text: `Central Pool Job Type ${this.inputValidation.monthConvert(this.dates ? this.dates.month : this.currMonth)}-${this.dates ?
        this.dates.year : this.currYear}`,
      display: true
    },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }],
    }
  };
  barChartLegend = true;
  barChartPlugins = [];
  barChartData: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  constructor(
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
    private readonly userService: UserService,
    private readonly inputValidation: InputValidationService
  ) {
  }

  ngOnInit() {
    this.filterDtaes();
  }

  getspJobType(year, month) {
    this.chartsGraph = false;
    this.reportsService.getkpiReportsCpJobType('cpjobtype', year, month, 'Emergency').subscribe(res => {
      this.pdfData[0] = res || [];
      this.reportsService.getkpiReportsCpJobType('cpjobtype', year, month, 'PM').subscribe(res1 => {
        this.pdfData[1] = res1 || [];
        this.reportsService.getkpiReportsCpJobType('cpjobtype', year, month, 'NPM').subscribe(res2 => {
          this.pdfData[2] = res2 || [];
          this.reportsService.getkpiReportsCpJobType('cpjobtype', year, month, 'Total').subscribe(res3 => {
            this.pdfData[3] = res3 || [];
          });
        });
      });
    });
    this.userService.getDashboardData(`api/dashboard/cpjobtype/${year}/${month}`).subscribe(res => {
      this.barChartData = [{ data: [0], label: '' }];
      if (res && res.length) {
        this.barChartData = [];
        res.forEach(reason => {
          this.barChartData.push(
            {
              data: [reason.count ? reason.count : 0],
              label: `${reason.type}  (${reason.count ? reason.count : 0})`
            }
          );
        });
      }
      this.barChartOptions.title.text = `Central Pool Job Type ${this.inputValidation.monthConvert(month)} - ${year}`;
      this.chartsGraph = true;
    });
  }

  filterDtaes() {
    this.subscription = this.reportsService.searchDates.subscribe(res => {
      this.dates = res;
      if (res.year && res.month) {
        this.getspJobType(res.year, res.month);
      }
    });
  }


  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('centralpooltable', 'centralpooltable');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('centralpooltable', 'centralpooltable');
    }
  }
  setPageSizeOptions() {
    this.pdfData = this.pdfData.filter(key => {
      const a = {
        '': key.item,
        ytd: key.ytd,
        jan: key.jan,
        feb: key.feb,
        mar: key.mar,
        apr: key.apr,
        may: key.may,
        jun: key.jun,
        jul: key.jul,
        aug: key.aug,
        oct: key.oct,
        nov: key.nov,
        dec: key.dec
      };
      Object.assign(key, a);
      return key;
    }) || [];
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
