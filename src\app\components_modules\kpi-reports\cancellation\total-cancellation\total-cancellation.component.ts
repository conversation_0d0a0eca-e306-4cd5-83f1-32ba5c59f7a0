import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ChartOptions, ChartDataSets } from 'chart.js';
import { TotalWorkload } from '../../../../models/totalWorkload';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import * as moment from 'moment';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
@Component({
  selector: 'app-total-cancellation',
  templateUrl: './total-cancellation.component.html',
  styleUrls: ['./total-cancellation.component.scss', '../../../../scss/table.scss']
})
export class TotalCancellationComponent implements OnInit, OnDestroy {
  dates;
  chartsGraph = false;
  pdfData = [];
  subscription: any;
  today = new Date();
  currentYear = this.today.getFullYear();
  years = [this.currentYear, this.currentYear - 1];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');
  displayedColumns: string[] = ['item', 'ytd', 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
  dataSource = new MatTableDataSource<TotalWorkload>();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  barChartOptions: ChartOptions = {
    title: {
      text: `Total Cancellation ${this.inputValidation.monthConvert(this.dates ? this.dates.month : this.currMonth)}-
      ${this.dates ? this.dates.year : this.currYear}`,
      display: true
    },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }],
    }
  };
  barChartLegend = true;
  barChartPlugins = [];
  barChartData: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  constructor(
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
    private readonly userService: UserService,
    private readonly inputValidation: InputValidationService
  ) {
  }

  ngOnInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.filterDtaes();
  }

  getWorkloads(year, month) {
    this.chartsGraph = false;
    this.reportsService.getkpiReports('cancellation', year, month).subscribe(data => {
      this.dataSource = new MatTableDataSource(data ? data : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
      this.userService.getDashboardData(`api/kpi/cancelgraph/${year}/${month}`).subscribe(res => {
        this.barChartData = [{ data: [0], label: '' }];
        if (res && res.length) {
          this.barChartData = [];
          res.forEach(reason => {
            this.barChartData.push(
              {
                data: [reason.count ? reason.count : 0],
                label: `${reason.type}  (${reason.count ? reason.count : 0})`
              }
            );
          });
        }
        this.barChartOptions.title.text = `Total Cancellation ${this.inputValidation.monthConvert(month)} - ${year}`;
        this.chartsGraph = true;
      });
    });
  }

  filterDtaes() {
    this.subscription = this.reportsService.searchDates.subscribe(res => {
      this.dates = res;
      if (res.year && res.month) {
        this.getWorkloads(res.year, res.month);
      }
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('totalcancellation_table', 'totalcancellation_table');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('totalcancellation_table', 'totalcancellation_table');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          Cancellation: key.item,
          'Ytd Avg': key.ytd,
          Jan: key.jan,
          Feb: key.feb,
          Mar: key.mar,
          Apr: key.apr,
          May: key.may,
          Jun: key.jun,
          Jul: key.jul,
          Aug: key.aug,
          Sep: key.sep,
          Oct: key.oct,
          Nov: key.nov,
          Dec: key.dec
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
