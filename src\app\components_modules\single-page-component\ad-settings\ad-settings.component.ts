import { Component, OnInit } from "@angular/core";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-ad-settings",
  templateUrl: "./ad-settings.component.html",
  styleUrls: ["./ad-settings.component.scss"],
})
export class AdSettingsComponent implements OnInit {
  ldapForm: FormGroup;
  fileupload: File = null;
  imageUrl: any;
  fetchJson: any;

  constructor(
    private readonly userService: UserService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService
  ) {
    this.ldapForm = this.fb.group({
      // ad_id: '',
      // ad_status: 'false',
      // ad_url1: ['', Validators.required],
      // ad_url2: ['', Validators.required],
      // ad_url3: ['', Validators.required],
      baseUrl: ["", Validators.required],
      // ad_url4: '',
      // ad_url5: '',
      ad_imgurl: [""],
      created_by: "",
      created_date: "",
      last_modified_by: "",
      last_modified_date: "",
      ad_status: ["", Validators.required],
      ad_settings: this.fb.array([]),
    });
  }

  addUrlAppend(adUrl: Array<any>, ad_status, ad_image) {
    if (adUrl.length > 0) {
      const adSetting = this.ldapForm.controls["ad_settings"] as FormArray;
      const { ad_imgurl: image, ad_status: status } = this.ldapForm.controls;

      adUrl.forEach((data, i) =>
        adSetting.push(
          this.fb.group({ ad_name: [data.ad_url, Validators.required] })
        )
      );
      status.patchValue(ad_status);
      image.patchValue(ad_image);
    }
  }

  ngOnInit() {
    this.getAdts();
  }

  getAdts() {
    this.userService.getAds().subscribe((res) => {
      this.userService.isldap.next((res && res.ad_status) || false);
      if (res.ad_image) {
        this.imageUrl = { result: res.ad_image };
        this.ldapForm.get("baseUrl").clearValidators();
        this.ldapForm.get("baseUrl").updateValueAndValidity();
        this.userService.logoUrl.next(res.ad_image);
      }

      this.addUrlAppend(
        res.ad_settings,
        res.ad_status === true ? "true" : "false",
        res.ad_image
      );
      this.fetchJson = res;
    });
  }

  getFile(file) {
    this.fileupload = file;
    const oFReader = new FileReader();
    oFReader.readAsDataURL(this.fileupload);
    oFReader.onload = (oFREvent) => {
      this.imageUrl = oFREvent && oFREvent.target;
      this.ldapForm.get("ad_imgurl").setValue(this.imageUrl.result);
    };
  }

  adsSubmit() {
    if (this.ldapForm.valid) {
      this.ldapForm.get("ad_status").value === "true"
        ? this.ldapForm.get("ad_status").setValue(true)
        : this.ldapForm.get("ad_status").setValue(false);

      const adJson = this.fetchJson.ad_settings.map((sample, i) => {
        return {
          ...sample,
          ad_url: this.ldapForm.value.ad_settings[i].ad_name,
        };
      });
      this.userService
        .addUpdateAds({
          ...this.ldapForm.value,
          ad_image: this.ldapForm.value.ad_imgurl,
          ad_settings: adJson,
        })
        .subscribe(
          () => {
            this.toastr.success(
              "Active directory settings updated successfully",
              "Success"
            );
            this.getAdts();
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.ldapForm.markAllAsTouched();
    }
  }
}
