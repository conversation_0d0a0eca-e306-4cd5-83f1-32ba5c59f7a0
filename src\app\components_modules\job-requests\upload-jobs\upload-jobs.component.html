<div class="main-content h-100">
  <div class="h-100">
    <mat-toolbar class="toolbar__css mat-elevation-z4">
      <span class="">Upload Jobs</span>
    </mat-toolbar>

    <mat-card>
      <!-- style="height: calc(100% - 164px)" -->
      <div class="row h-100">
        <div class="col-12" style="height: 91%">
          <mat-tab-group [(selectedIndex)]="tabIndex" class="h-100">
            <mat-tab label="File" [disabled]="tabIndex !== 0">
              <ng-container *ngTemplateOutlet="fileUpload"> </ng-container>
            </mat-tab>
            <mat-tab label="Preview" [disabled]="tabIndex !== 1">
              <ng-container *ngTemplateOutlet="tableView"> </ng-container>
            </mat-tab>
            <mat-tab label="Done" [disabled]="tabIndex !== 2">
              <ng-container *ngTemplateOutlet="tableUploadedView">
              </ng-container>
            </mat-tab>
          </mat-tab-group>
        </div>

        <div class="col-12" *ngIf="tabIndex == 1">
          <button
            mat-raised-button
            color="primary"
            (click)="uploadCsv()"
            class="float-right"
          >
            Upload
          </button>
        </div>
        <div class="col-12" *ngIf="tabIndex == 2">
          <button
            mat-raised-button
            color="primary"
            (click)="uploadNewJob()"
            class="float-right"
          >
            Upload New Job
          </button>
        </div>
      </div>
    </mat-card>
  </div>
</div>

<ng-template #fileUpload>
  <mat-card>
    <div class="row">
      <div class="col-6">
        <div class="pt-4">
          <a
            mat-raised-button
            color="primary"
            href="assets/file/Sample job request sheet.xlsx"
            color="primary"
          >
            Download Sample Template
          </a>
        </div>
      </div>
      <div class="col-6 mt-3">
        <app-file-picker
          [control]="fileForm.controls.file"
          [fieldName]="'CSV'"
          [fieldType]="'select'"
          [allowedExtensions]="['csv', 'text/csv']"
          [fileupload]="csvFile"
          [maxMB]="52428800"
          [maxSize]="50"
          (selectedFile)="getMediaFile($event)"
          [required]="false"
        >
        </app-file-picker>
      </div>
      <div class="col-12">
        <button
          mat-raised-button
          color="primary"
          (click)="getUploadedFile()"
          class="proceed_btn mt-4"
        >
          Proceed To Preview Data
        </button>
      </div>
    </div>
    <!-- <mat-divider class="divider"></mat-divider> -->
   
  </mat-card>
  <div class="text-alignment">
    <div class="col-6">
        <dl
          class="important__note font-italic pt-4" style="margin-bottom: -.5rem;">
          <dt style="color: red">
            To upload the jobs in bulk, you have to follow these steps:
          </dt>
        </dl>
          <div class="row">
            <div class="col-12">
              <div class="table-responsive table-full-width"></div>
              <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="7">Step 1: Download the sample template here.</th>
                </tr>
              </thead>
              <tbody >
                <td colspan="7">Download sample file</td>
              </tbody>
              </table>
              <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="7">Step 2: Fill out the information in respective columns and save the tab into a separate file with .csv extension.</th>
                </tr>
              </thead>
              <tbody >
                <ng-container>
                  <tr>
                    <td colspan="7">For eg: Sample_Job_RequestFile.csv</td>
                  </tr>
                </ng-container>
              </tbody>
              </table>
              <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="7">Step 3: Upload the CSV file here to view the data in a preview mode.</th>
                </tr>
              </thead>
              <tbody >
                <ng-container>
                  <td colspan="7"><span>CSV Upload</span><br><span style="color: green;">Note: If there is any formatting issue with the data or invalid data, that particular job will not be created.</span></td>
                </ng-container>
              </tbody>
              </table>
              <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="7">Step 4: You have successfully uploaded the jobs in bulk.</th>
                </tr>
              </thead>
              <tbody >
                <ng-container>
                  <td colspan="7">CSV - Upload</td>
                </ng-container>
              </tbody>
              </table>
            </div>
        </div>
      </div>
  </div>
</ng-template>

<ng-template #tableView>
  <div class="row no-gutters">
    <div class="col-12">
      <table
        aria-describedby="preview table"
        mat-table
        [dataSource]="dataSource"
        class="mat-elevation-z8"
        class="w-100"
      >
        <!--- Note that these columns can be defined in any order.
            The actual rendered columns are set as a property on the row definition" -->

        <ng-container matColumnDef="valid">
          <th mat-header-cell *matHeaderCellDef id="col">Correct Data</th>
          <td mat-cell *matCellDef="let element">
            <span
              class="dot"
              [matTooltip]="element.info ? element.info : 'All data is good'"
              [ngStyle]="{ backgroundColor: element.valid ? 'green' : 'red' }"
            ></span>
          </td>
        </ng-container>

        <!-- Position Column -->
        <ng-container matColumnDef="due_date">
          <th mat-header-cell *matHeaderCellDef id="col">Due Date</th>
          <td mat-cell *matCellDef="let element">
            {{ element.due_date ? element.due_date : "-" }}
          </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="equipment">
          <th mat-header-cell *matHeaderCellDef scope="col">Equipment</th>
          <td mat-cell *matCellDef="let element">
            {{ element.equipment ? element.equipment : "-" }}
          </td>
        </ng-container>

        <!-- Weight Column -->
        <ng-container matColumnDef="from_location">
          <th mat-header-cell *matHeaderCellDef scope="col">From Location</th>
          <td mat-cell *matCellDef="let element">
            {{ element.from_location ? element.from_location : "-" }}
          </td>
        </ng-container>

        <!-- isolation_precaution Column -->
        <ng-container matColumnDef="isolation_precaution">
          <th scope="col" mat-header-cell *matHeaderCellDef>
            Isolation Precaution
          </th>
          <td mat-cell *matCellDef="let element">
            {{
              element.isolation_precaution ? element.isolation_precaution : "-"
            }}
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="job_category">
          <th scope="col" mat-header-cell *matHeaderCellDef>Job Category</th>
          <td mat-cell *matCellDef="let element">
            {{ element.job_category ? element.job_category : "-" }}
          </td>
        </ng-container>

        <!-- job_type Column -->
        <ng-container matColumnDef="job_type">
          <th scope="col" mat-header-cell *matHeaderCellDef>Job Type</th>
          <td mat-cell *matCellDef="let element">
            {{ element.job_type ? element.job_type : "-" }}
          </td>
        </ng-container>

        <!-- nric Column -->
        <ng-container matColumnDef="nric">
          <th scope="col" mat-header-cell *matHeaderCellDef>NRIC</th>
          <td mat-cell *matCellDef="let element">
            {{ element.nric ? element.nric : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="patient_name">
          <th scope="col" mat-header-cell *matHeaderCellDef>Patient Name</th>
          <td mat-cell *matCellDef="let element">
            {{ element.patient_name ? element.patient_name : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="request_type">
          <th scope="col" mat-header-cell *matHeaderCellDef>Request Type</th>
          <td mat-cell *matCellDef="let element">
            {{ element.request_type ? element.request_type : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="to_location">
          <th scope="col" mat-header-cell *matHeaderCellDef>To Location</th>
          <td mat-cell *matCellDef="let element">
            {{ element.to_location ? element.to_location : "-" }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          [ngStyle]="{ border: row.valid ? '' : '2px solid red' }"
        ></tr>
      </table>
    </div>
  </div>
</ng-template>

<ng-template #tableUploadedView>
  <div class="row no-gutters">
    <div class="col-12">
      <table
        aria-describedby="preview table"
        mat-table
        [dataSource]="dataSourceSuccess"
        class="mat-elevation-z8"
        class="w-100"
      >
        <!--- Note that these columns can be defined in any order.
            The actual rendered columns are set as a property on the row definition" -->

        <ng-container matColumnDef="valid">
          <th mat-header-cell *matHeaderCellDef id="col">Correct Data</th>
          <td mat-cell *matCellDef="let element">
            <mat-icon> {{ element.valid ? "check_circle" : "clear" }}</mat-icon>
          </td>
        </ng-container>

        <!-- Position Column -->
        <ng-container matColumnDef="due_date">
          <th mat-header-cell *matHeaderCellDef id="col">Due Date</th>
          <td mat-cell *matCellDef="let element">
            {{ element.due_date ? element.due_date : "-" }}
          </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="equipment">
          <th mat-header-cell *matHeaderCellDef scope="col">Equipment</th>
          <td mat-cell *matCellDef="let element">
            {{ element.equipment ? element.equipment : "-" }}
          </td>
        </ng-container>

        <!-- Weight Column -->
        <ng-container matColumnDef="from_location">
          <th mat-header-cell *matHeaderCellDef scope="col">From Location</th>
          <td mat-cell *matCellDef="let element">
            {{ element.from_location ? element.from_location : "-" }}
          </td>
        </ng-container>

        <!-- isolation_precaution Column -->
        <ng-container matColumnDef="isolation_precaution">
          <th scope="col" mat-header-cell *matHeaderCellDef>
            Isolation Precaution
          </th>
          <td mat-cell *matCellDef="let element">
            {{
              element.isolation_precaution ? element.isolation_precaution : "-"
            }}
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="job_category">
          <th scope="col" mat-header-cell *matHeaderCellDef>Job Category</th>
          <td mat-cell *matCellDef="let element">
            {{ element.job_category ? element.job_category : "-" }}
          </td>
        </ng-container>

        <!-- job_type Column -->
        <ng-container matColumnDef="job_type">
          <th scope="col" mat-header-cell *matHeaderCellDef>Job Type</th>
          <td mat-cell *matCellDef="let element">
            {{ element.job_type ? element.job_type : "-" }}
          </td>
        </ng-container>

        <!-- nric Column -->
        <ng-container matColumnDef="nric">
          <th scope="col" mat-header-cell *matHeaderCellDef>NRIC</th>
          <td mat-cell *matCellDef="let element">
            {{ element.nric ? element.nric : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="patient_name">
          <th scope="col" mat-header-cell *matHeaderCellDef>Patient Name</th>
          <td mat-cell *matCellDef="let element">
            {{ element.patient_name ? element.patient_name : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="request_type">
          <th scope="col" mat-header-cell *matHeaderCellDef>Request Type</th>
          <td mat-cell *matCellDef="let element">
            {{ element.request_type ? element.request_type : "-" }}
          </td>
        </ng-container>

        <!-- patient_name Column -->
        <ng-container matColumnDef="to_location">
          <th scope="col" mat-header-cell *matHeaderCellDef>To Location</th>
          <td mat-cell *matCellDef="let element">
            {{ element.to_location ? element.to_location : "-" }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </div>
</ng-template>
