@import "../../../../assets/scss/navbar.scss";
.main-content {
  width: 55%;
  margin: 10px auto;
}
.mat-radio-button ~ .mat-radio-button {
  margin-left: 16px;
}

.col-6 {
  flex: 0 0 100% !important;
}

button {
  cursor: pointer;
}
.alignInput {
  float: left;
  display: inline-flex;
  mat-checkbox {
    padding-right: 20px;
  }
}
mat-checkbox-inner-container {
  height: 20px;
  width: 20px;
}
.nav li {
  width: auto;
}
.nav-tabs-wrapper {
  display: inline-flex;
  em {
    float: left;
    cursor: pointer;
    margin-top: 10px;
  }
}
