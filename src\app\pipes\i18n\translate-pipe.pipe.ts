import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from 'src/app/Apiservices/translate/translate.service';

@Pipe({
  name: 'translatePipe',
  pure: false
})
export class TranslatePipePipe implements PipeTransform {

  constructor(
    private readonly translate: TranslateService
  ) { }
  transform(value: any, ...args: any[]): any {
    return this.translate.getLang(value);
  }

}
