import { TranslatePipePipe } from './translate-pipe.pipe';
import { DomSanitizer } from '@angular/platform-browser';
import { inject, TestBed } from '@angular/core/testing';
import { JasmineDependencyModule } from 'src/app/jasmine-dependency/jasmine-dependency.module';
import { TranslateService } from 'src/app/Apiservices/translate/translate.service';

describe('TranslatePipePipe', () => {
  beforeEach(() => {
    TestBed
      .configureTestingModule({
        imports: [
          JasmineDependencyModule
        ]
      });
  });

  it('create an instance', inject([DomSanitizer], (translateService: TranslateService) => {
    const pipe = new TranslatePipePipe(translateService);
    expect(pipe).toBeTruthy();
  }));
});
