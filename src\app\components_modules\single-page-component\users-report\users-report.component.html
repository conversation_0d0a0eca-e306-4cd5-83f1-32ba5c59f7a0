<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('pdf')"
                    >
                      PDF
                    </p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Users</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin"></div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table class="table">
                      <thead>
                        <tr>
                          <th scope="col">Users</th>
                          <th scope="col" *ngFor="let roles of roles$ | async">
                            {{ roles?.role_name }}
                          </th>
                          <th scope="col">Last Logged in date</th>
                          <th scope="col">Last Login Location</th>
                          <th scope="col">Created Date</th>
                          <th scope="col">Created By</th>
                          <th scope="col">Remarks</th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container
                          *ngFor="let module of userRoleDataMapping$ | async"
                        >
                          <tr>
                            <td>
                              {{ module?.username }}
                            </td>
                            <ng-container
                              *ngFor="let roleModule of module?.userRoleAssign"
                            >
                              <td>
                                <mat-icon>{{
                                  roleModule ? "done" : "maximize"
                                }}</mat-icon>
                              </td>
                            </ng-container>
                            <td>
                              {{
                                module.last_login_date
                                  | localDateConversion: "full"
                              }}
                            </td>
                            <td>
                              {{ module.last_login_location }}
                            </td>
                            <td>
                              {{
                                module.created_date
                                  | localDateConversion: "full"
                              }}
                            </td>
                            <td>
                              {{ module.created_by }}
                            </td>
                            <td>
                              {{ module.remarks }}
                            </td>
                          </tr>
                        </ng-container>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="[
    'Users',
    allRoleForExcel$ | async,
    'Last Logged in date',
    'Last Login Location',
    'Created Date',
    'Created By',
    'Remarks'
  ]"
  [title]="'Users Report'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<!-- this is table of user report only to export excel -->
<table class="table" id="userReportDumb" style="display: none">
  <thead>
    <tr>
      <th scope="col">Users</th>
      <th scope="col" *ngFor="let roles of roles$ | async">
        {{ roles?.role_name }}
      </th>
      <th scope="col">Last Logged in date</th>
      <th scope="col">Last Login Location</th>
      <th scope="col">Created Date</th>
      <th scope="col">Created By</th>
      <th scope="col">Remarks</th>
    </tr>
  </thead>
  <tbody>
    <ng-container *ngFor="let module of userRoleDataMapping$ | async">
      <tr>
        <td>
          {{ module?.username }}
        </td>
        <ng-container *ngFor="let roleModule of module?.userRoleAssign">
          <td>
            <mat-icon>{{ roleModule ? true : false }}</mat-icon>
          </td>
        </ng-container>
        <td>
          {{ module.last_login_date | localDateConversion: "full" }}
        </td>
        <td>
          {{ module.last_login_location }}
        </td>
        <td>
          {{ module.created_date | localDateConversion: "full" }}
        </td>
        <td>
          {{ module.created_by }}
        </td>
        <td>
          {{ module.remarks }}
        </td>
      </tr>
    </ng-container>
  </tbody>
</table>
