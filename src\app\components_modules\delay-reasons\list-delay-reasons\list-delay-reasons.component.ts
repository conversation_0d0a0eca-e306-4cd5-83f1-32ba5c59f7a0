import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';

export interface UserData {
  delay_reason: string;
  allow_remarks: string;
  status: string;
  delay_id: string;
}

@Component({
  selector: 'app-list-delay-reasons',
  templateUrl: './list-delay-reasons.component.html',
  styleUrls: ['./list-delay-reasons.component.scss', '../../../scss/table.scss']
})
export class ListDelayReasonsComponent implements OnInit {
  displayedColumns: string[] = ['delay_reason', 'allow_remarks', 'status', 'edit'];
  dataSource: MatTableDataSource<UserData>;
  isExcelClicked: any;
  pdfData = [];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly settingsSeervice: SettingsService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
  }


  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('delayReasonTable', 'delay_reasons');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.isExcelClicked = true;
      setTimeout(() => {
        this.isExcelClicked = TableUtil.exportToExcel('delayReasonTable', 'delay_reasons');
      }, 0);
    }
  }


  ngOnInit() {
    this.getDelayReasons();
  }

  getDelayReasons() {
    this.settingsSeervice.getDelayReasons().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }


  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Delay Reason': key.delay_reason,
          'Allow Remarks': key.allow_remarks ? 'Yes' : 'No',
          Status: key.status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to Continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatedelayreason/${data.delay_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatedelayreason/${data.delay_id}`], { relativeTo: this.activatedRoute });
    }
  }

}

