import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { JwtHelperService } from "@auth0/angular-jwt";
import { Observable } from "rxjs";
import { HttpService } from "../httpService/http.service";
import { environment } from "src/environments/environment";
import { StorageService } from "../stoargeService/storage.service";

@Injectable({
  providedIn: "root",
})
export class RoutingService {
  loggedInuser: any;
  constructor(
    private readonly router: Router,
    private readonly jwthelper: JwtHelperService,
    private readonly http: HttpService,
    private readonly storageService: StorageService
  ) {}

  next() {
    if (this.storageService.getData("access_token")) {
      this.router.navigateByUrl("/app/dashboard");
    }
  }

  currentUser(): Observable<any> {
    const authUrl = environment.base_url + "/loggedInuser";
    return this.http.post(authUrl, this.storageService.getData("access_token"));
  }

  setCurrentUser(value?: any) {
    if (this.storageService.getData("access_token")) {
      if (!value) {
        this.currentUser().subscribe((res) => {
          this.loggedInuser = res;
        });
      } else {
        if (this.loggedInuser) {
          this.loggedInuser = Object.assign(this.loggedInuser, value);
        } else {
          this.loggedInuser = value;
        }
      }
    } else {
      this.loggedInuser = null;
    }
  }

  decodeToken() {
    const token = this.storageService.getData("access_token");
    return this.jwthelper.decodeToken(token);
  }

  checkForExpiredToken() {
    const token = this.storageService.getData("access_token");
    return this.jwthelper.isTokenExpired(token);
  }

  urlAssignToMenu(menu) {
    return {
      ViewStatus: "/app/viewstatus",
      Home: "/app",
      "Job Request": "/Fe-Job Request",
      "View Status": "/app/jobstatus",
      "Search Jobs": "/app/search/searchjob",
      Towers: "/app/towers",
      Levels: "/app/levels",
      Locations: "/app/locations",
      Dashboard: "/app/dashboard",
      "External Dashboard": "/app/externaldashboard",
      Jobs: "/Jobs",
      "New Job": "/app/jobrequests/addjobrequest",
      "Facility Configuration": "/Facility Configuration",
      "Category Transport Mapping": "/app/categorytransportmapping",
      "Location Transport Mapping": "/app/stationtransportMapping",
      "Standard Time Configuration": "/app/standardtime",
      "Distance Configuration": "/app/distance",
      "Location Time Configuration": "/Location Time Configuration",
      "Bus Route Management": "/app/busroutes",
      "Mobile Number Management": "/app/manageMobile",
      "Equipment Maintenance": "/Equipment Maintenance",
      "Station View Status": "/app/station-view-status",
      Settings: "/Settings",
      "Active Directory": "/app/singlepage/ads",
      "Smart Assign": "/app/smartassign",
      "Help Files": "/app/managehelpfile",
      "Delay Reasons": "/app/delayreasons",
      "Message Templates": "/app/messagetemplates",
      "Cancel Message Templates": "/app/canceltemplates",
      // 'Grid View Settings': '/Grid View Settings',
      "Feedback Email Set Up": "/app/singlepage/feedbackEmail",
      "User Management": "/User Management",
      "Administrative Users": "/app/users",
      "Portering Staff": "/app/managestaff",
      "Porter Live Status": "/app/managestaff/porterlivestatus",
      "Approval Status": "/app/approval",
      Roles: "/app/roles",
      "Master Data Management": "/Master Data Management",
      "Mode of Transport": "/app/transportmodes",
      "Status Color Codes": "/app/colorcodes",
      'Manage Departments': '/app/manage-departments',
      'Station Departments': '/app/station-departments',
      'CoSy Cost Centers': '/app/cosy-departments',
      "Job Categories": "/app/mainjobcategory",
      "Job Sub Categories": "/app/subjobcategory",
      Actions: "/Actions",
      "Message Staff": "/app/messageStaff",
      "Assign Mobile#": "/Assign Mobile#",
      Reports: "/Reports",
      "KPI reports": "/app/kpiReports/workload",
      "ADT Patient Logs": "/app/singlepage/adtpatientlogs",
      "Map on/off porters": "/app/mapporters",
      "OnGoing Bus Routes": "/app/singlepage/ongoingbus",
      "Enhanced Reports": "/app/singlepage/enhancedReports",
      "Summary Reports": "/app/singlepage/transport-reports",
      "Porter Workload": "/app/singlepage/porterReports",
      "Search(ACK)": "/app/singlepage/enhancedReports",
      "Bus Route Reports": "/app/singlepage/busrouteSearch",
      "Monthly Reports": "/app/singlepage/monthlyraw-report",
      "PTS Reports": "/app/singlepage/pts-reports",
      "Location Reports": "/app/singlepage/enhanced-location-reports",
      "Message Reports": "/app/singlepage/messages-reports",
      "RTLS Message Reports": "/app/singlepage/rtls-messages-reports",
      Feedback: "/app/singlepage/feedbackList",
      "Station Reports": "/app/singlepage/stationReport",
      "Station Porter Reports": "/app/singlepage/stationReport",
      "Station Porter Specific Reports":
        "/app/singlepage/stationSpecificReport",
      "Cannister Id": "/app/cannister-id",
      "Grid View Settings": "/app/grid-view-settings",
      // medication route
      "Medication Codes": "/app/medication-codes",
      "View Status by location": "/app/view-status-by-location",
      "View Status by Job-category": "/app/view-status-by-job-category",
      "UAM reports": "/app/singlepage/uam-report",
      "Roles-reports": "/app/singlepage/role-report",
      Users: "/app/singlepage/user-report",
      "Disabled-Users": "/app/singlepage/disable-user-report",
      "Active-Users": "/app/singlepage/active-user-report",
      "Role-Modules": "/app/singlepage/uam-report",
      "Task Type Summary": "/app/singlepage/transport-reports",
      "Location Summary": "/app/singlepage/location-summary",
      "Equipment Summary": "/app/singlepage/equipment-move",
      "Hourly Summary": "/app/singlepage/hourly-summary",
      "KPI Report": "/app/singlepage/kpi-report",

      // KPI reports
      "Total Workload": "/app/kpiReports/workload",
      "CP Job Type": "/app/kpiReports/cpjobtype",
      "SP Job Type": "/app/kpiReports/spjobtype",
      "SP Workload by Location": "/app/kpiReports/spworkload",
      "Performance KPI": "/app/kpiReports/perfomancekpi",
      "Total Cancellation": "/app/kpiReports/totalcancellation",
      "Cancellation Reasons": "/app/kpiReports/cancellationreason",
      "Upload Jobs": "/app/jobrequests/upload-jobs",
      "Audit Reports": "/app/singlepage/audit-report",
      "KPI Summary":"/app/kpiReports/kpisummmary",
    }[menu];
  }

  navigateAsPerRole(role) {
    if (role === "admin") {
      this.router.navigate(["/admin"]);
    } else if (role === "appadmin") {
      this.router.navigate(["/appadmin"]);
    } else if (role === "controller") {
      this.router.navigate(["/controller"]);
    } else {
      this.router.navigate(["/"]);
    }
  }
}
