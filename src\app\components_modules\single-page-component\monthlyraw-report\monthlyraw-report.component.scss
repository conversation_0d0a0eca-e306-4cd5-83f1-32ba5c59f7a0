@import "../../../../assets/scss/navbar.scss";
td,
th {
  width: 200px !important;
  max-width: 220px;
  min-width: 218px;
}
.pt-4 {
  text-align: left;
  padding-bottom: 1.5rem;
}

.text-alignment {
  padding: 2rem;
}

.divider { 
  border-top-width: 3px;
}

.mat-button.btn.btn-primary, .mat-raised-button.btn.btn-primary, .mat-raised-button.btn:not([class*="mat-elevation-z"]).btn-primary, .btn.btn-primary{
  width: 40% !important;
}