import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListCategoryModeTransportComponent } from './list-category-mode-transport/list-category-mode-transport.component';
import { AddUpdateCategoryModeTransportComponent } from './add-update-category-mode-transport/add-update-category-mode-transport.component';


const routes: Routes = [
  { path: '', component: ListCategoryModeTransportComponent },
  { path: 'addcategorytransportmapping', component: AddUpdateCategoryModeTransportComponent },
  { path: 'updatecategorytransportmapping/:id/:id2', component: AddUpdateCategoryModeTransportComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CategoryModeTransportRoutingModule { }
