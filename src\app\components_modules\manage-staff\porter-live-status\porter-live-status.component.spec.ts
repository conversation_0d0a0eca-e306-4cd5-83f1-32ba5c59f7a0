import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PorterLiveStatusComponent } from './porter-live-status.component';

describe('PorterLiveStatusComponent', () => {
  let component: PorterLiveStatusComponent;
  let fixture: ComponentFixture<PorterLiveStatusComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PorterLiveStatusComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PorterLiveStatusComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
