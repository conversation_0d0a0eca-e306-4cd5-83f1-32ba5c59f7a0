import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { KpiReportsRoutingModule } from "./kpi-reports-routing.module";
import { TotalWorkLoadComponent } from "./workload/total-work-load/total-work-load.component";
import { CpJobTypeComponent } from "./workload/cp-job-type/cp-job-type.component";
import { SpJobTypeComponent } from "./workload/sp-job-type/sp-job-type.component";
import { SpWorkLoadByLocationComponent } from "./workload/sp-work-load-by-location/sp-work-load-by-location.component";
import { PerformanceKpiComponent } from "./performance-kpi/performance-kpi/performance-kpi.component";
import { TotalCancellationComponent } from "./cancellation/total-cancellation/total-cancellation.component";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { SharedModule } from "src/app/shared/shared.module";
import { CancellationReasonsComponent } from "./cancellation/cancellation-reasons/cancellation-reasons.component";
import { KpiReportHomeComponent } from "./kpi-report-home/kpi-report-home.component";
import { SearchfilterComponent } from "./searchFilter/searchfilter/searchfilter.component";
import { MatTabsModule } from "@angular/material/tabs";
import { KpiSummaryComponent } from './kpi-summary/kpi-summary.component';
import { NgxMatDatetimePickerModule, NgxMatTimepickerModule, NgxMatNativeDateModule } from '@angular-material-components/datetime-picker';

@NgModule({
  declarations: [
    TotalWorkLoadComponent,
    CpJobTypeComponent,
    SpJobTypeComponent,
    SpWorkLoadByLocationComponent,
    PerformanceKpiComponent,
    TotalCancellationComponent,
    CancellationReasonsComponent,
    KpiReportHomeComponent,
    SearchfilterComponent,
    KpiSummaryComponent,
  ],
  imports: [
    CommonModule,
    KpiReportsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatTabsModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatNativeDateModule
  ],
})
export class KpiReportsModule { }
