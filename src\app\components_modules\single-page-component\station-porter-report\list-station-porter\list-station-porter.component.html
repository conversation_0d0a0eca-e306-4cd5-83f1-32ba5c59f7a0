<div class="main-content">
  <div class="container-fluid">
    <app-summary-menu-list [routes]="routes"></app-summary-menu-list>
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('xsls')">
                      xsls
                    </p>
                  </a>
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('csv')">
                      csv
                    </p>
                  </a>
                  <!-- <a style="margin-top: -5px; cursor: pointer; color: #555555" (click)="exportTable('pdf')"
                    class="nav-link">
                    <p style="display: inline-block">PDF</p>
                  </a> -->
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav">
                  <p>View Station Porter Reports</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">

              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="stationReportForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Station Porter Reports
                      </legend>
                      <div class="row">
                        <div class="col-4">
                          <mat-form-field>
                            <mat-label> Report Type </mat-label>
                            <mat-select formControlName="report_type" (ngModelChange)="reportTypeChange()">
                              <mat-option *ngFor="let value of reportData" [value]="value.id">
                                {{ value.label }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>

                        <div class="col-4">
                          <mat-form-field>
                            <span matSuffix style="cursor: pointer"
                              (click)="clearFormValue('locationsList');$event.stopPropagation()">
                              <mat-icon>clear</mat-icon>
                            </span>
                            <mat-label>Station location</mat-label>
                            <mat-select #singleSelect formControlName="locationsList" [disableOptionCentering]="true">
                              <mat-option>
                                <ngx-mat-select-search [formControl]="locationFilterCtrl"
                                  [placeholderLabel]="'Find Location...'" [noEntriesFoundLabel]="
                                    'no matching location found'
                                  "></ngx-mat-select-search>
                              </mat-option>
                              <mat-option [value]="0">All</mat-option>
                              <mat-option *ngFor="let location of filteredLocations | async"
                                [value]="location.location_id">
                                {{ location.location_name }}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>

                        <div class="col-4">
                          <app-datep-picker [dateConfiguration]="specificDate"
                            [control]="stationReportForm.controls.from" [fieldName]="specificDate.label"
                            [fieldType]="'select'">
                          </app-datep-picker>
                        </div>
                        <div *ngIf="showToDate" class="col-4">
                          <app-datep-picker *ngIf="
                              stationReportForm.get('report_type').value ===
                              'daterange'
                            " [dateConfiguration]="Todate" [control]="stationReportForm.controls.to"
                            [fieldName]="Todate.label" [fieldType]="'select'">
                          </app-datep-picker>
                        </div>
                        <div class="col-4">
                          <mat-form-field>
                            <mat-label>Porter
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="staff">
                              <mat-option [value]="0">All</mat-option>
                              <mat-option *ngFor="let value of staff" [value]="value.staff_id">
                                {{ value.staff_name }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="stationReportForm.controls.staff" [fieldName]="'Staff'"
                                [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div *ngIf="!showToDate" class="col-4"></div>
                        <div class="col-4">
                          <button mat-raised-button class="btn btn-white pull-right" (click)="reset()">
                            Reset
                          </button>
                          <button mat-raised-button type="submit" (click)="searchStationReport()"
                            class="btn btn-primary pull-right">
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table id="stationReportTable" mat-table [dataSource]="dataSource" matSort>
                      <caption></caption>

                      <ng-container matColumnDef="JobID">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Order No
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row?.JobID }}</td>
                      </ng-container>

                      <ng-container matColumnDef="station_location">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Station Location
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.station_location }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="staff_name">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Staff Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.staff_name }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="FromLocation">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          From
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.FromLocation }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="ToLocation">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          To
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.ToLocation }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="JobType">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Job Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.JobType }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="TransportType">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Transportation Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.TransportType }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="RequestTime">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Request Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.RequestTime }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="ack_by">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Ack By
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.ack_by }}
                        </td>
                      </ng-container>
                      <!-- <ng-container matColumnDef="StartTime">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Respond Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.StartTime }}
                        </td>
                      </ng-container> -->

                      <ng-container matColumnDef="CompletionTime">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Completion Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.CompletionTime }}
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="CancelTime">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Cancel Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.CancelTime }}
                        </td>
                      </ng-container> -->

                      <ng-container matColumnDef="PatientName_NRICNO">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Patient Name/NRIC No
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.PatientName_NRICNO }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="Patient_IC_Scanned_Time">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Patient IC Scanned Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.PatientICScanTime }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="Remarks">
                        <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                          Remarks
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.Remarks }}
                        </td>
                      </ng-container>

                      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                    </table>
                    <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons [pageSize]=50
          (page)="pageChanged($event)"></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf [heads]="[
    'Order No',
    'Station Location',
    'Staff Name',
    'From',
    'To',
    'Job Type',
    'Transportation Type',
    'Request Time',
    'ack_by',
    'Completion Time',
    'Patient Name/NRIC No',
    'Remarks'
  ]" [title]="'Station Porter Report'" [datas]="pdfData">
</app-table-for-pdf>