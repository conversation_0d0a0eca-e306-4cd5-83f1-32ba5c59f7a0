import { Injectable, Error<PERSON>andler } from '@angular/core';
import { LoaderService } from '../Apiservices/loader/loader.service';

@Injectable()
export class ErrorHandle implements ErrorHandler {
    constructor(
        private readonly loader: LoaderService
    ) { }

    handleError(error) {
        if (error) {
            console.log(error);
            this.loader.hide();
        }
    }
}
