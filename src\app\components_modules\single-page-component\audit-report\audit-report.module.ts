import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { AuditReportRoutingModule } from "./audit-report-routing.module";
import { AuditReportComponent } from "./audit-report.component";
import { SharedModule } from "src/app/shared/shared.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { MatTabsModule, MatToolbarModule } from "@angular/material";

@NgModule({
  declarations: [AuditReportComponent],
  imports: [
    CommonModule,
    AuditReportRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatToolbarModule,
    MatTabsModule,
  ],
})
export class AuditReportModule {}
