import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListTransportMappingComponent } from './list-transport-mapping/list-transport-mapping.component';
import { AddUpdateTransportMappingComponent } from './add-update-transport-mapping/add-update-transport-mapping.component';

const routes: Routes = [
  {path: '', component: ListTransportMappingComponent},
  {path: 'addsubjobcategory', component: AddUpdateTransportMappingComponent},
  {path: 'updatesubjobcategory/:id', component: AddUpdateTransportMappingComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StationTransportMappingRoutingModule { }
