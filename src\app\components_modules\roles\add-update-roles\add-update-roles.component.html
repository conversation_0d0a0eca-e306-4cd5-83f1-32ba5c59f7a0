<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Roles</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Roles</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="rolesForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Role Name <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Role Name"
                                                            formControlName="role_name">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="rolesForm.controls.role_name"
                                                                [fieldName]="'Role Name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <mat-radio-group formControlName="status">
                                                        <mat-radio-button class="example-margin" value=true>Active
                                                        </mat-radio-button>
                                                        <mat-radio-button class="example-margin" value=false>
                                                            Inactive</mat-radio-button>
                                                    </mat-radio-group>
                                                    <mat-error class="pull-left error-css">
                                                        <app-error-message [control]="rolesForm.controls.status"
                                                            [fieldName]="'Status'" [fieldType]="'select'">
                                                        </app-error-message>
                                                    </mat-error>
                                                </div>
                                                <div class="col-12">
                                                    <div cdkDropListGroup>
                                                        <div class="row">
                                                            <div class="col-5">
                                                                <div class="example-container card">
                                                                    <h4>Menu</h4>
                                                                    <div cdkDropList [cdkDropListData]="menuList"
                                                                        class="example-list"
                                                                        (cdkDropListDropped)="drop($event)">
                                                                        <div class="example-box"
                                                                            *ngFor="let item of menuList" cdkDrag>
                                                                            {{item.name}}</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-2">
                                                                <div class="forward-example">
                                                                    <p class="border-forward">></p>
                                                                    <p class="border-forward">>></p>
                                                                </div>
                                                                <div class="forward-example">
                                                                    <p class="border-forward">{{'<'}}</p>
                                                                    <p class="border-forward">{{'<<'}}</p>
                                                                </div>
                                                            </div>
                                                            <div class="col-5">
                                                                <div class="example-container card">
                                                                    <h4>Selected Menu</h4>
                                                                    <div cdkDropList [cdkDropListData]="selectedMenu"
                                                                        class="example-list"
                                                                        (cdkDropListDropped)="drop($event)">
                                                                        <div class="example-box"
                                                                            *ngFor="let item of selectedMenu" cdkDrag>
                                                                            {{item.name}}</div>
                                                                    </div>
                                                                    <mat-error class="pull-left error-css">
                                                                        <app-error-message
                                                                            [control]="rolesForm.controls.subId_list"
                                                                            [fieldName]="'Menu'" [fieldType]="'drag'">
                                                                        </app-error-message>
                                                                    </mat-error>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveRoles('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveRoles('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="rolesForm.reset(); selectedMenu.length = 0; getMenu(); rolesForm.controls.status.setValue('true'); getRole()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>