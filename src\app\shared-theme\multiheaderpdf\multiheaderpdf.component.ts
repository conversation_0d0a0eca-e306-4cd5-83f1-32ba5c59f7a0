import { Component, OnInit, Input } from '@angular/core';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-multiheaderpdf',
  templateUrl: './multiheaderpdf.component.html',
  styleUrls: ['./multiheaderpdf.component.scss']
})
export class MultiheaderpdfComponent implements OnInit {
  logoUrl = '';
  @Input() title = '';
  @Input() pdfData = [];
  @Input() eq = false;
  @Input() kpi = false;
  tis = 2;
  constructor(
    private readonly userService: UserService
  ) { }

  ngOnInit() {
    this.getLogo();
  }

  getLogo() {
    this.userService.logoUrl.subscribe(res => {
      this.logoUrl = res || '';
    });
  }
}
