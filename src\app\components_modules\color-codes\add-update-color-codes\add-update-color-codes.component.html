<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Color code</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Color code</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="colorCodesForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Job Status<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="condition">
                                                            <mat-option *ngFor="let statusjob of jobstatus"
                                                                [value]="statusjob.condition"
                                                                (click)="colrdIdSet(statusjob.color_id)">
                                                                {{statusjob.condition}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="colorCodesForm.controls.condition"
                                                                [fieldName]="'Job Status'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Color <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput #ignoredInput
                                                            [style.background]="colorCodesForm.controls.color_code.value"
                                                            value="{{colorCodesForm.controls.color_code.value}}"
                                                            (colorPickerChange)="setColorCode($event); color=$event"
                                                            [cpIgnoredElements]="[ignoredButton, ignoredInput]"
                                                            [(cpToggle)]="toggle" [(colorPicker)]="color"
                                                            [cpOKButton]="true"
                                                            [cpOKButtonClass]="'btn btn-primary btn-xs'" />
                                                        <button #ignoredButton matSuffix (click)="toggle=!toggle">
                                                            <img src="../../../../assets/img/ColorPickerSprites (1).png"
                                                                alt="image not found">
                                                        </button>
                                                        <mat-hint style="text-align: end;">
                                                            {{colorCodesForm.controls.color_code.value}} </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="colorCodesForm.controls.color_code"
                                                                [fieldName]="'Color'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right" (click)="saveLevel()">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right" (click)="saveLevel()">Update</button>
                                            <button mat-raised-button
                                                (click)="colorCodesForm.reset(); getColorCodeById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>