import { Injectable } from '@angular/core';
import { Title, Meta } from '@angular/platform-browser';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { filter, map, mergeMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TitleService {

  constructor(
    private readonly titleService: Title,
    private readonly meta: Meta,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
) { }

updateMetaInfo(contents, author, category) {
    this.meta.updateTag({ name: 'description', content: contents });
    this.meta.updateTag({ name: 'author', content: author });
    this.meta.updateTag({ name: 'keywords', content: category });
}

updateTitle(title?: string) {
    if (!title) {
        this.router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                map(() => this.activatedRoute),
                map((route) => {
                    while (route.firstChild) { route = route.firstChild; }
                    return route;
                }),
                filter((route) => route.outlet === 'primary'),
                mergeMap((route) => route.data)).subscribe((event) => {
                    // tslint:disable-next-line:no-string-literal
                    this.titleService.setTitle(event['title'] || 'UETrack™ - Portering System');
                });
    } else {
        this.titleService.setTitle(title || 'UETrack™ - Portering System');
    }
}
}
