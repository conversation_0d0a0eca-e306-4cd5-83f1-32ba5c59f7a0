import { Component, OnInit, Input } from "@angular/core";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-table-for-pdf",
  templateUrl: "./table-for-pdf.component.html",
  styleUrls: ["./table-for-pdf.component.scss"],
})
export class TableForPdfComponent implements OnInit {
  logoUrl = "";
  heads: any[];
  @Input("heads")
  public set head(v: Array<any>) {
    this.heads = [].concat.apply([], v);
  }
  @Input() title = "";
  @Input() datas = [];
  @Input() fontClass = "";
  @Input() isHourlyReport = false;
  constructor(private readonly userService: UserService) { }

  ngOnInit() {
    this.getLogo();
  }

  getLogo() {
    this.userService.logoUrl.subscribe((res) => {
      this.logoUrl = res || "";
    });
  }
}
