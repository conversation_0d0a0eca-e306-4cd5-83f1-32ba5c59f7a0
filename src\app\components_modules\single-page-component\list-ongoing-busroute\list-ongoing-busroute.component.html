<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav">
                  <p>View Ongoing Bus Routes</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <fieldset class="scheduler-border">
                    <legend></legend>
                    <div class="row">
                      <div class="col-3">
                        <div class="col" style="padding-top: 1%">
                          <mat-form-field>
                            <input
                              matInput
                              placeholder="Search..."
                              #filter
                              (keydown)="applyFilter($event.target.value)"
                            />
                            <mat-icon matSuffix>search</mat-icon>
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label
                            >Departments<span class="error-css"></span>
                          </mat-label>
                          <mat-select
                            [(ngModel)]="selectedDepartments"
                            (ngModelChange)="selectedDepartments"
                            multiple="true">
                            <mat-option #allSelected (click)="toggleAllSelection()" [value]="'All'">All</mat-option>
                            <mat-option
                              *ngFor="let data of departmentList"
                              [value]="data.departmentId" (click)="tosslePerOne(allSelected.viewValue)">
                              {{ data.departmentName }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-1" style="padding-top: .5%">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="applyFilter(filter.value)">
                          <em class="fa fa-minus-square-o"></em>Search
                        </button>
                      </div>
                      <div class="col-1" style="padding-top: .5%">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="filter.value = ''; applyFilter(filter.value, 'reset')">
                          <em class="fa fa-minus-square-o"></em>Reset
                        </button>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="onGoingBusRouteTable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <!-- <ng-container matColumnDef="route_id">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Route Id
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.route_id}} </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="route_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Route No
                        </th>

                        <td mat-cell *matCellDef="let row">
                          <span>{{ row.route_no }}</span>
                          <ng-container *ngIf="row.route_type == 'Scheduled'"
                            ><span>*</span>
                          </ng-container>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="route_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Route Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.route_name }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="created_date">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Creation Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.created_date | localDateConversion: "full" }}
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="start_location_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Start Loc
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.start_location_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="end_location_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> End Loc
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.end_location_name}} </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="location_count">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          No of Locs
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <span>
                            <a
                              href="javascript:void(0);"
                              *ngIf="row.location_count; else nolocations"
                              data-toggle="modal"
                              data-target="#exampleModal"
                              (click)="
                                locationDisplay(
                                  row.location_list,
                                  row.is_sequential
                                )
                              "
                              >{{ row.location_count }}</a
                            >
                            <ng-template #nolocations>0</ng-template>
                          </span>
                          <span *ngIf="row.is_sequential">*</span>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="start_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Sch. Start Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.start_time | localDateConversion: "full" }}
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="assign_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Assigned
                                                    Time </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row.assign_time ? row.assign_time : '--'}} </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="actual_start_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Start Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            *ngIf="
                              row.actual_start_time != null;
                              else notStarted
                            "
                            >{{
                              row.actual_start_time
                                | localDateConversion: "full"
                            }}</a
                          >
                          <ng-template #notStarted>
                            <a
                              *ngIf="
                                !row.actual_start_time &&
                                  (row.resource1 || row.resource2) &&
                                  !row.cancel_time;
                                else cantStart
                              "
                              href="javascript:void(0);"
                              data-toggle="modal"
                              data-target="#StartTimeModal"
                              (click)="
                                presentRouteId = row.route_id;
                                actionType = 'start';
                                adHocdisabled = row.route_type;
                                setDate(row, 'start')
                              "
                              >Start</a
                            >
                          </ng-template>
                          <ng-template #cantStart>--</ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="end_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Comp Time
                        </th>

                        <td mat-cell *matCellDef="let row">
                          <a *ngIf="row.end_time"
                            >{{ row.end_time | localDateConversion: "full" }}
                          </a>
                          <a
                            *ngIf="row.actual_start_time && !row.end_time"
                            href="javascript:void(0);"
                            data-toggle="modal"
                            data-target="#StartTimeModal"
                            (click)="
                              presentRouteId = row.route_id;
                              actionType = 'end';
                              setDate(row, 'end')
                            "
                            >End</a
                          >
                          <span *ngIf="!row.actual_start_time && !row.end_time"
                            >--</span
                          >
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="cancel_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Cancel
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <span *ngIf="row.cancel_time">{{
                            row.cancel_time | localDateConversion: "full"
                          }}</span>
                          <a
                            *ngIf="
                              !row.end_time &&
                              !row.cancel_time &&
                              !row.actual_start_time
                            "
                            href="javascript:void(0);"
                            (click)="
                              takeAction('cancel', row.route_id);
                              actionType = 'cancel'
                            "
                            >Cancel</a
                          >
                          <span
                            *ngIf="!row.cancel_time && row.actual_start_time"
                            >--</span
                          >
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="resource1">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Resource 1
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            *ngIf="!row.resource1; else cannotAssign"
                            href="javascript:void(0);"
                            data-toggle="modal"
                            data-target="#resourceModal"
                            (click)="
                              presentRouteId = row.route_id;
                              actionType = 'assign'
                            "
                            >assign</a
                          >
                          <ng-template #cannotAssign>
                            {{ row.resource1 }}
                          </ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="resource2">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Resource 2
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            *ngIf="
                              row.resource1 &&
                                !row.actual_start_time &&
                                !row.cancel_time;
                              else cannotReassign
                            "
                            href="javascript:void(0);"
                            data-toggle="modal"
                            data-target="#resourceModal"
                            (click)="
                              getNewStaffList(row);
                              presentRouteId = row.route_id;
                              actionType = 'reassign'
                            "
                            >{{ row.resource2 ? row.resource2 : "ReAssign" }}
                          </a>

                          <ng-template #cannotReassign
                            >{{ row.resource2 ? row.resource2 : "--" }}
                          </ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="remarks">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Remarks
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.remarks }}
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf
  [heads]="[
    'Route No',
    'Route Name',
    'Creation Time',
    'Start Loc',
    'End Loc',
    'No of Locs',
    'Sch. Start Time',
    'Assigned Time',
    'Start Time',
    'Comp Time',
    'Cancel Time',
    'Resource 1',
    'Resource 2',
    'Remarks'
  ]"
  [title]="'OnGoing Bus Routes'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<!-- Location Modal -->
<div class="modal fade" id="exampleModal" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <p class="modal-title">Locations</p>
      </div>
      <div class="modal-body border">
        <ul>
          <li *ngFor="let location of locationNames">
            {{ location.location_name }} -
            {{ location.arrival_time | localDateConversion: "date" }}
            {{ location.arrival_time | localDateConversion: "time" }}
          </li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!--Start time Modal -->
<div class="modal fade" id="StartTimeModal" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <form [formGroup]="timeAndStaffAction">
      <div class="modal-content">
        <div class="modal-header">
          <p class="modal-title">Select Date and Time</p>
        </div>
        <div class="modal-body">
          <ng-container
            *ngIf="
              (adHocdisabled && adHocdisabled !== 'Ad-hoc') || !adHocdisabled
            "
          >
            <mat-form-field style="margin-bottom: 7%">
              <input
                matInput
                formControlName="date"
                [matDatepicker]="picker"
                [placeholder]="dateOfJoiningDate.label"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </ng-container>

          <p *ngIf="adHocdisabled && adHocdisabled === 'Ad-hoc'">
            {{ adoCDate }}
          </p>

          <!-- <ng-container *ngIf="actionType == 'start'">
            <app-time-range-picker
              [label]="'Select Time'"
              [control]="timeAndStaffAction.controls.time"
              [fieldName]="'Time'"
              (timeChanged)="getDateTime()"
              [fieldType]="'select'"
            >
            </app-time-range-picker>
          </ng-container> -->

          <!-- //////////////////////////// -->

          <ng-container *ngIf="methodOfStartEndBusRoute">
            <div class="row">
              <ng-container
                formArrayName="location"
                *ngFor="
                  let locationForm of timeAndStaffAction.get('location')[
                    'controls'
                  ];
                  let i = index
                "
              >
                <div class="col-5">
                  <p>
                    {{
                      timeAndStaffAction.controls.location["controls"][i][
                        "controls"
                      ]["end_loc"].value
                    }}
                  </p>
                </div>
                <!-- *ngIf="showLocationFlag" -->
                <div class="col-6">
                  <ng-container [formGroupName]="i">
                    <mat-form-field>
                      <input
                        type="text"
                        [id]="'picker' + i"
                        [formControl]="
                          timeAndStaffAction.controls.location['controls'][i][
                            'controls'
                          ]['time']
                        "
                        matInput
                        #myPickerRef
                      />
                    </mat-form-field>
                    <mat-error
                      style="font-size: 12px; margin-top: 18px"
                      *ngIf="
                        i == rowData.length - 1 &&
                        !timeAndStaffAction.controls.location['controls'][i][
                          'controls'
                        ]['time'].touched &&
                        this.triggerTimeValidation
                      "
                    >
                      Please select time
                    </mat-error>
                  </ng-container>
                </div>
              </ng-container>
            </div>
          </ng-container>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" (click)="takeAction()">
            Submit
          </button>
          <button
            mat-raised-button
            class="btn btn-white"
            data-dismiss="modal"
            (click)="resetdata()"
          >
            Cancel
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!--  Reaasign (resource) Modal -->
<div class="modal fade" id="resourceModal" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <form [formGroup]="timeAndStaffAction">
      <div class="modal-content">
        <div class="modal-header">
          <p class="modal-title">Select Staff</p>
        </div>
        <div class="modal-body">
          <mat-form-field>
            <mat-label>Staff </mat-label>
            <mat-select formControlName="staff_id">
              <mat-option disabled="true">Select Staff</mat-option>
              <mat-option *ngFor="let val of staff" [value]="val.staff_id"
                >{{ val.staff_id }} -
                {{ val.staff_name }}
              </mat-option>
            </mat-select>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="timeAndStaffAction.controls.staff_id"
                [fieldName]="'Staff'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </mat-form-field>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" (click)="takeAction()">
            Submit
          </button>
          <button
            mat-raised-button
            class="btn btn-white"
            data-dismiss="modal"
            (click)="resetdata()"
          >
            Cancel
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
