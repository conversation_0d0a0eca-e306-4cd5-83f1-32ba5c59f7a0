import { Component, OnInit, Input } from '@angular/core';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-cpjobtypepdf',
  templateUrl: './cpjobtypepdf.component.html',
  styleUrls: ['./cpjobtypepdf.component.scss']
})
export class CpjobtypepdfComponent implements OnInit {
  logoUrl = '';
  @Input() title = '';
  @Input() pdfData = [];
  @Input() rows = [];
  @Input() headers = [];
  constructor(
    private readonly userService: UserService
  ) { }

  ngOnInit() {
    this.getLogo();
  }

  getLogo() {
    this.userService.logoUrl.subscribe(res => {
      this.logoUrl = res || '';
    });
  }
}
