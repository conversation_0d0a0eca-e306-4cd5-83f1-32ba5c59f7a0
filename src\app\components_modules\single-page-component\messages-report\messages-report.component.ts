import { UserService } from './../../../Apiservices/userService/user.service';
import { ToastrService } from 'ngx-toastr';
import { ReportsService } from './../../../Apiservices/reports/reports.service';
import { FormBuilder, FormControl } from '@angular/forms';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { TableUtil } from '../../../utuilities/tablexlsxutility/tableXslxUtility';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

@Component({
  selector: 'app-messages-report',
  templateUrl: './messages-report.component.html',
  styleUrls: ['./messages-report.component.scss']
})
export class MessagesReportComponent implements OnInit {
  routes = [
    { path: "./../messages-report", label: "Messages Report" },
    { path: "./../rtls-messages-report", label: "RTLS Messages Report" }
  ];

  displayedColumns: string[] = [
    "staffName",
    "locationName",
    "message",
    "messageDate",
    "ackDate",
    "ackBy",
  ];
  dataSource: MatTableDataSource<[]>;
  exportResult: any;
  messagesReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  count = 0;
  staffs: any = [];
  staffCtrl = new FormControl();
  filteredStaff: Observable<any[]>;
  constructor(
    private _fb: FormBuilder,
    private reportService: ReportsService,
    private userService: UserService,
    private toastr: ToastrService
  ) {
    this.getManageStaffs();
    this.createForm();
    this.searchByData();
  }

  createForm() {
    this.messagesReportForm = this._fb.group({
      staffName: [null],
      from_date: [null],
      to_date: [null],
    });
  }

  ngOnInit() {
    this.filteredStaff = this.staffCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredStaff(category) : this.staffs.slice()))
    );
  }

  getManageStaffs() {
    this.userService.getManageStaff().subscribe((res) => {
      res =
        res &&
        res.length &&
        res.filter((staff) => {
          if (staff.status) {
            return staff;
          }
        });
      this.staffs = res || [];
      this.filteredStaff = this.staffCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filteredStaff(category) : this.staffs.slice()))
      );
    });
  }

  _filteredStaff(value: string) {
    const filterValue = value.toLowerCase();
    return this.staffs.filter(
      (category) => category.staff_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  searchByData(type?: any, paginationData?: any) {
    const data = {
      "fromDate": this.messagesReportForm.value.from_date,
      "toDate": this.messagesReportForm.value.to_date,
      "staffId": this.messagesReportForm.value.staffName,
      // "pageNo": type && type == 'next' ? Number(paginationData.offset) + 1 : 1,
      // "pageSize": (type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xlsx') ? this.count : this.paginator.pageSize) || 50,
    }
    this.reportService.getMessageReports(data).subscribe(res => {
      this.exportResult = res;
      if (type && type == 'next') {
        this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
        this.exportResult.push(...res);
        this.exportResult.length = res[0].count;
        this.dataSource = new MatTableDataSource<any>(this.exportResult);
        this.dataSource._updateChangeSubscription();
        this.dataSource.paginator = this.paginator;
      } else if (type && (type == 'xlsx' || type == 'csv')) {
        this.mapTableDataforReport(this.exportResult, type);
      } else {
        this.paginator.pageIndex = 0
        // this.exportResult.length = res[0].count;
        // this.count = res[0].count;
        this.paginator.length = res.length;;
        this.dataSource = new MatTableDataSource(this.exportResult || []);
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
      }
    })
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.searchByData('next', paginationData);
  }
  mapTableDataforReport(exportResult, type?) {
    let mappedData;
    console.log(exportResult);
    let exportObj: any[] = [];
    for (var i = 0; i < exportResult.length; i++) {
      mappedData = {
        'Staff Name': exportResult[i].staffName,
        'Location Name': exportResult[i].locationName,
        'Message': exportResult[i].message,
        'Message Date': exportResult[i].messageDate,
        'Acknowledged Date': exportResult[i].ackDate,
        'Acknowledged By': exportResult[i].ackBy,
      };
      exportObj.push(mappedData);
    }
    if (type == 'csv') {
      TableUtil.exportArrayToCsvDynamically(exportObj, "messagesReport_list");
    } else {
      TableUtil.exportArrayToExcelDynamically(exportObj, "messagesReport_list");
    }
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    this.searchByData(fileType);
  }

  resetForm() {
    this.paginator.pageIndex = 0;
    this.paginator.pageSize = 50;
    this.createForm();
    this.searchByData();
  }

  clearFormValue(formField: string) {
    this.messagesReportForm.get([formField]).setValue('')
  }

  getDate() {
    if (
      this.messagesReportForm.get("from_date").value &&
      this.messagesReportForm.get("to_date").value
    ) {
      if (
        this.messagesReportForm.get("from_date").value >
        this.messagesReportForm.get("to_date").value
      ) {
        this.messagesReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be greater then From date", "Error");
      }
    }
  }

}
