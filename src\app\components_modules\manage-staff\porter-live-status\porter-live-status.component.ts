import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { ManageStaff } from "src/app/models/manageStaff";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";

@Component({
  selector: 'app-porter-live-status',
  templateUrl: './porter-live-status.component.html',
  styleUrls: ['./porter-live-status.component.scss', "../../../scss/table.scss"]
})
export class PorterLiveStatusComponent implements OnInit {

  displayedColumns: string[] = [
    
    "staff_name",
    "TOL",
    "assign_time",
    "staff_type",
    "status",

  ];
  pdfData = [];
  dataSource: MatTableDataSource<ManageStaff>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  polling: any;

  constructor(
    private readonly userService: UserService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.getManageStaff();
    this.polling = setInterval(() => {
      this.getManageStaff();
    },30*1000)
  }

  ngOnDestroy() {
    clearInterval(this.polling);
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "managestaff",
        "managestaff"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("managestaff", "managestaff");
    }
  }

  getManageStaff() {
    this.userService.getPorterLiveStatus().subscribe((res) => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Porter Name": key.staff_name,
              "Porter Type": key.staff_type,
              "Skill Level": key.skill_level,
              Gender: key.gender,
              Mobile: key.mobile_no,
              Status: key.status ? "Active" : "Inactive",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

}
