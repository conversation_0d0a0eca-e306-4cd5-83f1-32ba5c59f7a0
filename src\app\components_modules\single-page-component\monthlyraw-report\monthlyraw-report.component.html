<div class="main-content">
    <div class="container-fluid">
      <div class="row">
        <div class="card card-nav-tabs">
          <div class="card-header card-header-primary card__header__grey">
            <div class="nav-tabs-navigation">
              <div class="">
                <ul class="nav">
                  <li>
                    <p>Monthly Raw Report</p>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="tab-content text-center">
              <div class="tab-pane active" id="viewStatus">
                <div class="row">
                  <div class="col-md-12 filter-margin">
                    <form [formGroup]="monthlyReportForm" style="padding-bottom: 100px;">
                      <br>
                      <fieldset class="scheduler-border">
                          <legend class="scheduler-border">Monthly Raw Report </legend>
                          <div class="row">
                              <div class="col-2">
                                  <mat-form-field>
                                      <mat-select formControlName="month">
                                          <mat-option *ngFor="let value of months" [value]="value.id">
                                              {{value.val}}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>
                              </div>
                              <div class="col-2">
                                  <mat-form-field>
                                      <mat-select formControlName="year">
                                          <mat-option *ngFor="let val of years" [value]="val">
                                              {{val}}
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>
                              </div>
                              <div class="col-6">
                                  <button mat-raised-button type="submit" class="btn btn-primary pull-left" (click)="downloadReport()">Download Montly Report</button>
                             </div>
                          </div>
                      </fieldset>
                  </form>
                  <mat-divider class="divider"></mat-divider>
                  <!-- <div class="row text-alignment">
                    Data
                    <div class="col-12">
                        <dl
                          class="important__note font-italic pt-4"
                        >
                          <dt style="color: #000000; font-weight: 600">
                            Important Notes:
                          </dt>
                          <dd style="color: #000000">
                            - Text...
                          </dd>
                        </dl>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>