import { HttpClient } from "@angular/common/http";
import { TestBed } from "@angular/core/testing";

import { UamReportsService } from "./uam-reports.service";
import {
  HttpClientTestingModule,
  HttpTestingController,
} from "@angular/common/http/testing";
import { UamReportComponent } from "./uam-report.component";
import { environment } from "src/environments/environment";
describe("UamReportsService", () => {
  let uamReportController: HttpTestingController;
  let service: UamReportsService;
  beforeEach(() =>
    TestBed.configureTestingModule({
      providers: [UamReportsService],
      imports: [HttpClientTestingModule],
    })
  );

  uamReportController = TestBed.get(UamReportComponent);
  service = TestBed.get(UamReportsService);

  afterEach(() => {
    uamReportController.verify();
  });

  it("returned uam report role data", () => {
    service.getUAMRoles().subscribe((role) => {
      expect(role).toEqual([
        {
          role_name: "Superadmin",
          portering: false,
          uems: true,
        },
        {
          role_name: "Controller",
          portering: true,
          uems: false,
        },
        {
          role_name: "Manager",
          portering: true,
          uems: false,
        },
        {
          role_name: "SKH GS Manager",
          portering: true,
          uems: false,
        },
        {
          role_name: "User",
          portering: true,
          uems: false,
        },
        {
          role_name: "controler ",
          portering: true,
          uems: false,
        },
        {
          role_name: "Application Admin",
          portering: true,
          uems: false,
        },
        {
          role_name: "AmbulanceServices",
          portering: true,
          uems: false,
        },
      ]);
      const req = uamReportController.expectOne(
        `${environment.base_url}api/uam/roles/module`
      );
      expect(req.request.method).toEqual("GET");
      req.flush([
        {
          role_name: "Superadmin",
          portering: false,
          uems: true,
        },
        {
          role_name: "Controller",
          portering: true,
          uems: false,
        },
        {
          role_name: "Manager",
          portering: true,
          uems: false,
        },
        {
          role_name: "SKH GS Manager",
          portering: true,
          uems: false,
        },
        {
          role_name: "User",
          portering: true,
          uems: false,
        },
        {
          role_name: "controler ",
          portering: true,
          uems: false,
        },
        {
          role_name: "Application Admin",
          portering: true,
          uems: false,
        },
        {
          role_name: "AmbulanceServices",
          portering: true,
          uems: false,
        },
      ]);

      expect(req.request.method).toEqual("POST");
    });
  });
  it("should be created", () => {
    expect(service).toBeTruthy();
  });
});
