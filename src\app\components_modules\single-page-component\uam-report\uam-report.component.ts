import { Component, OnInit } from "@angular/core";
import { from, of } from "rxjs";
import { map, tap } from "rxjs/operators";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { UamReportsService } from "./uam-reports.service";

@Component({
  selector: "app-uam-report",
  templateUrl: "./uam-report.component.html",
  styleUrls: ["./uam-report.component.scss"],
})
export class UamReportComponent implements OnInit {
  roleModule$: any;
  users$: any;
  roles$: any;
  allRoles: any = {};
  roleModuleAllAssign$: any;
  pdfData: any;
  allRoleForExcel$: any;

  constructor(
    private uamReportService: UamReportsService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.getUAMRoles();
  }

  getUAMRoles() {
    this.roles$ = this.uamReportService.getUAMRoles();
    this.roles$
      .pipe(
        map((data: any) => {
          return data.map((module) => {
            return (this.allRoles[module.role_name] = "");
          });
        })
      )

      .subscribe((data) => this.getUAMRolesModule());

    this.allRoleForExcel$ = this.roles$.pipe(
      map((allRoleExcel: any) => allRoleExcel.map((data) => data.role_name))
    );
  }

  getUAMRolesModule() {
    this.roleModule$ = this.uamReportService.getUAMRolesModule();
    this.roleModuleAllAssign$ = this.roleModule$.pipe(
      map((data: any) => {
        return data.map((module_name) => {
          const allRolesDefault = { ...this.allRoles };
          const roleAssign = [];
          module_name.roles.forEach((dataValue) => {
            allRolesDefault[dataValue] = dataValue;
          });
          const roleValue = Object.keys(allRolesDefault).map(
            (key) => allRolesDefault[key]
          );
          return { ...module_name, roles: roleValue };
        });
      })
    );
    this.setPageSizeOptions();
  }

  async exportTable(fileType) {
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "uamReportDumb",
        "uam_module_report"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("uamReportDumb", "uam_module_report");
    }
  }

  setPageSizeOptions() {
    this.roleModuleAllAssign$.subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Modules: key.sub_menu,
              ...key.roles.reduce((allRoles, everyRole) => {
                return { [everyRole]: everyRole ? true : false, ...allRoles };
              }, {}),
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
}
