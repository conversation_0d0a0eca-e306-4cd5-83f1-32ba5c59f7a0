import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ValidationService } from "src/app/Apiservices/validation/validation.service";

@Component({
  selector: "app-file-picker",
  templateUrl: "./file-picker.component.html",
  styleUrls: ["./file-picker.component.scss"],
})
export class FilePickerComponent implements OnInit {
  @Input() control: FormControl;
  @Input() fieldName: string;
  @Input() fieldType: string;
  @Input() completeError: string;
  @Input() allowedExtensions: any = [];
  @Output() selectedFile = new EventEmitter();
  @Input() fileupload: File = null;
  @Input() maxSize = 5;
  @Input() required = true;
  @Input() maxMB = 5242880;
  @Input() multiple: boolean = false;
  @Input() showErrorRight = false;

  constructor(private readonly validationService: ValidationService) {}

  ngOnInit() {}

  documnet(event): any {
    if (event.target.files.length == 1) {
      this.control.clearValidators();
      this.control.updateValueAndValidity();
      this.fileupload = event.target.files[0];
      const file = event.target.files[0];
      const photoName = file.name;
      const fileExtension = photoName.split(".").pop();
      const maxSize = this.maxMB;
      const size = file.size;
      if (size > maxSize) {
        this.control.setErrors({
          customError: `File size should be less then ${this.maxSize} MB`,
        });
        this.selectedFile.next(null);

        return;
      }
      if (
        this.allowedExtensions.length &&
        !this.isInArray(this.allowedExtensions, fileExtension)
      ) {
        const extension = this.allowedExtensions.slice();
        this.control.setErrors({
          customError: `File type should be ${extension.join()}`,
        });
        return;
      }
      this.selectedFile.next(file);
    }
  }

  isInArray(array, word) {
    return array.indexOf(word.toLowerCase()) > -1;
  }

  get errorMessage() {
    for (const propertyName in this.control && this.control.errors) {
      if (
        (this.control && this.control.errors).hasOwnProperty(propertyName) &&
        this.control.touched
      ) {
        return this.validationService.getValidatorErrorMessage(
          propertyName,
          this.fieldType,
          this.fieldName,
          (this.control && this.control.errors)[propertyName],
          this.completeError
        );
      }
    }
    return null;
  }
}
