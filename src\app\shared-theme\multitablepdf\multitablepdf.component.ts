import { Component, OnInit, Input } from '@angular/core';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-multitablepdf',
  templateUrl: './multitablepdf.component.html',
  styleUrls: ['./multitablepdf.component.scss']
})
export class MultitablepdfComponent implements OnInit {
  logoUrl = '';
  @Input() tables = [];
  @Input() title = '';
  @Input() fontClass = '';
  constructor(
    private readonly userService: UserService
  ) { }

  ngOnInit() {
    this.getLogo();
  }

  getLogo() {
    this.userService.logoUrl.subscribe(res => {
      this.logoUrl = res || '';
    });
  }
}
