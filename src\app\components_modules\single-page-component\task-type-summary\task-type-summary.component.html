<div class="main-content">
  <div class="container-fluid">
    <app-summary-menu-list [routes]="routes"></app-summary-menu-list>
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav" style="margin-left: 1%; line-height: 32px">
                  <p>View Trasportation Type Report</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
             <!-- <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div>-->

              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="TransportReportForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Trasportation Type Report
                      </legend>
                      <div class="row">
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="TransportReportForm.controls.from_date"
                            [fieldName]="FromDate.label"
                            (getDate)="getDate()"
                            [fieldType]="'select'"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="TransportReportForm.controls.to_date"
                            [fieldName]="FromDate.label"
                            (getDate)="getDate()"
                            [fieldType]="'select'"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button
                            mat-raised-button
                            (click)="
                              searchTransportReport(month, today);
                              TransportReportForm.get('from_date').setValue(
                                month
                              );
                              TransportReportForm.get('to_date').setValue(
                                today
                              );
                              filter.value = ''
                            "
                            class="btn btn-white pull-right"
                          >
                            Reset
                          </button>
                          <button
                            mat-raised-button
                            (click)="
                              searchTransportReport(
                                TransportReportForm.get('from_date').value,
                                TransportReportForm.get('to_date').value
                              )
                            "
                            class="btn btn-primary pull-right"
                          >
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="tranportReport"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <ng-container matColumnDef="TrasportationType">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Transportation Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.TrasportationType }}
                        </td>
                      </ng-container>
                      <!-- <ng-container matColumnDef="TotalRequested">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                        Total Requested
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.TotalRequested }}
                        </td>
                      </ng-container> -->
                      <ng-container matColumnDef="TotalComplete">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                        Total Completed
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.TotalComplete }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="TotalCancelled">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                        Total Cancelled 
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.TotalCancelled }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="TotalJobs">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total Jobs
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.TotalJobs }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="CompletePercentage">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                           % Completed
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.CompletePercentage }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="CancelledPercentage">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                           % Cancelled
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.CancelledPercentage }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="Totalpercentage">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total %
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.Totalpercentage }}
                        </td>
                      </ng-container>
                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row 
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf
  [heads]="[
    'Transportation Type',
    'Total Completed',
    'Total Cancelled',
    'Total Jobs',
    '% Completed',
    '% Cancelled',
    'Total %'
  ]"
  [title]="'Trasportation Type Report'"
  [datas]="pdfData"
>
</app-table-for-pdf>
