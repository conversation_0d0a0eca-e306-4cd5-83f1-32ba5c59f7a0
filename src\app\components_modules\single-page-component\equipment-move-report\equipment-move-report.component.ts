import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { EquipmentReport } from "src/app/models/equipmentReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import * as moment from "moment";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { ToastrService } from "ngx-toastr";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-equipment-move-report",
  templateUrl: "./equipment-move-report.component.html",
  styleUrls: [
    "../../../scss/table.scss",
    "./equipment-move-report.component.scss",
  ],
})
export class EquipmentMoveReportComponent implements OnInit {
  routes = [];
  displayedColumns: string[] = [
    "Equipment",
    "NPM",
    "NNPM",
    "NormalTotal",
    "EPM",
    "ENPM",
    "EmergencyTotal",
    "APM",
    "ANPM",
    "AdvanceTotal",
  ];
  dataSource: MatTableDataSource<EquipmentReport>;
  locationType = [];
  pdfData = [];
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  equipmentReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  constructor(
    private readonly fb: FormBuilder,
    public inputValidation: InputValidationService,
    private readonly loader: LoaderService,
    private readonly reports: ReportsService,
    private readonly toastr: ToastrService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }

  createForm() {
    this.equipmentReportForm = this.fb.group({
      from_date: [this.month, Validators.required],
      to_date: [this.today, Validators.required],
    });
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }
  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.equipmentSearch(this.month, this.today);
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "pdf-download",
        "equipmentReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("pdf-download", "equipmentReport_list");
    }
  }
  equipmentSearch(fromValue, toValue) {
    if (this.equipmentReportForm.valid) {
      const fromdate = moment(fromValue).format("YYYY/MM/DD");
      const todate = moment(toValue).format("YYYY/MM/DD");
      this.reports.getEquipmentReport(fromdate, todate).subscribe((res) => {
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.setPageSizeOptions();
      });
    }
  }
  getDate() {
    if (
      this.equipmentReportForm.get("from_date").value &&
      this.equipmentReportForm.get("to_date").value
    ) {
      if (
        this.equipmentReportForm.get("from_date").value >=
        this.equipmentReportForm.get("to_date").value
      ) {
        this.equipmentReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData = data || [];
    });
  }
}
