export interface BusRoutes {
    route_no: string;
    route_name: string;
    creation_time: string;
    start_time: string;
    monday: boolean;
    created_date: string;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
    start_location_name: string;
    end_location_name: string;
    no_of_locations: string;
    staff_name: string;
    remarks: string;
    requestor: string;
    status: string;
    route_id: string;
}

