// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = Object.freeze({
  production: false,
  //base_url: 'http://localhost:57839/',
    //dashboard_url: 'http://localhost:4202/login?token='
    //CGH
  base_url: "https://uemssginternal.azurewebsites.net/portering/cghadminapi/",  // UEMS
  dashboard_url: 'https://uetracksg.com/portering/cghadmindashboard?token=' // UEMS
 
  //base_url: "https://cguemsvsapp01cg.ses.shsu.com.sg/ApiPorteringAdmin/", // UAT
  //dashboard_url: "http://cguemsvsweb01cg.ses.shsu.com.sg/UETrackAdminDashboard?token=", // UAT

  //base_url: "https://cguemsvpapp01cg.shses.shs.com.sg/ApiPorteringAdmin/", // Live
  //dashboard_url: "https://cguemsvpapp01cg.shses.shs.com.sg/UETrackAdminDashboard?token=", // Live
});

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
