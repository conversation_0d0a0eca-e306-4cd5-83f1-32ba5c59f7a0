import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder } from "@angular/forms";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import * as moment from "moment";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";

export interface KpiTable01 {
  total_jobs: string;
  perc: string;
  heading: string;
  urgenttotal_jobs: string;
  urgentperc: string;
  nonurgenttotal_jobs: string;
  nonurgentperc: string;
}
export interface KpiTable02 {
  heading: string;
  grand_total: string;
  passed_kpi: string;
  passed_kpi_percent: string;
  missed_kpi: string;
  missed_kpi_percent: string;
}
@Component({
  selector: "app-kpi-summary-report",
  templateUrl: "./kpi-summary-report.component.html",
  styleUrls: [
    "./kpi-summary-report.component.scss",
    "../../../scss/table.scss",
  ],
})
export class KpiSummaryReportComponent implements OnInit {
  routes = [
    // { path: './../transportReports', label: 'Task Type Summary' },
    // { path: './../locationSearch', label: 'Location Summary' },
    // { path: './../equipmentmove', label: 'Equipment Summary' },
    // { path: './../hourReport', label: 'Hourly Summary' },
    // { path: './../kpiReport', label: 'KPI Report' },
  ];
  table01: KpiTable01[] = [
    { heading: "Total number of jobs requested", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
    { heading: "Total number of jobs cancelled", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
    { heading: "Total number of jobs completed", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
    { heading: "Responded within 30 mins", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
    { heading: "AVG Response time (mins)", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
    { heading: "AVG Completion time (mins)", total_jobs: "--", perc: "--",urgenttotal_jobs: "--", urgentperc: "--",nonurgenttotal_jobs: "--", nonurgentperc: "--" },
  ];
  table02: KpiTable02[] = [
    {
      heading: "Urgent",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Patient Move ",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Non-Patient Move ",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Non-Urgent",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Patient Move ",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Non-Patient Move ",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
    {
      heading: "Grand Total ",
      grand_total: "--",
      passed_kpi: "--",
      passed_kpi_percent: "--",
      missed_kpi: "--",
      missed_kpi_percent: "--",
    },
  ];
  tables = [];
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];

  displayedColumns: string[] = [
    "staff_name",
    "no_jobs",
    "assign_resp",
    "assign_comp",
  ];
  pdfData = [];
  kpiReportForm: FormGroup;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  arrangeKPIReport: any;
  constructor(
    private readonly fb: FormBuilder,
    public inputValidation: InputValidationService,
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
    private readonly toastr: ToastrService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.getkpireports();
    });
  }

  createForm() {
    this.kpiReportForm = this.fb.group({
      fromdate: [moment().clone().startOf("month").toDate()],
      todate: [moment().toDate()],
    });
  }

  getkpireports() {
    const x = {
      fromdate: moment(this.kpiReportForm.get("fromdate").value).format(
        "YYYY/MM/DD"
      ),
      todate: moment(this.kpiReportForm.get("todate").value).format(
        "YYYY/MM/DD"
      ),
    };
    this.reportsService.getKpiReport(x.fromdate, x.todate).subscribe((res) => {
      if (res) {
        const { KpiSummary } = res;
        this.arrangeKPIReport = KpiSummary.reduce((all, acc) => {
          if (acc.RequestType == "Urgent") {
            all[0] = { ...acc, header_name: "Urgent", make_bold: true };
          }
          if (acc.RequestType == "Urgent PM") {
            all[1] = { ...acc, header_name: "Patient Move" };
          }
          if (acc.RequestType == "Urgent NPM") {
            all[2] = { ...acc, header_name: "Non-Patient Move" };
          }
          if (acc.RequestType == "Non-Urgent") {
            all[3] = { ...acc, header_name: "Non-Urgent", make_bold: true };
          }
          if (acc.RequestType == "Non-Urgent PM") {
            all[4] = { ...acc, header_name: "Patient Move" };
          }
          if (acc.RequestType == "Non-Urgent NPM") {
            all[5] = { ...acc, header_name: "Non-Patient Move" };
          }
          if (acc.RequestType == "Total") {
            all[6] = { ...acc, header_name: "Grand Total", make_bold: true };
          }
          return all;
        }, []);
        this.table01[0].total_jobs = res.TotalJobs;
        this.table01[1].total_jobs = res.CancelledJobs;
        this.table01[2].total_jobs = res.CompletedJobs;
        this.table01[0].perc = "--";
        this.table01[1].perc = res.CancelledJobsPercentage;
        this.table01[2].perc = res.CompletedJobsPercentage;
        this.table01[0].urgenttotal_jobs = res.UrgentTotalJobs;
        this.table01[1].urgenttotal_jobs = res.UrgentCancelledJobs;
        this.table01[2].urgenttotal_jobs = res.UrgentCompletedJobs;
        this.table01[0].urgentperc = "--";
        this.table01[1].urgentperc = res.UrgentCancelledJobsPercentage;
        this.table01[2].urgentperc = res.UrgentCompletedJobsPercentage;
        this.table01[0].nonurgenttotal_jobs = res.NonUrgentTotalJobs;
        this.table01[1].nonurgenttotal_jobs = res.NonUrgentCancelledJobs;
        this.table01[2].nonurgenttotal_jobs = res.NonUrgentCompletedJobs;
        this.table01[0].nonurgentperc = "--";
        this.table01[1].nonurgentperc = res.NonUrgentCancelledJobsPercentage;
        this.table01[2].nonurgentperc = res.NonUrgentCompletedJobsPercentage;
        this.table01[3].total_jobs = res.ResJobs;
        this.table01[3].perc = res.ResJobsPercentage;
        this.table01[3].urgenttotal_jobs = res.UrgentResJobs;
        this.table01[3].urgentperc = res.UrgentResJobsPercentage;
        this.table01[3].nonurgenttotal_jobs = res.NonUrgentResJobs;
        this.table01[3].nonurgentperc = res.NonUrgentResJobsPercentage;
        this.table01[4].total_jobs = res.AVGresponsetime;
        this.table01[4].urgenttotal_jobs = res.UrgentAVGresponsetime;
        this.table01[4].nonurgenttotal_jobs = res.NonUrgentAVGresponsetime;
        this.table01[5].total_jobs = res.AVGcompletiontime;
        this.table01[5].urgenttotal_jobs = res.UrgentAVGcompletiontime;
        this.table01[5].nonurgenttotal_jobs = res.NonUrgentAVGcompletiontime;

        this.createtables();
      }
    });
  }

  createtables() {
    this.tables = [
      {
        heads: ["Labels", "Total Jobs", "%"],
        datas: this.table01.filter((resdadta) => {
          const x = {
            Labels: resdadta.heading,
            "Total Jobs":
              resdadta.total_jobs !== "NaN" ? resdadta.total_jobs : "0",
            "%": resdadta.perc !== "NaN" ? resdadta.perc : "0",
            "Urgent Jobs" : resdadta.urgenttotal_jobs !== "NaN" ? resdadta.urgenttotal_jobs : "0",
            "%.": resdadta.urgentperc !== "NaN" ? resdadta.urgentperc : "0",
            "Non Urgent Jobs" : resdadta.nonurgenttotal_jobs !== "NaN" ? resdadta.nonurgenttotal_jobs : "0",
            ".%": resdadta.nonurgentperc !== "NaN" ? resdadta.nonurgentperc : "0",
          };
          Object.assign(resdadta, x);
          return resdadta;
        }),
      },
      {
        heads: [
          "Labels",
          "Grand Total",
          "Passed KPI",
          "Passed KPI %",
          "Missed KPI",
          "Missed KPI %",
        ],
        datas: this.arrangeKPIReport.map((data) => {
          return {
            Labels: data.header_name,
            "Grand Total": data.TotalJobs,
            "Passed KPI": data.PassedKpi,
            "Passed KPI %": data.PassedKpiPercent + "%",
            "Missed KPI": data.MissedKpi,
            "Missed KPI %": data.MissedKpiPercent,
          };
        }),
      },
    ];
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "kpireportid",
        "KpiReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("kpireportid", "KpiReport_list");
    }
  }

  getDate() {
    if (
      this.kpiReportForm.get("fromdate").value &&
      this.kpiReportForm.get("todate").value
    ) {
      if (
        this.kpiReportForm.get("fromdate").value >=
        this.kpiReportForm.get("todate").value
      ) {
        this.kpiReportForm.get("todate").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
