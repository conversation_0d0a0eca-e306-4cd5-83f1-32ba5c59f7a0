import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-location',
  templateUrl: './add-update-location.component.html',
  styleUrls: ['./add-update-location.component.scss']
})
export class AddUpdateLocationComponent implements OnInit {
  towers = {};
  locationType: any = [];
  locationPriority: any = [];
  towerName: any = [];
  levelName: any = [];
  locationForm: FormGroup;
  randomNo: any;
  towerLevels: any = [];
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService
  ) {
    this.locationForm = this.fb.group({
      location_type: [''],
      potering: [''],
      location_name: ['', Validators.required],
      short_description: ['', Validators.required],
      location_sap_code: [''],
      tower_id: ['', Validators.required],
      level_id: ['', Validators.required],
      priority: ['', Validators.required],
      contact_no: [''],
      location_password: [''],
      virtual_location: [''],
      has_station_porter: [''],
      status: ['1'],
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.generateRandomNo();
    this.getLocationType();
    this.getLocationPriority();
    this.getTowerName();
    this.getLocationById();
  }
  generateRandomNo() {
    this.randomNo = Math.floor(1000 + Math.random() * 9000);
  }
  getLocationType() {
    this.facilityConfig.getLocationType().subscribe(res => {
      this.locationType = res;
    });
  }
  getLocationPriority() {
    this.facilityConfig.getPriority().subscribe(res => {
      this.locationPriority = res;
    });
  }
  getTowerName() {
    this.facilityConfig.getTowers().subscribe(res => {
      res = res && res.length && res.filter(val => {
        if (val.status) {
          return val;
        }
      }) || [];
      this.towerName = res;
    });
  }
  getLocationById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getLocationById(this.activatedRoute.snapshot.params.id)
        .subscribe(res => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          this.locationForm.patchValue(res);
          this.towerSelected(res.tower_id);
          res.status = res.status === 1 ? '1' : '0';
          this.locationForm.patchValue(res);
        });
    }
  }
  towerSelected(towerId) {
    this.locationForm.get('level_id').setValue('');
    this.facilityConfig.getLevelsByTowerId(towerId).subscribe(res => {
      this.towerLevels = res;
      if (this.towerLevels === []) {
        this.locationForm.get('level_id').setValue('');
      }
    });
  }

  saveLocation(actiontype) {
    let locationId: any;
    if (this.locationForm.valid) {
      const data = this.locationForm.value;
      data.location_password = this.randomNo;
      if (actiontype === 'update') {
        locationId = Number(this.activatedRoute.snapshot.params.id);
        if (data.approval_status && data.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.updateLocation(data, actiontype, locationId);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.updateLocation(data, actiontype, locationId);
        }
      } else {
        this.updateLocation(data, actiontype, locationId);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.locationForm.markAllAsTouched();
    }
  }

  updateLocation(data, actiontype, locationId) {
    if (data.potering === '') {
      data.potering = false;
    }
    if (data.virtual_location === '') {
      data.virtual_location = false;
    }
    if (data.has_station_porter === '') {
      data.has_station_porter = false;
    }
    this.facilityConfig.addUpadteLevels(
      data, actiontype === 'save' ?
      'api/locations/add' : `api/locations/edit/${locationId}`, actiontype)
      .subscribe(res => {
        this.location.back();
      }, err => {
        console.log(err);
      });
  }
}

