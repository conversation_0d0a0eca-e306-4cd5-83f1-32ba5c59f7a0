import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';

@Component({
  selector: 'app-add-update-standard-time-configuration',
  templateUrl: './add-update-standard-time-configuration.component.html',
  styleUrls: ['./add-update-standard-time-configuration.component.scss']
})
export class AddUpdateStandardTimeConfigurationComponent implements OnInit {
  stdTimeConfigurationForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService
  ) {
    this.stdTimeConfigurationForm = this.fb.group({
      std_code: ['', Validators.required],
      start_time: ['', Validators.required],
      end_time: ['', Validators.required],
      normal_kpi: ['', [Validators.required]],
      advance_kpi: ['', [Validators.required]],
      emergency_kpi: ['', [Validators.required]],
      total_time: ['']
    });
  }



  ngOnInit() {
    this.stdTimeConfigurationForm.get('total_time').disable();
    this.getstdTimeById();
  }

  standardTotalTime() {
    if (this.stdTimeConfigurationForm.get('start_time').value < 1) {
      this.stdTimeConfigurationForm.get('start_time').setValue('');
    }
    if (this.stdTimeConfigurationForm.get('end_time').value < 1) {
      this.stdTimeConfigurationForm.get('end_time').setValue('');
    }
    this.stdTimeConfigurationForm.get('total_time').setValue(
      this.stdTimeConfigurationForm.get('start_time').value + this.stdTimeConfigurationForm.get('end_time').value);
  }


  getstdTimeById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getStandardTimeById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.stdTimeConfigurationForm.patchValue(res || {});
          this.standardTotalTime();
        });
    }
  }

  saveStdTime(actiontype) {
    if (this.stdTimeConfigurationForm.valid) {
      const url = actiontype ===
        'add' ? 'api/timeconfigurations/add' :
        `api/timeconfigurations/edit/${this.activatedRoute.snapshot.params.id}`;
      this.facilityConfig.addStandardTime(
        this.stdTimeConfigurationForm.getRawValue(),
        actiontype, url)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} color code`, 'Success');
        }, err => {
          console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.stdTimeConfigurationForm.markAllAsTouched();
    }
  }
}
