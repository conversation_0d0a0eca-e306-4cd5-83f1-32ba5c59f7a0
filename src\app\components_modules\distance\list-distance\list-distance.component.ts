import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';

export interface DistanceData {
  from_tower_name: string;
  to_tower_name: string;
  total_time: number;
  distance_id: number;
}

@Component({
  selector: 'app-list-distance',
  templateUrl: './list-distance.component.html',
  styleUrls: ['./list-distance.component.scss', '../../../scss/table.scss']
})
export class ListDistanceComponent implements OnInit {
  pdfData = [];
  displayedColumns: string[] = ['from_tower_name', 'to_tower_name', 'total_time', 'distance_id'];
  dataSource: MatTableDataSource<DistanceData>;

  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  constructor(private readonly facilityConfig: FacilityConfigService, private readonly loader: LoaderService) { }

  ngOnInit() {
    this.distanceList();
  }
  distanceList() {
    this.facilityConfig.getDistanceList().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }


  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('distancetable', 'distance_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('distancetable', 'distance_list');
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'From Tower Name': key.from_tower_name,
          'To Tower Name': key.to_tower_name,
          'Total Time': key.total_time
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

}
