<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Manage Category and Mode of
                                        Transport</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Manage Category and Mode of
                                        Transport</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="categoryMapingForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Main Job Category<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="mj_category_id"
                                                            (ngModelChange)="getsubJobCategory($event)">
                                                            <mat-option *ngFor="let data of mainJobCategories"
                                                                [value]="data.mj_category_id">{{data.category_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="categoryMapingForm.controls.mj_category_id"
                                                                [fieldName]="'Main Job Category'"
                                                                [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Sub Category<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="sj_category_id">
                                                            <mat-option *ngFor="let data of subJobCategories"
                                                                [value]="data.sj_category_id">{{data.sj_category_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="categoryMapingForm.controls.sj_category_id"
                                                                [fieldName]="'Sub Category'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Mode of Transport<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="transport_mode_ids"
                                                            multiple="true">
                                                            <mat-option *ngFor="let data of modesOfTransport"
                                                                [value]="data.transport_mode_id">
                                                                {{data.transport_mode_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="categoryMapingForm.controls.transport_mode_ids"
                                                                [fieldName]="'Mode of Transport'"
                                                                [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right" (click)="save('add')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="save('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="categoryMapingForm.reset(); getLevelsById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>