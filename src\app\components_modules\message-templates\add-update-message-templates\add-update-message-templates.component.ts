import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';


@Component({
  selector: 'app-add-update-message-templates',
  templateUrl: './add-update-message-templates.component.html',
  styleUrls: ['./add-update-message-templates.component.scss']
})
export class AddUpdateMessageTemplatesComponent implements OnInit {
  messageForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly settingsService: SettingsService
  ) {
    this.messageForm = this.fb.group({
      message_template: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.getMessageTemplate();
  }

  getMessageTemplate() {
    if (this.activatedRoute.snapshot.params.id) {
      this.settingsService.getMessageTemplate(this.activatedRoute.snapshot.params.id).subscribe(res => {
        this.messageForm.patchValue(res ? res : {});
      }, err => {
        console.log(err);
      });
    }
  }

  saveMessage(actiontype) {
    if (this.messageForm.valid) {
      this.settingsService.addUpdateMessageTemplate(this.messageForm.value, actiontype,
        this.activatedRoute.snapshot.params.id).subscribe(() => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'add' ? 'added' : 'updated'} message template`, 'Success');
        }, err => {
          console.log(err);
        });

    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.messageForm.markAllAsTouched();
    }
  }

}
