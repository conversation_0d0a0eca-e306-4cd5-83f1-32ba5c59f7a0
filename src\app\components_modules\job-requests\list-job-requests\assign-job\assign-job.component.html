<!-- Modal -->
<div class="row">
  <div class="col-12">
    <div class="">
      <p class="text-center bold__display">
        {{ jobNumber.actionType }} (Job Number - {{ jobNumber.order_no }})
      </p>
    </div>
  </div>
  <div class="col-12">
    <div class="border p-3">
      <form [formGroup]="actionForm">
        <!-- <legend class="scheduler-border">
        {{ jobNumber.actionType }} (Job Number - {{ jobNumber.order_no }})
      </legend> -->
        <div class="row">
          <div
            *ngIf="
              jobNumber.actionType === 'Cancel' ||
              jobNumber.actionType === 'Delay Reason'
            "
            class="col-12"
          >
            <mat-form-field>
              <mat-label
                >Reason
                <span class="error-css"><span class="error-css">*</span></span>
              </mat-label>
              <mat-select
                formControlName="reason"
                (ngModelChange)="cancelReasonChange($event)"
              >
                <mat-option
                  *ngFor="let data of reasons"
                  [value]="
                    data?.cancel_template_id
                      ? data?.cancel_template_id
                      : data?.delay_id
                  "
                >
                  {{
                    data?.cancel_template
                      ? data?.cancel_template
                      : data?.delay_reason
                  }}
                </mat-option>
              </mat-select>
              <mat-error class="pull-left error-css">
                <app-error-message
                  [control]="actionForm.controls.reason"
                  [fieldName]="'Reason'"
                  [fieldType]="'select'"
                >
                </app-error-message>
              </mat-error>
            </mat-form-field>
            <br />
          </div>
          <div
            *ngIf="
              jobNumber.actionType === 'Assign' ||
              jobNumber.actionType === 'Reassign'
            "
            class="col-12"
          >
            <mat-form-field>
              <mat-label
                >Staff
                <span class="error-css"><span class="error-css">*</span></span>
              </mat-label>
              <mat-select
                formControlName="staff"
                (ngModelChange)="staffChanged($event)"
                #singleSelect
              >
                <mat-option>
                  <ngx-mat-select-search
                    [formControl]="staffFilterCtrl"
                    [placeholderLabel]="'Find staff...'"
                    [noEntriesFoundLabel]="'no matching staff found'"
                  ></ngx-mat-select-search>
                </mat-option>

                <mat-option
                  *ngFor="let data of filteredStaffs | async"
                  [value]="data.staff_id"
                >
                  {{ data.staff_name }}
                </mat-option>
              </mat-select>
              <mat-error class="pull-left error-css">
                <app-error-message
                  [control]="actionForm.controls.staff"
                  [fieldName]="'Staff'"
                  [fieldType]="'select'"
                >
                </app-error-message>
              </mat-error>
              <mat-hint>Porter name – Porter number (Skillset)</mat-hint>
            </mat-form-field>
            <br />
          </div>
          <div
            class="col-12"
            *ngIf="
              jobNumber.actionType === 'Assign' ||
              jobNumber.actionType === 'Reassign'
            "
          >
            <div class="row">
              <div class="col-4">
                <div *ngIf="staffStatus" class="staff-image">
                  <img
                    class="img-fluid staff-image-size"
                    [src]="imageUrl?.result"
                    alt="Staff Image"
                  />
                </div>
              </div>
              <div class="col-8">
                <!-- show table -->
                <table
                  class="table table-bordered assign__table"
                  *ngIf="staffStatus"
                >
                  <thead>
                    <tr>
                      <th scope="col">Total Jobs <sup>#</sup></th>
                      <th scope="col">Skill Set</th>
                      <th scope="col">Staff Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ toatlJobsStaff ? toatlJobsStaff : "-" }}</td>
                      <td>{{ skillSet ? skillSet : "-" }}</td>
                      <td>{{ staffStatus ? staffStatus : "-" }}</td>
                    </tr>
                  </tbody>
                </table>
                <br />
                <p *ngIf="staffStatus" class="job_description">
                  # Jobs done in last 8 hours
                </p>
                <!-- show table -->
              </div>
            </div>
          </div>
          <div
            *ngIf="
              (jobNumber.actionType === 'Cancel' && allowReamrks) ||
              (jobNumber.actionType === 'Delay Reason' && allowReamrks)
            "
            class="col-12"
          >
            <mat-form-field>
              <mat-label>Remarks <span class="error-css"></span> </mat-label>
              <input
                matInput
                #desc
                placeholder="Remarks"
                maxlength="300"
                formControlName="remarks"
              />
              <mat-hint style="text-align: end"
                >{{ desc.value.length }} / 300
              </mat-hint>
              <mat-error class="pull-left error-css">
                <app-error-message
                  [control]="actionForm.controls.remarks"
                  [fieldName]="'Remarks'"
                  [fieldType]="'enter'"
                >
                </app-error-message>
              </mat-error>
            </mat-form-field>
            <br />
          </div>
          <div
            *ngIf="
              jobNumber.actionType === 'Respond' ||
              jobNumber.actionType === 'Complete'
            "
            class="col-12"
          >
            <mat-form-field>
              <mat-label
                >{{
                  jobNumber.actionType === "Respond"
                    ? "Start Time"
                    : "Completion Time"
                }}
                <span class="error-css"></span>
              </mat-label>
              <input
                matInput
                #desc
                placeholder="Time"
                [value]="actionForm.controls.time.value"
                maxlength="300"
                formControlName="time"
              />
              <mat-error class="pull-left error-css">
                <app-error-message
                  [control]="actionForm.controls.time"
                  [fieldName]="'Time'"
                  [fieldType]="'enter'"
                >
                </app-error-message>
              </mat-error>
            </mat-form-field>
            <br />
          </div>
        </div>
      </form>
    </div>
    <!-- table -->
    <table
      *ngIf="
        jobNumber.actionType === 'Delay Reason' && delayReasonsofJob.length
      "
      id="classTable"
      class="table table-bordered"
    >
      <caption></caption>
      <thead>
        <th id="">Edit</th>
        <th id="">Delay Reason</th>
        <th id="">Remarks</th>
        <th id="">Date</th>
      </thead>
      <tbody>
        <tr *ngFor="let data of delayReasonsofJob">
          <td>
            <a href="javascript:void(0)" (click)="editDelayReason(data)"
              >Edit</a
            >
          </td>
          <td>{{ data.delay_reason }}</td>
          <td class="textBreak">{{ data.remark ? data.remark : "--" }}</td>
          <td>
            {{
              data.rsn_date
                ? (data.rsn_date | localDateConversion: "full")
                : "--"
            }}
          </td>
        </tr>
      </tbody>
    </table>
    <div class="row from-submit">
      <div class="col">
        <button
          mat-raised-button
          class="btn btn-primary pull-right"
          (click)="actionTaken()"
        >
          {{ isEditDelayReason ? "Update" : "Submit" }}
        </button>
        <button
          mat-raised-button
          class="btn btn-white pull-right"
          data-dismiss="modal"
          (click)="actionForm.reset(); createActionForm(); onNoClick()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
