import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StationViewStatusRoutingModule } from './station-view-status-routing.module';
import { StationViewStatusComponent } from './station-view-status.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { MatDialogModule } from '@angular/material/dialog';
import { BaseTimerComponent } from 'src/app/shared-theme/base-timer/base-timer.component';


@NgModule({
  declarations: [StationViewStatusComponent],
  imports: [
    CommonModule,
    StationViewStatusRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatDialogModule,
    SharedThemeModule
  ]
})
export class StationViewStatusModule { }
