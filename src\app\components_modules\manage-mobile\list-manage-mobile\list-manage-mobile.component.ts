import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { ManageMobile } from 'src/app/models/manageMobile';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';


@Component({
  selector: 'app-list-manage-mobile',
  templateUrl: './list-manage-mobile.component.html',
  styleUrls: ['./list-manage-mobile.component.scss', '../../../scss/table.scss']
})
export class ListManageMobileComponent implements OnInit {
  displayedColumns: string[] = ['mobile_no', 'edit', 'delete'];
  dataSource: MatTableDataSource<ManageMobile>;
  locationNames = [];
  pdfData = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  constructor(private readonly facilityConfig: FacilityConfigService,
              private readonly toaster: ToastrService,
              private readonly loader: LoaderService) { }
  ngOnInit() {
    this.getManageMobile();
  }
  getManageMobile() {
    this.facilityConfig.getManageMobile().subscribe(res => {
      res = res && res.filter(val => {
        val.mobile_no = val.phone_tag.concat(' - ', val.mobile_no);
        return val;
      });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('manageMobile', 'manageMobile_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('manageMobile', 'manageMobile_list');
    }
  }

  deleteMobileData(id) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.facilityConfig.deleteManageMobile(id).subscribe(res => {
          this.toaster.success('Successfully deleted std time code', 'Success');
          this.getManageMobile();
        }, err => {
          console.log(err);
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Mobile Number': key.mobile_no,
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }
}


