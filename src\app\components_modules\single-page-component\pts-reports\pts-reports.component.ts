import { TableUtil } from './../../../utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from './../../../Apiservices/facilityConfig/facility-config.service';
import { UserService } from './../../../Apiservices/userService/user.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import * as moment from "moment";
import { ReportsService } from '../../../Apiservices/reports/reports.service';

@Component({
  selector: 'app-pts-reports',
  templateUrl: './pts-reports.component.html',
  styleUrls: ['./pts-reports.component.scss']
})
export class PtsReportsComponent implements OnInit {
  routes = [
    { path: './../enhancedReports', label: 'Search (ACK)' },
    { path: './../busrouteSearch', label: 'Bus Route Reports' },
    { path: './../porterReports', label: 'Porter Workload' },
    { path: './../pts-reports', label: 'PTS Reports' },
  ];
  displayedColumns: string[] = [
    "ptsId",
    "cannisterId",
    "fromLocation",
    "toLocation",
    "patientName",
    "patientNRIC",
    "wardNo",
    "roomNo",
    "bedNo",
    "status",
    "reason",
    "remarks",
    "createdBy",
    "createdDate",
    "acceptedBy",
    "acceptedDate",
    "rejectedBy",
    "rejectedDate",
    "cancelledBy",
    "cancelledDate",
    // "medication_code",
    // "medication_name",
    // "qty",

  ];
  dataSource: MatTableDataSource<any>;
  exportResult: any;
  ptsReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  locations = [];
  locationCtrl = new FormControl();
  filteredLocation: Observable<any[]>;
  count = 0;
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  month = this.prevMonth["_d"];
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  constructor(
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    public router: Router,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly reports: ReportsService
  ) {
    this.createForm();
  }

  createForm() {
    this.ptsReportForm = this.fb.group({
      fromDate: [new Date()],
      toDate: [new Date()],
      location: [""]
    });
  }

  ngOnInit() {
    this.getLocations();
    this.getCurrentDateTime();
    this.filteredLocation = this.locationCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredLocation(category) : this.locations.slice()))
    );
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      // this.prevMonth = moment(this.today).subtract(1, "months");
      // this.month = this.prevMonth["_d"];
      this.createForm();
      this.searchByData();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  _filteredLocation(value: string) {
    const filterValue = value.toLowerCase();
    return this.locations.filter(
      (loc) => loc.location_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  restForm() {
    this.paginator.pageIndex = 0;
    this.paginator.pageSize = 50;
    this.createForm();
    this.searchByData();
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      this.locations = res;
      this.filteredLocation = this.locationCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filteredLocation(category) : this.locations.slice()))
      );
    });
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    this.searchByData(fileType);
  }

  mapTableDataforReport(exportResult, type?) {
    let mappedData;
    let exportObj: any[] = [];
    for (var i = 0; i < exportResult.length; i++) {
      mappedData = {
        "PTS ID": exportResult[i].ptsId,
        "Cannister ID": exportResult[i].cannisterId,
        "From Location": exportResult[i].fromLocation ? exportResult[i].fromLocation.toString().replace("\r\n", "") : "--",
        "To Location": exportResult[i].toLocation ? exportResult[i].toLocation.toString().replace("\r\n", "") : "--",
        "Patient Name": exportResult[i].patientName,
        "Patient NRIC": exportResult[i].patientNRIC,
        "Ward": exportResult[i].wardNo,
        "Room": exportResult[i].roomNo,
        "Bed": exportResult[i].bedNo,
        "Status": exportResult[i].status,
        "Reason": exportResult[i].reason,
        "Remarks": exportResult[i].remarks,
        "Created By": exportResult[i].createdBy,
        "Created Date": exportResult[i].createdDate,
        "Accepted By": exportResult[i].acceptedBy,
        "Accepted Date": exportResult[i].acceptedDate,
        "Rejected By": exportResult[i].rejectedBy,
        "Rejected Date": exportResult[i].rejectedDate,
        "Cancel By": exportResult[i].cancelledBy,
        "Cancel Date": exportResult[i].cancelledDate,
        // "Medication Code": exportResult[i],
        // "Medication Name": exportResult[i],
        // "Qty": exportResult[i],
      };
      exportObj.push(mappedData);
    }
    if (type == 'csv') {
      TableUtil.exportArrayToCsvDynamically(exportObj, "ptsJobs_list");
    } else {
      TableUtil.exportArrayToExcelDynamically(exportObj, "ptsJobs_list");
    }
  }

  searchByData(type?, paginationData?) {
    const data: any = this.userService.getOnlyFilledObjects(
      this.ptsReportForm.value
    );
    const fromdate = moment(
      this.ptsReportForm.get("fromDate").value
    ).format("YYYY/MM/DD");
    const todate = moment(this.ptsReportForm.get("toDate").value).format(
      "YYYY/MM/DD"
    );
    data.fromDate = fromdate;
    data.toDate = todate;
    data.pageNo = type && type == 'next' ? Number(paginationData.offset) + 1 : 1;
    data.pageSize = (type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xlsx') ? this.count : this.paginator.pageSize) || 50;
    if (
      (moment(data.fromDate).isValid() && moment(data.toDate).isValid())
    ) {
      this.reports.getPTSReportSearch(data).subscribe(
        (res) => {
          if (res && res.length > 0) {
            if (type && type == 'next') {
              this.exportResult = res;
              this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
              this.exportResult.push(...res);
              this.exportResult.length = res[0].count;
              this.dataSource = new MatTableDataSource<any>(this.exportResult);
              this.dataSource._updateChangeSubscription();
              this.dataSource.paginator = this.paginator;
            } else if (type && (type == 'xlsx' || type == 'csv')) {
              this.exportResult = res;
              this.mapTableDataforReport(this.exportResult, type);
            } else {
              this.exportResult = res;
              this.paginator.pageIndex = 0
              this.exportResult.length = res[0].count;
              this.count = res[0].count;
              this.paginator.length = res[0].count;
              this.dataSource = new MatTableDataSource(this.exportResult || []);
              this.dataSource.sort = this.sort;
              this.dataSource.paginator = this.paginator;
            }
          } else {
            this.paginator.length = 0;
            this.dataSource = new MatTableDataSource([]);
          }
        },
        (err) => {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
        }
      );
    } else {
      this.toastr.warning("Please have either by job No. or From & To date");
    }
  }

  getDate() {
    if (
      this.ptsReportForm.get("fromDate").value &&
      this.ptsReportForm.get("toDate").value
    ) {
      if (
        this.ptsReportForm.get("fromDate").value >
        this.ptsReportForm.get("toDate").value
      ) {
        this.ptsReportForm.get("toDate").setValue("");
        this.toastr.error("To date should be greater then From date", "Error");
      }
    }
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.searchByData('next', paginationData);
  }

  clearFormValue(formField: string) {
    this.ptsReportForm.get([formField]).setValue('')
  }

}
