import { Component, OnInit } from "@angular/core";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { UamReportsService } from "../uam-report/uam-reports.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";

@Component({
  selector: "app-role",
  templateUrl: "./role.component.html",
  styleUrls: ["./role.component.scss"],
})
export class RoleComponent implements OnInit {
  roles$: any;
  pdfData: any;

  constructor(
    private uamReportService: UamReportsService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.roles$ = this.uamReportService.getUAMRoles();
    this.setPageSizeOptions();
  }
  async exportTable(fileType) {
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "roleReportDumb",
        "role_report"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("roleReportDumb", "role_report");
    }
  }

  setPageSizeOptions() {
    this.roles$.subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              ROLE: key.role_name,
              Portering: key.portering,
              UEMS: key.uems,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
}
