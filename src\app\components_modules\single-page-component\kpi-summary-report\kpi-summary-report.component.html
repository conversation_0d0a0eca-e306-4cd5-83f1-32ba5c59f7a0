<div class="main-content">
  <div class="container-fluid">
    <app-summary-menu-list [routes]="routes"></app-summary-menu-list>
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li style="margin-left: 1%; line-height: 32px">
                  <p>View Summary KPI Report</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="kpiReportForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Summary KPI Report
                      </legend>
                      <div class="row">
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="kpiReportForm.controls.fromdate"
                            [fieldName]="FromDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="kpiReportForm.controls.todate"
                            [fieldName]="ToDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button
                            mat-raised-button
                            (click)="kpiReportForm.reset(); createForm()"
                            class="btn btn-white pull-right"
                          >
                            Reset
                          </button>
                          <button
                            mat-raised-button
                            type="submit"
                            class="btn btn-primary pull-right"
                            (click)="getkpireports()"
                          >
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
                <div class="col-md-12">
                  <div
                    class="mat-elevation-z8"
                    id="kpireportid"
                    style="overflow-x: auto"
                  >
                    <table>
                      <caption></caption>
                      <thead>
                        <tr>
                          <th id="" style="width: 30%;"></th>
                          <th id="">Total Jobs</th>
                          <th id="">%</th>
                          <th id="">Urgent Jobs</th>
                          <th id="">%</th>
                          <th id="">Non Urgent Jobs</th>
                          <th id="">%</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let el of table01">
                          <ng-container *ngIf="el.heading ==='AVG Response time (mins)' || el.heading ==='AVG Completion time (mins)' ? false : true">
                          <th scope="row">{{ el.heading }}</th>
                          <td>{{ el.total_jobs }}</td>
                          <td>
                            {{ el.perc !== "--" ? el.perc + "%" : el.perc }}
                          </td>
                          <td>{{ el.urgenttotal_jobs }}</td>
                          <td>
                            {{ el.urgentperc !== "--" ? el.urgentperc + "%" : el.urgentperc }}
                          </td>
                          <td>{{ el.nonurgenttotal_jobs }}</td>
                          <td>
                            {{ el.nonurgentperc !== "--" ? el.nonurgentperc + "%" : el.nonurgentperc }}
                          </td>
                        </ng-container>
                        <ng-container *ngIf="el.heading ==='AVG Response time (mins)' || el.heading ==='AVG Completion time (mins)' ? true : false">
                          <th scope="row">{{ el.heading }}</th>
                          <td colspan="2">{{ el.total_jobs }}</td>
                          <td colspan="2">{{ el.urgenttotal_jobs }}</td>
                          <td colspan="2">{{ el.nonurgenttotal_jobs }}</td>
                        </ng-container>
                        </tr>
                      </tbody>
                    </table>

                    <table class="spacing">
                      <caption></caption>
                      <thead>
                        <tr>
                          <th id=""></th>
                          <th id="">Grand Total</th>
                          <th id="">Passed KPI</th>
                          <th id="">Passed KPI %</th>
                          <th id="">Missed KPI</th>
                          <th id="">Missed KPI %</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr mdbTableCol *ngFor="let el of arrangeKPIReport">
                          <th
                            scope="row"
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{ el.header_name }}
                          </th>
                          <td
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{ el.TotalJobs }}
                          </td>
                          <td
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{ el.PassedKpi }}
                          </td>
                          <td
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{
                              el.PassedKpiPercent !== "NaN"
                                ? el.PassedKpiPercent + "%"
                                : "0"
                            }}
                          </td>
                          <td
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{ el.MissedKpi }}
                          </td>
                          <td
                            [ngStyle]="{
                              'font-weight': el.make_bold ? '600' : '200'
                            }"
                          >
                            {{
                              el.MissedKpiPercent !== "NaN"
                                ? el.MissedKpiPercent + "%"
                                : "0"
                            }}
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <div *ngIf="table01.length === 0">
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-multitablepdf
  [tables]="tables"
  [title]="'Summary KPI Report'"
></app-multitablepdf>
