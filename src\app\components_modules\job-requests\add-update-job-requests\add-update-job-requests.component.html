<div class="main-content">
  <div class="container-fluid">
    <!-- Loading Indicator -->
    <div *ngIf="loading$ | async" class="loading-container">
      <div class="loading-spinner">
        <mat-spinner diameter="50"></mat-spinner>
        <p class="loading-text">Loading job request data...</p>
      </div>
    </div>

    <div class="row" [style.opacity]="(loading$ | async) ? '0.5' : '1'">
      <div class="card card-nav-tabs mt-0">
        <form [formGroup]="jobRequestForm">
          <mat-accordion>
            <mat-expansion-panel [expanded]="true" class="mb-4 mat__expansion__panel__custom">
              <mat-expansion-panel-header style="background: #ececec; height: 53px">
                <mat-panel-title>
                  <div class="card__custom__header">
                    <p>New Job</p>
                  </div>
                  <div class="float-right d-flex" style="margin: -5px 0">
                    <button *ngIf="activatedRoute.snapshot.params.id" mat-raised-button color="primary"
                      (click)="goBack()">
                      Go back to View
                    </button>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <section>
                <div class="card-body">
                  <div class="tab-content text-center">
                    <div class="tab-pane active" id="serviceRequests">
                      <p class="scheduler-border border__requestor">
                        Requestor Details
                      </p>
                      <div class="row">
                        <div class="col-3">
                          <mat-form-field>
                            <mat-label>Requestor Name
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput placeholder="Enter requestor Name" formControlName="requester_name" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  jobRequestForm.controls.requester_name
                                " [fieldName]="'Requestor Name'" [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-3">
                          <mat-form-field>
                            <mat-label>Booking From
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="order_from" (ngModelChange)="bookingFrom($event)">
                              <mat-option *ngFor="let location of locations" [value]="location.location_id">
                                <!--{{ location.tower_name }} -
                                {{ location.level_name }} - -->
                                {{location.location_name}}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="jobRequestForm.controls.order_from"
                                [fieldName]="'Booking From'" [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-3">
                          <mat-form-field>
                            <mat-label>Time
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput placeholder="Enter Time" [ngModelOptions]="{ standalone: true }"
                              [(ngModel)]="todayTime" disabled="true" />
                          </mat-form-field>
                        </div>
                        <div class="col-3">
                          <mat-form-field>
                            <mat-label>
                              Contact No
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput #number maxlength="10" (keydown)="inputValidation.onlyNumbers($event)"
                              placeholder="Enter Contact No" autocomplete="off" formControlName="contact_no" />
                            <mat-hint style="text-align: end">
                              {{ number.value.length }} / 10
                            </mat-hint>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="jobRequestForm.controls.contact_no"
                                [fieldName]="'Contact No'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </mat-expansion-panel>
          </mat-accordion>

          <div class="card cancelled-card"
            *ngIf="activatedRoute.snapshot.params.id && !activatedRoute.snapshot.params.type">
            <div class="card-body">
              <dl class="row">
                <dd class="col-2">Creation Time:</dd>
                <dt class="col-10">
                  {{
                  singleJob?.request_time
                  ? (singleJob?.request_time | localDateConversion: "full")
                  : "--"
                  }}
                </dt>
                <dd class="col-2">Job Status:</dd>
                <dt class="col-10">
                  {{ singleJob?.job_status ? singleJob?.job_status : "--" }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Cancelled'">
                  Cancelled By:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Cancelled'">
                  {{ singleJob?.cancelled_by ? singleJob?.cancelled_by : "--" }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Cancelled'">
                  Cancel Reason:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Cancelled'">
                  {{
                  singleJob?.cancel_reason ? singleJob?.cancel_reason : "--"
                  }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Cancelled'">
                  Cancel Time:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Cancelled'">
                  {{
                  singleJob?.cancellation_time
                  ? singleJob?.cancellation_time
                  : "--"
                  }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Assigned'">
                  Assigned By:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Assigned'">
                  {{ singleJob?.assigned_by ? singleJob?.assigned_by : "--" }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Assigned'">
                  Assigned Time:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Assigned'">
                  {{ singleJob?.assign_time ? singleJob?.assign_time : "--" }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Started'">
                  Responded By:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Started'">
                  {{ singleJob?.responded_by ? singleJob?.responded_by : "--" }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Started'">
                  Response Time:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Started'">
                  {{
                  singleJob?.responded_time
                  ? (singleJob?.responded_time
                  | localDateConversion: "full")
                  : "--"
                  }}
                </dt>
                <dd class="col-2" *ngIf="singleJob?.job_status === 'Completed'">
                  Completion Time:
                </dd>
                <dt class="col-10" *ngIf="singleJob?.job_status === 'Completed'">
                  {{
                  singleJob?.completion_time
                  ? (singleJob?.completion_time
                  | localDateConversion: "full")
                  : "--"
                  }}
                </dt>
              </dl>
            </div>
          </div>

          <div class="container-fluid">
            <div class="row">
              <div class="col-5">
                <p class="scheduler-border border__requestor m-0">
                  Job Details
                </p>

                <div class="card border p-3 m-0" style="height: 92%">
                  <div class="row">
                    <div class="col-12">
                      <!-- <mat-form-field>
                                                <mat-label>Request Type <span class="error-css"><span
                                                            class="error-css">*</span></span>
                                                </mat-label>
                                                <mat-select formControlName="request_type"
                                                    (ngModelChange)="requestType($event)">
                                                    <mat-option value="Normal">Normal</mat-option>
                                                    <mat-option value="Emergency">Urgent</mat-option>
                                                    <mat-option value="Advance">Advance</mat-option>
                                                </mat-select>
                                                <mat-error class="pull-left error-css">
                                                    <app-error-message [control]="jobRequestForm.controls.request_type"
                                                        [fieldName]="'Request Type'" [fieldType]="'select'">
                                                    </app-error-message>
                                                </mat-error>
                                            </mat-form-field> -->
                    </div>
                  </div>
                  <div class="row">
                    <div class="col">
                      <mat-form-field>
                        <mat-label>Job Category
                          <span class="error-css"><span class="error-css">*</span></span>
                        </mat-label>
                        <mat-select disableOptionCentering formControlName="main_category"
                          (ngModelChange)="getsubJobCategory($event)">
                          <mat-option class="font__weight__500" *ngFor="let data of mainJobCategories"
                            [value]="data.mj_category_id">
                            {{ data.mj_category_name }}
                          </mat-option>
                        </mat-select>
                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.main_category"
                            [fieldName]="'Main Job Category'" [fieldType]="'select'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                    <div class="col">
                      <mat-form-field>
                        <mat-label>Job Type
                          <span class="error-css"><span class="error-css">*</span></span>
                        </mat-label>
                        <mat-select disableOptionCentering formControlName="sub_category" (ngModelChange)="
                            getModesOfTransport($event, 'clicked')
                          " appSelectFirstOption (isFirst)="isSelectionIsFirst($event)">
                          <!-- <mat-option
                            class="font__weight__500"
                            *ngFor="let data of subJobCategories"
                            [value]="data.sj_category_id"
                          >
                            {{ data.sj_category_name }}
                          </mat-option> -->
                          <mat-option>
                            <ngx-mat-select-search [formControl]="jobTypeFilterCtrl"
                              [placeholderLabel]="'Find Job type...'" [noEntriesFoundLabel]="
                                'no matching job type found'
                              "></ngx-mat-select-search>
                          </mat-option>

                          <mat-option *ngFor="let subJob of filteredJobType | async" [value]="subJob.sj_category_id">
                            {{ subJob.sj_category_name }}</mat-option>
                        </mat-select>
                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.sub_category"
                            [fieldName]="'Sub Category'" [fieldType]="'select'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <!-- <div class="row" *ngIf="isUrgent || roundTrip">
                                        <div class="col" *ngIf="isUrgent">
                                            <br>
                                            <mat-label style="float: left;">Urgent :
                                            </mat-label>
                                            <mat-checkbox class="align-checkbox" formControlName="urgent">
                                            </mat-checkbox>
                                        </div>
                                        <div class="col"></div>
                                    </div> -->
                  <div class="row">
                    <div class="col">
                      <mat-form-field>
                        <mat-label>From
                          <span class="error-css"><span class="error-css">*</span></span>
                        </mat-label>
                        <mat-select disableOptionCentering formControlName="from_location"
                          (ngModelChange)="patientsBasedOnLoation()">
                          <mat-option>
                            <ngx-mat-select-search [formControl]="fromLocationFilterCtrl"
                              [placeholderLabel]="'Find Location...'" [noEntriesFoundLabel]="
                                'no matching location found'
                              "></ngx-mat-select-search>
                          </mat-option>

                          <mat-option *ngFor="
                              let location of filteredFromLocations | async
                            " [value]="location.location_id">
                            {{ location.location_name }}</mat-option>
                        </mat-select>

                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.from_location" [fieldName]="'From'"
                            [fieldType]="'select'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                    <div class="col">
                      <mat-form-field>
                        <mat-label>To
                          <span class="error-css"><span class="error-css">*</span></span>
                        </mat-label>
                        <mat-select disableOptionCentering class="font__weight__500" formControlName="to_location"
                          (ngModelChange)="patientsBasedOnLoation()">
                          <!-- <mat-option
                            *ngFor="let location of toLocationList"
                            [value]="location.location_id"
                          >
                            {{ location.tower_name }} -
                            {{ location.level_name }} -{{
                              location.location_name
                            }}</mat-option
                          > -->
                          <mat-option>
                            <ngx-mat-select-search ngModel [formControl]="toLocationFilterCtrl"
                              [placeholderLabel]="'Find location...'" [noEntriesFoundLabel]="
                                'no matching location found'
                              ">
                            </ngx-mat-select-search>
                          </mat-option>

                          <mat-option *ngFor="let location of filteredToLocations | async"
                            [value]="location.location_id">
                            {{ location.location_name }}</mat-option>
                        </mat-select>
                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.to_location" [fieldName]="'To'"
                            [fieldType]="'select'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="row" *ngIf="modesOfTransport.length">
                    <div class="col-12">
                      <mat-label style="float: left" class="font__weight__600">Equipment
                        <span class="error-css"><span class="error-css">*</span></span>
                      </mat-label>
                    </div>
                    <div class="col-12">
                      <mat-chip-list>
                        <mat-chip class="col-3 mat__chip" *ngFor="let data of modesOfTransport; let i = index"
                          (click)="!disabledCheckbox && getCurrentChipSelected(data, i)" [ngClass]="{
                            'mat-chip-active_value': activeChip === i
                          }">
                          <div class="row no-gutters w-100">
                            <div class="col-3" style="line-height: 6vh">
                              <em *ngIf="data.transport_mode_image.i_class" class="{{
                                  data.transport_mode_image.class
                                }} iconsize"></em>
                              <img *ngIf="data.transport_mode_image.img" [src]="data.transport_mode_image.class"
                                style="width: 20px; height: 20px" alt="no image found" />
                            </div>
                            <div class="col-9" style="line-height: 5vh">
                              <span> {{ data.transport_mode_name }}</span>
                            </div>
                          </div>
                        </mat-chip>
                      </mat-chip-list>
                      <mat-error class="pull-left error-css">
                        <app-error-message [control]="jobRequestForm.controls.transport_mode" [fieldName]="'Equipment'"
                          [fieldType]="'select'">
                        </app-error-message>
                      </mat-error>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col" *ngIf="roundTrip">
                      <mat-form-field>
                        <mat-label>Return Location
                          <span class="error-css"><span class="error-css">*</span></span>
                        </mat-label>
                        <mat-select formControlName="return_location">
                          <mat-option *ngFor="let location of locations" [value]="location.location_id">
                            {{ location.location_name }}</mat-option>
                        </mat-select>
                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.return_location"
                            [fieldName]="'Return Location'" [fieldType]="'select'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>

                  <!-- <div class="row">
                    <div class="col-12">
                      <mat-form-field>
                        <mat-label>Assign Porter </mat-label>
                        <mat-select
                          formControlName="porter_id"
                          disableOptionCentering
                        >
                          <mat-option
                            *ngFor="let porter of porters"
                            [value]="porter.staff_id"
                          >
                            {{ porter.staff_name }}</mat-option
                          >
                        </mat-select>
                        <mat-error class="pull-left error-css">
                          <app-error-message
                            [control]="jobRequestForm.controls.porter_id"
                            [fieldName]="'Assign Porter'"
                            [fieldType]="'select'"
                          >
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div> -->
                  <div class="row">
                    <mat-radio-group formControlName="request_type" class="col-12"
                      (ngModelChange)="requestType($event)">
                      <div class="row">
                        <mat-radio-button class="example-margin col-3" value="Normal"
                          [disabled]="!categoryOptionDisable.includes('N')">Normal
                        </mat-radio-button>
                        <mat-radio-button class="example-margin col-4" value="Emergency"
                          [disabled]="!categoryOptionDisable.includes('E')">Urgent
                        </mat-radio-button>
                        <mat-radio-button class="example-margin col-4" value="Advance"
                          [disabled]="!categoryOptionDisable.includes('A')">Advance
                        </mat-radio-button>
                      </div>

                      <mat-error class="pull-left error-css">
                        <app-error-message [control]="jobRequestForm.controls.request_type" [fieldName]="'Request Type'"
                          [fieldType]="'select'">
                        </app-error-message>
                      </mat-error>
                    </mat-radio-group>
                    <div class="col-12">
                      <div class="row" *ngIf="
                          jobRequestForm.get('request_type').value &&
                          jobRequestForm
                            .get('request_type')
                            .value.toLowerCase() === 'advance'
                        ">
                        <div class="col-6">
                          <div class="row no-gutters">
                            <div class="col-12">
                              <app-datep-picker [dateConfiguration]="dueDateConfiGuration"
                                [control]="jobRequestForm.controls.date" [fieldName]="dueDateConfiGuration.label"
                                (getDate)="getDateTime()" [fieldType]="'select'">
                              </app-datep-picker>
                            </div>
                            <!-- <div class="col-12">
                              <mat-error
                                class="float-right"
                                *ngIf="jobRequestForm.controls.date.errors"
                              >
                                Please select a proper date
                              </mat-error>
                            </div> -->
                          </div>
                        </div>
                        <div class="col-6 time__picker">
                          <div class="row no-gutters">
                            <div class="col-12">
                              <ngx-timepicker-field [controlOnly]="true" [defaultTime]="serverDateTime"
                                formControlName="time"></ngx-timepicker-field>
                            </div>
                            <div class="col-12">
                              <mat-error class="float-right" *ngIf="
                                  jobRequestForm.controls.time.errors &&
                                  jobRequestForm.controls.time.errors
                                    ?.advance_time
                                ">
                                Please enter date time 30 minutes from current
                                time
                              </mat-error>
                            </div>
                            <div class="col-12">
                              <div class="col-12">
                                <mat-hint class="float-right" style="color: #000000">Note: Enter the time that porter
                                  should
                                  arrive</mat-hint>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <mat-form-field>
                        <mat-label>Remarks </mat-label>
                        <textarea matInput #desc placeholder="Remarks" maxlength="300" formControlName="remarks"
                          rows="2"></textarea>
                        <mat-hint style="text-align: end">{{ desc.value.length }} / 300
                        </mat-hint>
                        <mat-error class="pull-left error-css">
                          <app-error-message [control]="jobRequestForm.controls.remarks" [fieldName]="'Remarks'"
                            [fieldType]="'enter'">
                          </app-error-message>
                        </mat-error>
                      </mat-form-field>
                    </div>
                    <div class="col-12">
                      <dl class="important__note font-italic" style="margin-top: 25px">
                        <dt style="color: #000000; font-weight: 600">
                          Important Notes:
                        </dt>
                        <dd style="color: #000000">
                          - Porter's waiting time is limited to 10 minutes only
                        </dd>
                        <dd style="color: #000000">
                          - Please enter casenotes number or general item type
                          in Remarks field
                        </dd>

                        <dd style="color: #000000">
                          - Porters alone are not allowed to send patients via
                          wheelchair with oxygen tank, drips and trachy.
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-7">
                <ng-container *ngIf="isNonPatientJob">
                  <p class="scheduler-border border__requestor m-0">
                    Patient Details
                  </p>
                  <div class="card m-0 p-3 border" style="height: 92%">
                    <div class="row mr-2 mb-2">
                      <ng-container *ngIf="isNonPatientJob">
                        <div class="col-4"
                          *ngIf="!activatedRoute.snapshot.params.id || activatedRoute.snapshot.params.type">
                          <mat-form-field>
                            <mat-label>Patient Name/NRIC
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select disableOptionCentering formControlName="patientsCtrl"
                              (ngModelChange)="selectedPatientChanged($event)">
                              <mat-option>
                                <ngx-mat-select-search [formControl]="patientCtrl"
                                  [placeholderLabel]="'Find Patient...'" [noEntriesFoundLabel]="
                                  'no matching patient found'
                                "></ngx-mat-select-search>
                              </mat-option>
                              <mat-option *ngFor="
                                let patient of filteredPatient | async
                              " [value]="patient.nameNric">
                                <span style="width: auto; font-size: small">{{
                                  patient.patient_name
                                  }}</span>
                                <small *ngIf="patient.NRIC !== ''">
                                  | IC: {{ patient.nric_no }}</small>
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="jobRequestForm.controls.patientsCtrl"
                                [fieldName]="'Patient Name/NRIC'" [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-1 separator"
                          *ngIf="!activatedRoute.snapshot.params.id || activatedRoute.snapshot.params.type">(or)</div>
                        <!-- <div class="col-5" *ngIf="isEditable && !activatedRoute.snapshot.params.id">
                        <div class="form-group">
                          <label
                            class="pull-left make_label"
                            style="margin-bottom: 2px"
                            >Scan NRIC</label
                          >
                          <input
                            type="text"
                            id="txt-nric"
                            class="form-control"
                            style="height: 30px"
                            (keyup)="eventTriggered($event)"
                          />
                        </div>
                      </div> -->
                        <div class="col-5"
                          *ngIf="isEditable && (!activatedRoute.snapshot.params.id  || activatedRoute.snapshot.params.type)">
                          <div class="form-group" style="margin: 0px">
                            <mat-form-field style="padding: .4375em 0;">
                              <mat-label> Scan/Enter NRIC </mat-label>
                              <input matInput placeholder="Scan NRIC" formControlName="scan_nric"
                                (keyup)="eventTriggered($event)" />
                              <mat-error class="pull-left error-css">
                                <app-error-message [control]="jobRequestForm.controls.scan_nric"
                                  [fieldName]="'scanNric'" [fieldType]="'enter'">
                                </app-error-message>
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-2"
                          *ngIf="isEditable && (!activatedRoute.snapshot.params.id || activatedRoute.snapshot.params.type)">
                          <button color="primary" type="button" (click)="scanNricClicked()"
                            class="btn btn-primary btn-sm">
                            Scan/Enter
                          </button>
                        </div>
                        <div class="col-4" *ngIf="otherpatient">
                          <mat-form-field>
                            <mat-label>Patient Name
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput placeholder="Patient Name" formControlName="patient_name" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="jobRequestForm.controls.patient_name"
                                [fieldName]="'Patient Name'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-1"></div>
                        <div class="col-5" *ngIf="otherpatient">
                          <mat-form-field>
                            <mat-label>IC <span class="error-css"></span>
                            </mat-label>
                            <input matInput placeholder="IC" formControlName="nric" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="jobRequestForm.controls.nric" [fieldName]="'IC'"
                                [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-2" *ngIf="otherpatient">
                          <button color="primary" class="btn btn-primary btn-sm" (click)="addOtherPatient()">
                            Add
                          </button>
                        </div>
                      </ng-container>
                      <div class="col-12">
                        <div class="table-responsive table-full-width"></div>
                        <table class="table table-bordered">
                          <caption></caption>
                          <thead>
                            <tr>
                              <th id="">Patient Name</th>
                              <th id="">NRIC</th>
                              <!-- <th id="">From Room</th> -->
                              <th id="">From Bed No</th>
                              <!-- <th id="">To Room</th> -->
                              <th id="">To Bed No</th>

                              <th colspan="2" scope="col" style="width: 170px;">
                                Return &nbsp;&nbsp;&nbsp;&nbsp;<span class="tab">Round</span><br />
                                Equipment <span class="tab">Trip</span>
                              </th>

                              <th id="">Delete</th>
                              <th></th>
                              <th></th>
                            </tr>
                          </thead>
                          <tbody>
                            <ng-container formArrayName="patients" *ngFor="
                              let patint of jobRequestForm.get('patients')[
                                'controls'
                              ];
                              let i = index
                            ">
                              <tr *ngIf="
                                jobRequestForm.get('patients')['length'] === 0
                              ">
                                <td colspan="7">-- No Patients added --</td>
                              </tr>
                              <tr>
                                <td [formGroupName]="i">
                                  {{ patint.controls.patient_name.value }}
                                </td>
                                <td [formGroupName]="i">
                                  {{ patint.controls.nric.value }}
                                </td>
                                <!-- <td [formGroupName]="i">
                                  <div class="form-group">
                                    <input type="text" placeholder="Room" class="form-control text-center"
                                      formControlName="from_room" />
                                    <mat-error class="pull-left error-css arraytableError">
                                    </mat-error>
                                  </div>
                                </td> -->
                                <td [formGroupName]="i">
                                  <div class="form-group">
                                    <input type="text" placeholder="Bed Number" class="form-control text-center"
                                      formControlName="from_bed" />
                                    <mat-error class="pull-left error-css arraytableError">
                                    </mat-error>
                                  </div>
                                </td>
                                <!-- <td [formGroupName]="i">
                                  <div class="form-group">
                                    <input type="text" placeholder="Room" class="form-control text-center"
                                      formControlName="to_room" />
                                    <mat-error class="pull-left error-css arraytableError">
                                    </mat-error>
                                  </div>
                                </td> -->
                                <td [formGroupName]="i">
                                  <div class="form-group">
                                    <input type="text" placeholder="Bed Number" class="form-control text-center"
                                      formControlName="to_bed" />
                                    <mat-error class="pull-left error-css arraytableError">
                                    </mat-error>
                                  </div>
                                </td>
                                <td colspan="2" style="width: 170px;">
                                  <div class="row">
                                    <div class="m-auto" style="padding-top: 12%;padding-left: 5%; display: flex; flex-wrap: wrap;">
                                      <mat-checkbox
                                      [disabled]="disabledCheckbox"
                                        [checked]="jobRequestForm.get('non_patient_option').value === 'Equipment'"
                                        (change)="onCheckboxChange($event, 'Equipment')">
                                        Return Equipment
                                      </mat-checkbox>
                                      <mat-checkbox
                                        [disabled]="disabledCheckbox"
                                        [checked]="jobRequestForm.get('non_patient_option').value === 'Sendback'"
                                        (change)="onCheckboxChange($event, 'Sendback')">
                                        Round Trip
                                      </mat-checkbox>
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  <button *ngIf="
                                    !activatedRoute.snapshot.params.id || activatedRoute.snapshot.params.type;
                                    else deletenotavailable
                                  " type="button" class="btn btn-xs btn-danger" style="cursor: pointer"
                                    (click)="deleteSelectedPatient(i)">
                                    Delete
                                  </button>
                                  <ng-template #deletenotavailable>
                                    NA
                                  </ng-template>
                                </td>
                                <td></td>
                                <td></td>
                              </tr>
                              <tr [formGroupName]="i">
                                <ng-container formGroupName="isolation_precaution">
                                  <td>
                                    <label class="pull-left make_label"
                                      style="color: black; line-height: 29px">Isolation Precaution
                                      <span class="error-css">*</span></label>
                                  </td>
                                  <ng-container>
                                    <td colspan="7">
                                      <div class="row">
                                        <div *ngFor="
                                          let isolationPre of isolationPrecautionList;
                                          let j = index
                                        " [ngClass]="
                                          isolationPre.value != 'Nil'
                                            ? 'col-2'
                                            : 'col-1 mr-4'
                                        ">
                                          <mat-checkbox class="checkbox" [formControlName]="isolationPre.value"
                                            (change)="
                                            onChange($event, isolationPre, j, i)
                                          ">
                                            {{ isolationPre.value }}
                                          </mat-checkbox>
                                        </div>
                                      </div>
                                    </td>
                                    <td></td>
                                  </ng-container>
                                </ng-container>
                              </tr>
                              <tr>
                                <td colspan="9">
                                  <mat-error class="pull-left error-css" *ngIf="
                                    jobRequestForm.get('patients')['controls'][
                                      i
                                    ]['controls']['isolation_precaution'][
                                      'touched'
                                    ] &&
                                    jobRequestForm.get('patients')['controls'][
                                      i
                                    ]['controls']['isolation_precaution'][
                                      'errors'
                                    ]
                                  ">
                                    <p>Please select any one precaution</p>
                                  </mat-error>
                                </td>
                              </tr>
                              <tr [formGroupName]="i">
                                <td colspan="9">
                                  <div class="row">
                                    <div class="col-4">
                                      <mat-form-field>
                                        <input type="text" matInput placeholder="Remarks" formControlName="remarks" />
                                      </mat-form-field>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </ng-container>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="!isNonPatientJob && !isPatientMove && isPatientMove!=null">
                  <div class="card m-0 p-3 border" style="height: 92%;top:4%">
                    <div class="row mr-2 mb-2">
                      <div class="col-12">
                        <div class="checkbox-group">
                          <mat-checkbox [disabled]="disabledCheckbox" [checked]="jobRequestForm.get('non_patient_option').value === 'Equipment'"
                            (change)="onCheckboxChange($event, 'Equipment')" class="mr-3">
                            Return Equipment
                          </mat-checkbox>
                          <mat-checkbox [disabled]="disabledCheckbox" [checked]="jobRequestForm.get('non_patient_option').value === 'Sendback'"
                            (change)="onCheckboxChange($event, 'Sendback')" style="margin-left: 10px">
                            Round Trip
                          </mat-checkbox>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>

              <div class="row w-100 m-0">
                <div class="col-5"></div>
                <div class="col-6 mt-3 mt-3">
                  <div class="" style="width: 30vw">
                    <button mat-raised-button
                      *ngIf="(!activatedRoute.snapshot.params.id || activatedRoute.snapshot.params.type) && isEditable"
                      class="btn btn-primary pull-right" (click)="submit('add')">
                      Submit
                    </button>
                    <button mat-raised-button
                      *ngIf="activatedRoute.snapshot.params.id && !activatedRoute.snapshot.params.type && isEditable"
                      class="btn btn-primary pull-right" (click)="submit('update')">
                      Update
                    </button>
                    <button mat-raised-button type="button" *ngIf="isEditable" (click)="resetNewJobPage()"
                      class="btn btn-white pull-right">
                      Reset
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>