import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  getValidatorErrorMessage(validatorName: string, fieldType: string, fieldName: string, validatorValue?: any, completeError?: any) {
    let config;
    if (validatorName === 'customError') {
      config = {
        customError: `${validatorValue}`,
      };
    } else {
      config = {
        required: `Please ${fieldType} ${fieldName ? fieldName.toLowerCase() : ''}`,
        minlength: `Minimum length ${validatorValue.requiredLength}`,
        pattern: `${completeError ? completeError : 'Please enter valid ' + fieldName.toLowerCase()} `,
        maxlength: `Maximum length is ${validatorValue.requiredLength}`,
        min: `${fieldName} value should not be lesser than ${validatorValue.min}`,
        max: `${fieldName} value should not be greater than ${validatorValue.max}`,
        alreadyExists: `${fieldName} already exists.`,
      };
    }
    return config[validatorName];
  }

  matchPassword(account: AbstractControl) {
    const password = account.get('password').value;
    const confirmPassword = account.get('confirm_password').value;
    if (password !== confirmPassword) {
      account.get('confirm_password').setErrors({ customError: 'Password does not match' });
    } else {
      return null;
    }
  }

  reverseCheckPassword(account: AbstractControl) {
    const password = account.get('password').value;
    const confirmPassword = account.get('confirm_password').value;
    if (password === confirmPassword) {
      account.get('confirm_password').clearValidators();
      account.get('confirm_password').updateValueAndValidity();
    } else {
      return null;
    }
  }

  checkBoxOn(checkField: AbstractControl) {
    const check = checkField.get('cb').value;
    if (check === false || check === null) {
      checkField.get('cb').setErrors({ filetypeError: 'Please accept terms and conditions' });
    } else {
      return;
    }
  }
}
