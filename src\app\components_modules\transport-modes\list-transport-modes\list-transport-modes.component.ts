import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { TransportModes } from 'src/app/models/transportModes';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-list-transport-modes',
  templateUrl: './list-transport-modes.component.html',
  styleUrls: ['./list-transport-modes.component.scss', '../../../scss/table.scss']
})

export class ListTransportModesComponent implements OnInit {
  displayedColumns: string[] = ['transport_mode_name', 'transport_description', 'transport_mode_image',
    'department', 'status', 'transport_mode_id'];
  dataSource: MatTableDataSource<TransportModes>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly masterDataService: MasterDataService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getTransportModes();
  }

  getTransportModes() {
    this.masterDataService.getModeOfTransports().subscribe(res => {
      res = res && res.filter(imgMode => {
        imgMode.transport_mode_image = this.imageParserChecker(imgMode.transport_mode_image);
        return imgMode;
      });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  imageParserChecker(image) {
    try {
      return JSON.parse(image)
    } catch (error) {
      return { "id": 2, "name": image, "class": image, "img": true }
    }
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('transporttable', 'transport_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('transporttable', 'transport_list');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Mode of Transport': key.transport_mode_name,
          'Short Description': key.transport_description,
          'Transport Mode': key.transport_mode_name,
          Department: 'Portering',
          Status: key.status ? 'Active' : 'Inactive',
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatetransportMode/${data.transport_mode_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatetransportMode/${data.transport_mode_id}`], { relativeTo: this.activatedRoute });
    }
  }

}
