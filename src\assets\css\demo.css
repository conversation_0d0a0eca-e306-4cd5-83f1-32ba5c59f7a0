html,
body {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

* {
  font-family: Verdana, sans-serif;
}
.nav-tabs .nav-item .nav-link.active {
  background-color: rgba(255, 255, 255, 0.00001);
}

.mat-select-panel mat-option.mat-option {
  height: unset !important;
}

.mat-option-text.mat-option-text {
  /* white-space: normal !important; */
}

.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label:not(.mat-empty):not(.mat-form-field-empty) {
  margin-bottom: 3px;
}

.nav-tabs .nav-item .nav-link {
  font-size: 14px;
}

.mat-select {
  margin-top: 5px !important;
}

#sectionsNav {
  background-color: white !important;
  border-bottom: 2px solid #1b2581;
}

.navbar {
  margin-bottom: 5px !important;
}

.main-panel .main-content {
  margin-top: 5px !important;
  padding: 0 15px !important;
  min-height: calc(100vh - 200px) !important;
}

.nav ul {
  list-style: none;
  background-color: #444;
  text-align: center;
  padding: 0;
  margin: 0;
}

.nav li {
  font-size: 1.2em;
  line-height: 40px;
  height: 40px;
  border-bottom: 1px solid #888;
}

.nav a {
  text-decoration: none;
  color: #fff;
  display: block;
  transition: 0.3s background-color;
}

.nav a:hover {
  background-color: #1b2581;
}

.nav a.active {
  background-color: #fff;
  color: #444;
  cursor: default;
}

@media screen and (min-width: 600px) {
  .nav li {
    width: 120px;
    border-bottom: none;
    height: 50px;
    line-height: 50px;
    font-size: 1.4em;
    display: inline-block;
    margin-right: -4px;
  }
}

.nav-tabs-wrapper ul li p {
  margin: 0px 38px;
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
}

.nav-tabs-navigation ul li p {
  margin: 0px 16px;
  color: #ffffff;
  font-size: 20px;
  font-weight: 500;
}

.nav-item-submenu.ng-star-inserted.active .nav-link {
  background-color: #eee !important;
}

.nav-item:hover {
  background: rgb(135, 139, 175);
  color: white !important;
}

.card
  [class*="card-header-"]:not(.card-header-icon):not(.card-header-text):not(.card-header-image) {
  height: 80px;
  border-radius: 10px;
}

legend {
  display: none;
}

.card [class*="card-header-"] {
  margin: 0;
  padding: 0;
  position: relative;
}

.card .card-header-primary .card-icon,
.card .card-header-primary:not(.card-header-icon):not(.card-header-text),
.card .card-header-primary .card-text {
  background: #1b2581;
}

.error-css {
  color: rgba(255, 0, 0, 1);
  text-align: center;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: pre;
}

.navbar-nav {
  height: 20px;
}

.navbar .collapse .navbar-nav .nav-item .nav-link {
  padding: 5px 15px;
  text-transform: none;
}

.navbar.navbar-expand-md.bg-light.navbar-light {
  height: 50px;
}

.footer .copyright {
  padding: 0 0 !important;
}

.footer {
  padding: 0 0 !important;
}

.card {
  margin-bottom: 0 !important;
}

.col-12 {
  flex: 1 0 auto !important;
}

a:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 10px;
  left: 0;
  background-color: #1b2581;
  visibility: hidden;
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}

li.active a:before {
  visibility: visible;
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

#serviceRequests form {
  border-radius: 10px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);
}

#serviceRequests form fieldset .row {
  margin: 20px;
}

.btn-white {
  border-radius: 4px;
  height: 40px;
  width: 120px;
  border: 1px solid #1b2581;
  color: #1b2581;
  margin-right: 10px;
}

.mat-button.btn.btn-primary,
.mat-raised-button.btn.btn-primary,
.mat-raised-button.btn:not([class*="mat-elevation-z"]).btn-primary,
.btn.btn-primary {
  height: 40px;
  width: 120px;
  border-radius: 4px;
  background-color: #1b2581;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border: 2px solid #1b2581;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: #1b2581;
}

.mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: #1b2581;
}

.from-submit {
  margin-top: 10px;
}
