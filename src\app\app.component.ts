import { SESSION } from './shared/constants';
import { BnNgIdleService } from 'bn-ng-idle';
import { Component, OnInit, ChangeDetectionStrategy, DoCheck, ChangeDetectorRef } from '@angular/core';
import { TitleService } from './Apiservices/page-title/title.service';
import { Subject, interval, Subscription, merge, fromEvent } from 'rxjs';
import { LoaderService } from './Apiservices/loader/loader.service';
import { Router, NavigationStart, NavigationEnd, NavigationCancel } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UserService } from './Apiservices/userService/user.service';
// import { UserIdleService } from 'angular-user-idle';
import { StorageService } from './Apiservices/stoargeService/storage.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit, DoCheck {
  blockTemplate: any;
  isLoading: Subject<boolean> = new Subject<boolean>();
  setTimerCount = 0;
  event: any;
  subscription: Subscription;

  constructor(
    private readonly titleService: TitleService,
    private readonly loaderService: LoaderService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly toaster: ToastrService,
    private readonly userService: UserService,
    // private userIdle: UserIdleService,
    private readonly storageService: StorageService,
    private readonly http: HttpClient,
    private bnIdle: BnNgIdleService
  ) {
    this.isLoading = this.loaderService.isLoading;
    this.loaderService.isLoading.subscribe(res => {
      if (res) {
        const body = Array.from(document.getElementsByTagName('body'));
        body.forEach((element: HTMLElement) => {
          element.style.pointerEvents = 'none';
        });
      } else {
        const body = Array.from(document.getElementsByTagName('body'));
        body.forEach((element: HTMLElement) => {
          element.style.pointerEvents = 'auto';
        });
      }
    });
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        const onlineOffline = navigator.onLine;
        if (onlineOffline === false) {
          this.toaster.info('There is no internet connection, Please connect to network');
        }
        this.loaderService.isLoading.next(true);
      }
      if (event instanceof NavigationEnd || event instanceof NavigationCancel) {
        this.loaderService.isLoading.next(false);
      }
    });
  }

  ngOnInit() {
    this.titleService.updateTitle();
    this.checkISldap();
    this.bnIdle.startWatching(SESSION.sessionTimeOut).subscribe((isTimedOut: boolean) => {
      if (isTimedOut) {
        console.log('session expired', SESSION.sessionTimeOut);
        this.storageService.removeAllData();
        this.toaster.error(`You have been Idle for ${SESSION.sessionTimeOut/60} minutes. Please logged In again to continue.`);
          this.router.navigate(["/"]);
      }
    });
    const source = interval(SESSION.sessionTimeOut*1000); 
    this.subscription = source.subscribe(val => this.refresh());
  }

  refresh() {
    this.http.get(`${environment.base_url}api/refresh_token`).subscribe(res => {
      const element: any = res;
      this.storageService.setData("access_token", element);
    },
    (error) => {
      this.storageService.setData("access_token", '');
      this.router.navigate(["/"]);
    }); 
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
  }

  checkISldap() {
    this.userService.checkIsLDAP().subscribe(res => {
      this.userService.isldap.next(res && res.ad_status || false);
      this.userService.logoUrl.next(res && res.ad_imgurl || '');
    });
  }

  ngDoCheck() {
    this.cdr.detectChanges();
  }
}
