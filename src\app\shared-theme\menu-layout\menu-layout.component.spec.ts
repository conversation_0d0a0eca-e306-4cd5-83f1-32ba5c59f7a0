import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MenuLayoutComponent } from './menu-layout.component';
import { JasmineDependencyModule } from 'src/app/jasmine-dependency/jasmine-dependency.module';
import { TranslatePipePipe } from 'src/app/pipes/i18n/translate-pipe.pipe';

describe('MenuLayoutComponent', () => {
  let component: MenuLayoutComponent;
  let fixture: ComponentFixture<MenuLayoutComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [JasmineDependencyModule],
      declarations: [ MenuLayoutComponent, TranslatePipePipe ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MenuLayoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
