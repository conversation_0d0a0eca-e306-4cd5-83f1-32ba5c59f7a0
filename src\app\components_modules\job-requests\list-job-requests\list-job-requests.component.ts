import { Router, ActivatedRoute } from '@angular/router';
import {
  Component,
  OnInit,
  ViewChild,
  OnDestroy
} from "@angular/core";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  MatDialog,
  NativeDateAdapter,
  DateAdapter,
  MAT_DATE_FORMATS,
} from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { JobRequests } from "src/app/models/JobRequests";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import * as _moment from "moment";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { map, startWith, takeUntil } from "rxjs/operators";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import * as moment from "moment";
import { Observable, ReplaySubject, Subject } from "rxjs";
import { AssignJobComponent } from "./assign-job/assign-job.component";
import { MasterDataService } from "../../../Apiservices/masterData/master-data.service";
import { BaseTimerComponent } from 'src/app/shared-theme/base-timer/base-timer.component';
declare var $: any;

const staticColumn = {
  color_code: "color_code",
  "Job No": "order_no",
  Creation: "request_time",
  From: "from_location",
  To: "to_location",
  Task: "task",
  "Patient Info": "patient_name",
  "Porter Name": "porter_name",
  Actions: "order_id",
  "Cancel reason": "cancel_reason",
  "Delay reason": "delay_reason",
  "Assigned time": "assign_time",
  "Cancel time": "cancellation_time",
  "Responded time": "start_time",
  "Completion time": "completion_time",
  "Remarks": "remarks",
  "Ack by": "ack_by"
};

const displayLegends = {
  "In theQueue": "Queue",
  Responded: "Responded",
  Cancelled: "Cancelled",
  Completed: "Completed",
  Advance: "Advance",
  Assigned: "Assigned",
};
export const MY_FORMATS = {
  parse: {
    dateInput: "LL",
  },
  display: {
    dateInput: "YYYY-MM-DD",
    monthYearLabel: "YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "YYYY",
  },
};
export class AppDateAdapterTime extends NativeDateAdapter {
  format(date: Date, displayFormat: Object): string {
    if (displayFormat === "input") {
      let day: string = date.getDate().toString();
      day = +day < 10 ? "0" + day : day;
      let month: string = (date.getMonth() + 1).toString();
      month = +month < 10 ? "0" + month : month;
      let year = date.getFullYear();
      return `${day}-${month}-${year}`;
    }

    return moment(date).format("DD/MM/YYYY");
  }
}
@Component({
  selector: "app-list-job-requests",
  templateUrl: "./list-job-requests.component.html",
  styleUrls: ["../../../scss/table.scss", "./list-job-requests.component.scss"],
  providers: [
    {
      provide: DateAdapter,
      useClass: AppDateAdapterTime,
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class ListJobRequestsComponent implements OnInit, OnDestroy {

  allowReamrks = false;
  actionStatus = { value: "INQ", color_code: "" };
  displayedColumns: string[] = [
    "order_no",
    "request_time",
    "from_location",
    "to_location",
    "task",
    "patient_name",
    "porter_name",
    "order_id",
  ];
  public locationFilterCtrl: FormControl = new FormControl();
  public staffFilterCtrl: FormControl = new FormControl();
  public jobTypeFilterCtrl: FormControl = new FormControl();
  pdfData = [];
  colorCodes = [];
  filter = "";
  isEditDelayReason = false;
  reasons = [];
  delayReasonsofJob = [];
  editableDelayReasinId = "";
  staffs = [];
  allStaffs = [];
  staffStatus: any = "";
  toatlJobsStaff: any;
  imageUrl: any;
  reCalldata: any;
  hoveredJob: any;
  actionForm: FormGroup;
  jobData: any[];
  issearch: any = false;

  jobNumber = { order_id: "", actionType: "", order_no: "" };
  dataSource: MatTableDataSource<JobRequests> = new MatTableDataSource<JobRequests>([]);;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(BaseTimerComponent, { static: true }) baseTimer: BaseTimerComponent;
  htmlContent: string = "<p>Content goes here</p>";
  todaydate = moment().format("DD/MM/YYYY HH:mm");
  loggedInUserId: string;
  isButtonList: any[] = [];
  taskRequest = {
    job_no: null,
    patient: null,
    location_id: null,
    date: new Date(),
    //job_category: null,
    job_type: null,
    porter: null,
  };
  locationsList: any = [];
  protected _onDestroy = new Subject<void>();
  public filteredStaffs: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public filteredJobTypes: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  skillSet: any;
  length = 0;
  pageSize = 100;
  jobTypeList = [];
  exportResult = [];
  pageIndex = 0;
  constructor(
    private readonly jobService: JobsService,
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    public facilityConfig: FacilityConfigService,
    private dialog: MatDialog,
    private masterDataService: MasterDataService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    if (this.activatedRoute.snapshot.params && this.activatedRoute.snapshot.params.status) {
      this.actionStatus.value = this.activatedRoute.snapshot.params.status
    }
    this.createActionForm();
    this.loggedInUserId = atob(localStorage.getItem("user_id"));
    this.getSubJobCategory();
  }

  createActionForm() {
    this.actionForm = this.fb.group({
      reason: ["", Validators.required],
      time: [{ value: moment().format("DD/MM/YYYY HH:mm"), disabled: true }],
      staff: ["", Validators.required],
      remarks: [""],
    });
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  ngOnInit() {
    this.issearch = false;
    this.getCurrentDateTime();
    // this.fetchGridListData();
    this.getLocations();
    if (this.actionStatus.value === "In theQueue") {
      this.actionStatus.value = "INQ";
    }
    if (this.activatedRoute.snapshot.params && this.activatedRoute.snapshot.params.status) {
      this.actionStatus.value = this.activatedRoute.snapshot.params.status
    }
    this.getjobRequests(
      this.actionStatus.value ? this.actionStatus.value : "INQ", this.actionStatus.value || 'INQ'
    );
    // this.reCalldata = setInterval(() => {
    //   const isCodeExist = this.colorCodes.find(color => color.condition === this.actionStatus.value);
    //   if (isCodeExist) {
    //     this.actionStatus.color_code = isCodeExist.color_code;
    //   }
    //   if (this.actionStatus.value === 'In theQueue') {
    //     this.actionStatus.value = 'INQ';
    //   }

    //   this.getCurrentDateTime();
    // }, 30000);

    // real time search location
    this.filteredLocations.next(this.locationsList.slice());
    this.locationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterLocations();
      });
    // listen for search field value changes
    this.staffFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterStaff();
      });
    // real time search job types
    this.filteredJobTypes.next(this.jobTypeList.slice());
    this.jobTypeFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterJobType();
      });
    this.getAllStaff();
    this.filteredResources = this.resourceCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredResources(category) : this.staffs.slice()))
    );
  }

  getSubJobCategory() {
    this.masterDataService.getSubJobCategory().subscribe(res => {
      if (res) {
        res.filter(job => {
          if (job.status) {
            this.jobTypeList.push(job)
          }
        })
      }
      this.filterJobType();
    });
  }

  callThirtySecFun() {
    this.getjobRequests(
      this.actionStatus.value ? this.actionStatus.value : "INQ", this.actionStatus.value || 'INQ'
    );
  }

  protected filterStaff() {
    if (!this.staffs) {
      return;
    }
    // get the search keyword
    let search = this.staffFilterCtrl.value;
    if (!search) {
      this.filteredStaffs.next(this.staffs.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the staffs
    this.filteredStaffs.next(
      this.staffs.filter(
        (staff) => staff.staff_name.toLowerCase().indexOf(search) > -1
      )
    );
  }
  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    // this.taskRequest.fromLocationId =
    //   this.locations.length > 1 ? this.locations.slice()[0].id : "";
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }
  filterJobType() {
    if (!this.jobTypeList) {
      return;
    }
    // get the search keyword
    let search = this.jobTypeFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    if (!search) {
      this.filteredJobTypes.next(<any[]>this.jobTypeList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredJobTypes.next(
      this.jobTypeList.filter(
        (x) => x.sj_category_name.toLowerCase().indexOf(search) > -1
      )
    );
  }
  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.todaydate = res;
      this.createActionForm();
    });
  }

  getStaff() {
    this.staffs = [];
    this.jobService.getAssignstaff().subscribe((res) => {
      this.staffs = res || [];
      this.filteredStaffs.next(this.staffs.slice());
    });
  }

  openDialogAction(type?) {
    const dialogRef = this.dialog.open(AssignJobComponent, {
      width: "40%",
      height: "auto",
      disableClose: true,
      data: { jobNumber: this.jobNumber },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (this.issearch) {
          this.getjobRequestsById()
        } else {
          this.getjobRequests(
            this.actionStatus.value !== "In theQueue"
              ? this.actionStatus.value
              : "INQ", 'next'
          );
        }
        this.getColorCodes(type);
        this.baseTimer.start();
      } else {
        this.baseTimer.start();
      }
    });
  }

  getReasons(reason) {
    this.reasons = [];
    this.jobService
      .getReasons(
        reason === "cancel"
          ? "api/viewstatus/cancel/reason"
          : "api/viewstatus/delayreasons"
      )
      .subscribe((res) => {
        res =
          res &&
          res.filter((vals) => {
            if (vals.status) {
              return vals;
            }
          });
        this.reasons = res || [];
      });
  }

  viewJobDetailsHover(data) {
    this.hoveredJob = data;
  }

  getDelayReasons(jobNumber) {
    this.delayReasonsofJob = [];
    if (jobNumber) {
      this.jobService.getDelayReasonsForJob(jobNumber).subscribe((res) => {
        this.delayReasonsofJob = res || [];
      });
    }
  }

  async exportTable(fileType) {

    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "jobrequesttable",
        "jobrequesttable"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.getExcelData();
      // this.mapTableDataforReport(this.exportResult)
      // TableUtil.exportToExcel("jobrequesttable", "jobrequesttable");
    }
  }

  mapTableDataforReport(exportResult) {
    let mappedData;
    let exportObj: any[] = [];
    let task_time;
    for (var i = 0; i < exportResult.length; i++) {
      if (exportResult[i].request_type.toLowerCase() !== 'advance') {
        task_time = exportResult[i].request_time ? exportResult[i].request_time : "--";
      } else {
        task_time = exportResult[i].due_date ? exportResult[i].due_date : "--";
      }
      if (exportResult[i].request_time != "") {
        exportResult[i].request_time = moment(exportResult[i].request_time).format('MM-DD-YYYY hh:mm');
      }
      if (exportResult[i].assign_time != "") {
        exportResult[i].assign_time = moment(exportResult[i].assign_time).format('MM-DD-YYYY hh:mm');
      }
      if (exportResult[i].start_time != "") {
        exportResult[i].start_time = moment(exportResult[i].start_time).format('MM-DD-YYYY hh:mm');
      }
      if (exportResult[i].end_time != "") {
        exportResult[i].end_time = moment(exportResult[i].end_time).format('MM-DD-YYYY hh:mm');
      }
      if (exportResult[i].cancel_time != "") {
        exportResult[i].cancel_time = moment(exportResult[i].cancel_time).format('MM-DD-YYYY hh:mm');
      }

      mappedData = {
        "Job No": exportResult[i].order_no,
        "Task time": task_time,
        "From": exportResult[i].from_location ? exportResult[i].from_location.toString().replace("\r\n", "") : "--",
        "To": exportResult[i].to_location ? exportResult[i].to_location.toString().replace("\r\n", "") : "--",
        "Task": exportResult[i].sub_category,
        "Assigned time": exportResult[i].assign_time,
        "Cancel Time": exportResult[i].cancel_time,
        "Respond Time": exportResult[i].start_time,
        "Completion Time": exportResult[i].end_time,
        "Patient Info": exportResult[i].patient_name,
        "Porter Name": exportResult[i].porter_name,
        "Cancel reason": exportResult[i].cancel_reason,
      };
      exportObj.push(mappedData);
    }
    TableUtil.exportArrayToExcelDynamically(exportObj, "jobrequesttable");
  }

  getExcelData() {
    if (Object.keys(this.taskRequest).some((data) => this.taskRequest[data])) {
      const requestData = {} as any;
      requestData.location_id = this.taskRequest.location_id
        ? Number(this.taskRequest.location_id)
        : null;
      requestData.date = this.taskRequest.date
        ? moment(this.taskRequest.date).format("YYYY-MM-DD")
        : null;
      requestData.patient = this.taskRequest.patient
        ? this.taskRequest.patient
        : null;
      requestData.job_type = this.taskRequest.job_type
        ? this.taskRequest.job_type
        : null;
      requestData.job_requester = this.taskRequest.job_no
        ? this.taskRequest.job_no
        : null;
      this.jobService.getJobRequestByidSearch(requestData).subscribe((res) => {
        if (res) {
          this.exportResult = res || []
          this.mapTableDataforReport(this.exportResult)
        }
      })
    }
  }

  getColorCodes(type?) {
    // if((this.colorCodes && this.colorCodes.length == 0) || type){
    this.jobService.getColorCodes().subscribe((res) => {
      res =
        res &&
        res
          .map((status) => this.getDisplayName(status))
          .filter((val) => {
            // tslint:disable-next-line:no-string-literal
            val["statusCode"] = this.getStatusCode(val.condition);
            if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') || (this.actionStatus.value == val.condition)) {
              this.length = val.count
            }
            return val;
          });
      this.colorCodes = res || [];
      this.getMappedData()
    });
    // } else {
    //   this.getMappedData()
    // }
  }

  getMappedData() {
    this.colorCodes
      .map((status) => this.getDisplayName(status))
      .filter((val) => {
        // tslint:disable-next-line:no-string-literal
        val["statusCode"] = this.getStatusCode(val.condition);
        if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') || (this.actionStatus.value == val.condition)) {
          this.length = val.count
        }
        return val;
      });
    if (this.actionStatus.value === "INQ") {
      this.actionStatus.value = "In theQueue";
    }
    const code = this.actionStatus.value
      ? this.actionStatus.value
      : "In theQueue";
    const isCodeExist = this.colorCodes.find(
      (color) => color.condition === code
    );
    if (isCodeExist) {
      this.actionStatus.color_code = isCodeExist.color_code;
    }
    if (code) {
      this.colorCodes = this.colorCodes.map((data) =>
        (data.condition.includes(code)) || (code == 'Started' && data.condition == 'Responded') ? { ...data, show_active: true } : data
      );
    } else {
      this.colorCodes = this.colorCodes.map((data) =>
        data.condition == "In theQueue"
          ? { ...data, show_active: true }
          : data
      );
    }
  }

  getjobRequests(code?, condition?, nextPageData?) {
    this.baseTimer.stopTimerForDataLoadFun(true);
    this.fetchGridListData(condition == 'next' ? code : condition || this.actionStatus.value);
    this.getColorCodes();
    // if(this.colorCodes.length > 0){
    //   this.colorCodes.filter(val => {
    //     if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') || 
    //       (this.actionStatus.value.includes('Queue') && val.condition == 'In theQueue') || 
    //       (this.actionStatus.value.includes(val.condition))){
    //         this.length = val.count
    //    } 
    //   })
    // } else {
    //   this.getColorCodes();
    // }
    // make legend active
    this.colorCodes = this.colorCodes.map((data) => {
      if (data.condition == condition) {
        return { ...data, show_active: true };
      } else {
        return {
          ...data,
          show_active: false,
          background: "#FFFFFF",
          color: "#000000",
        };
      }
    });
    this.filter = "";
    this.dataSource = new MatTableDataSource([]);
    code = code === "Responded" ? "Started" : code == undefined || code.includes('Queue') ? 'INQ' : code;
    const data = {
      'jobStatus': code || "INQ",
      'pageNo': nextPageData ? Number(nextPageData.offset) + 1 : this.pageIndex + 1,
      'pageSize': nextPageData ? Number(nextPageData.limit) : this.pageSize
    }
    if (!nextPageData) {
      this.paginator.pageIndex = 0;
    }
    this.jobService.getJobRequests(data).subscribe(
      (res) => {
        if (res && res.length > 0) {
          this.actionStatus.value =
            res[0].job_status === "In Queue"
              ? "In theQueue"
              : res[0].job_status === "Started"
                ? "Responded"
                : res[0].job_status;
          if (this.colorCodes.length > 0) {
            this.colorCodes.filter(val => {
              if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') ||
                (this.actionStatus.value.includes('Queue') && val.condition == 'In theQueue') ||
                (this.actionStatus.value.includes(val.condition))) {
                this.length = val.count
              }
            })
          } else {
            this.getColorCodes();
          }
          this.jobData = res;
          if (nextPageData) {
            this.jobData.length = nextPageData.currentSize == 0 ? res[0].full_count : nextPageData.currentSize;
            this.jobData.push(...res);
            this.jobData.length = res[0].full_count;
            this.dataSource = new MatTableDataSource(
              this.jobData
                ? this.jobData.map((data) => ({
                  ...data,
                  transport_mode_image: data && data.transport_mode_image && data.transport_mode_image !== null ? JSON.parse(data.transport_mode_image) : '',
                }))
                : []
            );
            this.dataSource._updateChangeSubscription();
            this.dataSource.paginator = this.paginator;
            this.baseTimer.stopTimerForDataLoadFun(false);
          } else {
            this.jobData.length = res[0].full_count;
            this.dataSource = new MatTableDataSource(
              this.jobData
                ? this.jobData.map((data) => ({
                  ...data,
                  transport_mode_image: data && data.transport_mode_image && data.transport_mode_image !== null ? JSON.parse(data.transport_mode_image) : '',
                }))
                : []
            );
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
            this.baseTimer.stopTimerForDataLoadFun(false);
          }
        } else {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
          this.baseTimer.stopTimerForDataLoadFun(false);
        }
      },
      () => {
        this.paginator.length = 0;
        this.dataSource = new MatTableDataSource([]);
        this.baseTimer.stopTimerForDataLoadFun(false);
      }
    );
  }

  getjobRequestsById(type?) {
    this.issearch = true;
    this.paginator.pageIndex = 0;
    if (Object.keys(this.taskRequest).some((data) => this.taskRequest[data])) {
      const requestData = {} as any;
      requestData.location_id = this.taskRequest.location_id
        ? Number(this.taskRequest.location_id)
        : null;
      requestData.date = this.taskRequest.date
        ? moment(this.taskRequest.date).format("YYYY-MM-DD")
        : null;
      requestData.patient = this.taskRequest.patient
        ? this.taskRequest.patient.trim()
        : null;
      requestData.job_type = this.taskRequest.job_type
        ? this.taskRequest.job_type
        : null;
      requestData.job_requester = this.taskRequest.job_no
        ? this.taskRequest.job_no
        : null;
      requestData.porter = this.taskRequest.porter
        ? this.taskRequest.porter
        : null;
      requestData.pageNo = this.pageIndex + 1;
      requestData.pageSize = this.pageSize;
      // this.router.navigate(["./app/search/searchjob"], {
      //   queryParams: { ...requestData, status: this.actionStatus.value },
      // });
      this.baseTimer.reset();
      this.jobService.getJobRequestByidSearch(requestData).subscribe((res) => {
        if (res) {
          if (res.length !== 0) {
            this.actionStatus.value =
              res[0].job_status === "In Queue"
                ? "In theQueue"
                : res[0].job_status === "Started"
                  ? "Responded"
                  : res[0].job_status;
            this.jobData = res;
            this.jobData.length = res[0].full_count;
            this.dataSource = new MatTableDataSource(this.jobData);
            this.baseTimer.start();
            this.dataSource.sort = this.sort;
            const code = this.actionStatus.value
              ? this.actionStatus.value
              : "In theQueue";
            const isCodeExist = this.colorCodes.find(
              (color) => color.condition === code
            );
            if (isCodeExist) {
              this.actionStatus.color_code = isCodeExist.color_code;
            }
            this.dataSource.paginator = this.paginator;
            // this.applyFilter(id);
          } else {
            this.dataSource = new MatTableDataSource([]);
            this.baseTimer.stopTimerForDataLoadFun(false);
          }
        }
      });
    }
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.baseTimer.reset();
    this.baseTimer.start();
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    if (this.issearch) {
      if (Object.keys(this.taskRequest).some((data) => this.taskRequest[data])) {
        const requestData = {} as any;
        requestData.location_id = this.taskRequest.location_id
          ? Number(this.taskRequest.location_id)
          : null;
        requestData.date = this.taskRequest.date
          ? moment(this.taskRequest.date).format("YYYY-MM-DD")
          : null;
        requestData.patient = this.taskRequest.patient
          ? this.taskRequest.patient
          : null;
        requestData.job_type = this.taskRequest.job_type
          ? this.taskRequest.job_type
          : null;
        requestData.job_requester = this.taskRequest.job_no
          ? this.taskRequest.job_no
          : null;
        requestData.pageNo = Number(offset) + 1;
        requestData.pageSize = Number(limit);
        this.baseTimer.stopTimerForDataLoadFun(true);
        this.jobService.getJobRequestByidSearch(requestData).subscribe((res) => {
          if (res) {
            this.jobData.length = currentSize == 0 ? res[0].full_count : currentSize;
            this.jobData.push(...res);
            this.jobData.length = res[0].full_count;
            this.dataSource = new MatTableDataSource<any>(this.jobData);
            this.dataSource._updateChangeSubscription();
            this.dataSource.paginator = this.paginator;
            this.baseTimer.stopTimerForDataLoadFun(false);
          }
        })
      }
    } else {
      const paginationData = {
        'currentSize': currentSize,
        'offset': offset,
        'limit': limit
      }
      this.getjobRequests(this.actionStatus.value, 'next', paginationData)
    }
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Job No": key.order_no ? key.order_no : "--",
              Creation: key.request_time ? key.request_time : "--",
              From: key.from_location ? key.from_location : "--",
              To: key.to_location ? key.to_location : "--",
              Equipment: key.transport_mode_name
                ? key.transport_mode_name
                : "--",
              Task: key.task ? key.task : "--",
              "Patient Info": key.patient_name ? key.patient_name : "--",
              "Porter Name": key.porter_name ? key.porter_name : "--",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  getStatusCode(code) {
    return {
      "In the Queue": "INQ",
      Responded: "Started",
      Cancelled: "Cancelled",
      Completed: "Completed",
      Advance: "Advance",
      Assigned: "Assigned",
    }[code];
  }
  getDisplayName(code) {
    return {
      ...code,
      display: displayLegends.hasOwnProperty(code.condition)
        ? displayLegends[code.condition]
        : code.condition,
    };
  }

  staffChanged(event) {
    if (event) {
      this.jobService.getStaffForJobrequestbyid(event).subscribe((res) => {
        this.staffStatus = res.status ? res.status : "0 Job(s) in hand";
        this.toatlJobsStaff = res.total_jobs;
        this.skillSet = res.skillset;

        if (res.staff_photo) {
          this.userService.downloadImages(res.staff_photo).subscribe((img) => {
            const blob = new Blob([img.body], {
              type: "image/png",
            });
            const oFReader = new FileReader();
            oFReader.readAsDataURL(blob);
            oFReader.onload = (oFREvent) => {
              this.imageUrl = oFREvent && oFREvent.target;
            };
          });
        }
      });
    }
  }

  actionTaken() {
    switch (this.jobNumber.actionType) {
      case "Assign":
        this.assignReassign("assign");
        break;
      case "Cancel":
        if (this.actionForm.get("reason").valid) {
          const x = this.actionForm.value;
          this.jobService
            .takeAction(`api/viewstatus/cancel/${this.jobNumber.order_id}`, x)
            .subscribe(
              () => {
                this.toastr.success("Job cancelled successfully", "Success");
                this.actionFormSubmiited();
              },
              (err) => {
                console.log(err);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Respond":
        this.startComplete("start", "startdate");
        break;
      case "Reassign":
        this.assignReassign("reassign");
        break;
      case "Delay Reason":
        if (this.actionForm.get("reason").valid) {
          const x = Object.assign({}, this.actionForm.value);
          const y = {
            job_no: this.jobNumber.order_no,
            delay_reason: x.reason,
            remark: x.remarks,
          };
          this.jobService
            .jobRequest(
              y,
              this.isEditDelayReason
                ? `api/viewstatus/delayreason/edit/${this.editableDelayReasinId}`
                : `api/viewstatus/delayreason/add`,
              this.isEditDelayReason ? "up" : "add"
            )
            .subscribe(
              () => {
                this.toastr.success(
                  "Delay reason added successfully",
                  "Success"
                );
                this.actionFormSubmiited();
              },
              (err) => {
                console.log(err);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Complete":
        this.startComplete("complete", "completion_date");
        break;
      case "Return Equipment":
        this.jobService
          .takeAction(
            `api/viewstatus/returnEquipment/${this.jobNumber.order_no}`
          )
          .subscribe(
            () => {
              this.toastr.success("Equipment returned successfully", "Success");
              this.actionFormSubmiited();
            },
            (err) => {
              console.log(err);
            }
          );
        break;
      case "Fetchback":
        this.jobService
          .takeAction(`api/viewstatus/fetchback/${this.jobNumber.order_no}`)
          .subscribe(
            () => {
              this.toastr.success("Fetched back successfully", "Success");
              this.actionFormSubmiited();
            },
            (err) => {
              console.log(err);
            }
          );
        break;
      default:
        this.actionFormSubmiited();
    }
  }

  startComplete(action, actionType) {
    const x = this.todaydate;
    const formattedDate = _moment(x).format("YYYY-MM-DD HH:mm ");
    this.jobService
      .takeAction(
        `api/viewstatus/${action}/${this.jobNumber.order_id}?${actionType}=${formattedDate}`
      )
      .subscribe(
        () => {
          this.toastr.success(`Job ${action}ed successfully`, "Success");
          this.actionFormSubmiited();
        },
        (err) => {
          console.log(err);
        }
      );
  }

  cancelReasonChange(reason) {
    if (reason) {
      const exist = this.reasons.find(
        (resn) => resn.cancel_template_id === Number(reason)
      );
      if (exist) {
        this.allowReamrks = exist.allow_remarks;
      }
      const existdelay = this.reasons.find(
        (resn) => resn.delay_id === Number(reason)
      );
      if (existdelay) {
        this.allowReamrks = existdelay.allow_remarks;
      }
    }
  }

  assignReassign(action) {
    if (this.actionForm.get("staff").valid) {
      const x = this.actionForm.get("staff").value;
      this.jobService
        .takeAction(
          `api/viewstatus/${action}/${this.jobNumber.order_id}?porterid=${x}`
        )
        .subscribe(
          () => {
            this.toastr.success(`Job ${action}ed successfully`, "Success");
            this.actionFormSubmiited();
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.actionForm.markAllAsTouched();
    }
  }

  editDelayReason(data) {
    this.isEditDelayReason = true;
    this.editableDelayReasinId = data.delay_rsn_id;
    this.actionForm.get("reason").setValue(Number(data.delay_reason));
    this.actionForm.get("remarks").setValue(data.remark);
  }

  actionFormSubmiited() {
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
    this.ngOnInit();
    this.actionForm.reset();
    $("#actionModal").modal("hide");
    this.createActionForm();
    this.isEditDelayReason = false;
    this.editableDelayReasinId = "";
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  dateStructure(date) {
    return date.replace("T", " ");
  }

  fetchGridListData(condition?) {
    let displayFormattedCols;
    this.userService
      .getViewStatusGridsByUser(this.loggedInUserId)
      .subscribe((column) => {
        if (column) {
          this.displayedColumns = [
            ...column
              .sort((a, b) => a.order - b.order)
              .filter((data) => staticColumn[data.grid_name])
              .map((data) => staticColumn[data.grid_name]),
          ];
          if (condition && condition !== 'reset') {
            if (condition == 'Advance' || condition == 'INQ' || condition.includes('Queue')) {
              displayFormattedCols = this.displayedColumns.filter(col => (col !== 'porter_name' && col !== 'assign_time' && col !== 'start_time' &&
                col !== 'completion_time' && col !== 'cancellation_time' && col !== 'delay_reason' && col !== 'cancel_reason' && col !== 'ack_by'))
            } else if (condition == 'Assigned') {
              displayFormattedCols = this.displayedColumns.filter(col => (col !== 'start_time' &&
                col !== 'completion_time' && col !== 'cancellation_time' && col !== 'cancel_reason' && col !== 'remarks' && col !== 'ack_by'))
            } else if (condition == 'Responded' || condition == 'Started') {
              displayFormattedCols = this.displayedColumns.filter(col => (col !== 'completion_time' && col !== 'cancellation_time' && col !== 'cancel_reason' && col !== 'remarks'))
            } else if (condition == 'Cancelled') {
              displayFormattedCols = this.displayedColumns.filter(col => (col !== 'completion_time' && col !== 'remarks' && col !== 'ack_by'))
            } else if (condition == 'Completed') {
              displayFormattedCols = this.displayedColumns.filter(col => (col !== 'cancellation_time' && col !== 'cancel_reason' && col !== 'remarks' && col !== 'ack_by'))
            }
            this.displayedColumns = displayFormattedCols || this.displayedColumns;
          } else {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'porter_name' && col !== 'assign_time' && col !== 'start_time' &&
              col !== 'completion_time' && col !== 'cancellation_time' && col !== 'delay_reason' && col !== 'cancel_reason' && col !== 'remarks' && col !== 'Ack by'))
            this.displayedColumns = displayFormattedCols
          }
          this.isButtonList = column
            .filter((data) => data.is_btn)
            .map((data) => data.grid_name);
        } else {
          /// TODO: Grid for new user
        }
      });
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locationsList = res;
      this.filterLocations();
    });
  }
  resetForm() {
    this.taskRequest.date = new Date();
    this.taskRequest.job_no = "";
    this.taskRequest.location_id = "";
    this.taskRequest.patient = "";
    this.taskRequest.job_type = "";
    this.taskRequest.porter = "";
    this.issearch = false;
    this.actionStatus.value = 'INQ';
    this.paginator.pageIndex = 0;
  }

  hoverLegendColor(colorCode, colorCodeJSON, textColor) {
    colorCodeJSON.background = colorCode;
    colorCodeJSON.color = textColor;
  }

  clearFormValue(formField: string) {
    this.taskRequest[formField] = "";
  }

  ngOnDestroy() {
    $("#actionModal").modal("hide");
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
  }

  resetData() {
    this.pageSize = 100;
    // this.actionStatus.value = 'INQ';
  }

  updateJob(data) {
    this.router.navigate([`./updatejobrequest/${data.order_id}`, { 'type': 'recreate', 'status': data.job_status }], { relativeTo: this.activatedRoute })
  }
  getSliceIndex(location, location2) {
    const endIndex = location2.lastIndexOf("-") + 1;
    let res = location.slice(0, endIndex);
    return res;
  }
  filteredResources: Observable<any[]>;
  resourceCtrl = new FormControl();
  _filteredResources(value: string) {
    const filterValue = value.toLowerCase();
    return this.allStaffs.filter(
      (category) => category.staff_name.toLowerCase().includes(filterValue)
    );
  }

  getAllStaff() {
    this.userService.getManageStaff().subscribe((res) => {
      res =
        res &&
        res.length &&
        res.filter((staff) => {
          if (staff.status) {
            return staff;
          }
        });
      this.allStaffs = res || [];
      this.filteredResources = this.resourceCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filteredResources(category) : this.allStaffs.slice()))
      );
    });
  }
}
