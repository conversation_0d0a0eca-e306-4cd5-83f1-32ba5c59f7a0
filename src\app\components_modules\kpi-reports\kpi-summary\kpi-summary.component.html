<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li>
                                    <p>KPI Summary</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12">
                                    <form [formGroup]="kpiForm">
                                        <br>
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">Filter Kpi Summary Report </legend>
                                            <div class="row">
                                                <div class="col-3">
                                                    <mat-form-field>
                                                        <input matInput [ngxMatDatetimePicker]="fromPicker"
                                                            placeholder="From Date" formControlName="fromDate"
                                                            [min]="minDate" [max]="maxDate" readonly>
                                                        <mat-datepicker-toggle matSuffix
                                                            [for]="fromPicker"></mat-datepicker-toggle>
                                                        <ngx-mat-datetime-picker #fromPicker
                                                            [showSpinners]="showSpinners" [showSeconds]="showSeconds"
                                                            [stepHour]="stepHour" [stepMinute]="stepMinute"
                                                            [stepSecond]="stepSecond" [touchUi]="touchUi"
                                                            [color]="color">
                                                        </ngx-mat-datetime-picker>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <mat-form-field>
                                                        <input matInput [ngxMatDatetimePicker]="toPicker"
                                                            placeholder="To Date" formControlName="toDate"
                                                            [min]="minDate" [max]="maxDate" readonly>
                                                        <mat-datepicker-toggle matSuffix
                                                            [for]="toPicker"></mat-datepicker-toggle>
                                                        <ngx-mat-datetime-picker #toPicker [showSpinners]="showSpinners"
                                                            [showSeconds]="showSeconds" [stepHour]="stepHour"
                                                            [stepMinute]="stepMinute" [stepSecond]="stepSecond"
                                                            [touchUi]="touchUi" [color]="color">
                                                        </ngx-mat-datetime-picker>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <button mat-raised-button type="submit"
                                                        class="btn btn-primary pull-left"
                                                        (click)="submitSearchForm()">Search</button>
                                                    <button mat-raised-button class="btn btn-white pull-left"
                                                        (click)="resetFilter()">Reset</button>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto">
                                        <table id="kpiSummary" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="Type">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
                                                <td mat-cell *matCellDef="let row">{{ row.Type }}</td>
                                            </ng-container>

                                            <ng-container matColumnDef="PatientMoves">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient Moves %
                                                </th>
                                                <td mat-cell *matCellDef="let row">{{ row.PatientMoves }}</td>
                                            </ng-container>
                                            <ng-container matColumnDef="NonPatientMoves">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Non Patient Moves %
                                                </th>
                                                <td mat-cell *matCellDef="let row">{{ row.NonPatientMoves }}</td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                                <br />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf [heads]="[
      'Type',
      'Patient Moves%',
      'Non Patient Moves%']" [title]="'KPI Summary'" [datas]="pdfData">
</app-table-for-pdf>