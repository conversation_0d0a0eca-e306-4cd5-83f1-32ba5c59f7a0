import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListTowersComponent } from './list-towers/list-towers.component';
import { AddUpdateTowerComponent } from './add-update-tower/add-update-tower.component';


const routes: Routes = [
  {path: '', component: ListTowersComponent},
  {path: 'addtower', component: AddUpdateTowerComponent},
  {path: 'updatetower/:id', component: AddUpdateTowerComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TowerRoutingModule { }
