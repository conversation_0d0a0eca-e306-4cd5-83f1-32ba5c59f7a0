import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { ToastrService } from 'ngx-toastr';
import Swal from 'sweetalert2';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
export interface UserData {
  condition: string;
  colorcode: string;
  color_code: string;
  color_id: string;
}

@Component({
  selector: 'app-list-color-codes',
  templateUrl: './list-color-codes.component.html',
  styleUrls: ['./list-color-codes.component.scss', '../../../scss/table.scss']
})
export class ListColorCodesComponent implements OnInit {
  displayedColumns: string[] = ['condition', 'color_code', 'edit', 'delete'];
  dataSource: MatTableDataSource<UserData>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly masterDataService: MasterDataService,
    private readonly toaster: ToastrService,
    private readonly loader: LoaderService
  ) {
  }

  ngOnInit() {
    this.getColorCodes();
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('leveltable', 'level_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('leveltable', 'level_list');
    }
  }

  getColorCodes() {
    this.masterDataService.getColorCodes().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }


  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deleteColorCode(id) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.masterDataService.delteColoCode(id).subscribe(res => {
          if (res) {
            this.toaster.success('Successfully deleted color code', 'Success');
            this.getColorCodes();
          } else {
            this.toaster.error('Error occured in deleting color code', 'Error');
          }
        }, err => {
          this.toaster.error('Error occured in deleting color code', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          Condition: key.condition,
          'Color Code': key.color_code,
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }
}

