import { Component, OnInit } from "@angular/core";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
  FormControl,
  AbstractControl,
} from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import * as _moment from "moment";
import { RoutingService } from "src/app/Apiservices/routingService/routing.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import * as moment from "moment";
import { filter, map, startWith, takeUntil } from "rxjs/operators";
import { Observable, ReplaySubject, Subject } from "rxjs";
import { StorageService } from "src/app/Apiservices/stoargeService/storage.service";
import { MatCheckboxChange, MatRadioChange } from "@angular/material";

export interface State {
  patient_name: string;
  nric_no: string;
  bed_no: string;
  nameNric: string;
}
enum Category {
  NAE = "NAE",
  NA = "NA",
  NE = "NE",
  AE = "AE",
  E = "E",
  A = "A",
  N = "N",
}
let time = "";
let selectedDataAdvance = new Date();
function checkAdvanceTime(control: AbstractControl): { [key: string]: any } {
  let v = control.value;

  if (v) {
    if (
      moment(time).diff(
        new Date(`${moment(selectedDataAdvance).format("YYYY/MM/DD")} ${v}`),
        "minutes"
      ) > -29
    ) {
      return { advance_time: true };
    }
  }

  return null;
}
@Component({
  selector: "app-add-update-job-requests",
  templateUrl: "./add-update-job-requests.component.html",
  styleUrls: ["./add-update-job-requests.component.scss"],
})
export class AddUpdateJobRequestsComponent implements OnInit {
  jobRequestForm: FormGroup;
  isNonPatientJob = false;
  subJobCategories = [];
  singleJob: any;
  porters = [];
  modesOfTransport = [];
  mainJobCategories = [];
  filteredPatients: any = [];
  patientList = [];
  states: State[] = [];
  locations = [];
  otherpatient = false;
  patients: FormArray;
  patientsLength = 0;
  isEditable = true;
  isolationPrecaution = false;
  dueDateConfiGuration = {
    rangePicker: false,
    collumns: "col-16",
    label: "Advance date(dd/mm/yyyy)",
    required: true,
    minDateStart: new Date(),
    maxDateStart: new Date(),
    minDateEnd: "",
    maxDateEnd: "",
  };
  makeExpansionOpen: boolean = false;
  uniqueId: "";
  roundTrip = false;
  isUrgent = false;
  todayTime;
  currentDatetime = "";
  isolationPrecautionList = [
    { value: "Nil", base: "N", disable: false },
    { value: "Contact", base: "C", disable: true },
    { value: "Droplet", base: "D", disable: true },
    { value: "Airborne", base: "A", disable: true },
    { value: "Protective", base: "P", disable: true },
  ];
  checkIsolationValidation: boolean = true;
  requestTypeValue: any;
  activeChip: any;
  equipmentData: any;
  toLocationList: any = [];

  categoryOptionDisable: string = "";
  isOtherPatientAdded = false;
  isNRICScan = false
  modesOfTransportData = [];
  patientCtrl = new FormControl();
  filteredPatient: Observable<any[]>;
  protected _onDestroy = new Subject<void>();
  public fromLocationFilterCtrl: FormControl = new FormControl();
  public jobTypeFilterCtrl: FormControl = new FormControl();

  public fromLocationCtrl: FormControl = new FormControl();
  public toLocationFilterCtrl: FormControl = new FormControl();
  public toLocationCtrl: FormControl = new FormControl();
  public filteredFromLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(
    1
  );
  public filteredToLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(
    1
  );

  public filteredJobType: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  // isolationPrecaution = [
  //   { N: false, disable: false },
  //   { C: false, disable: false },
  //   { D: false, disable: false },
  //   { A: false, disable: false },
  //   { P: false, disable: false },
  // ]
  serverDateTime: string;
  patientDetails: any;
  editJobFlag: any = false;
  serverTimeDate: any;
  radioData: any;
  isPatientMove: boolean = null;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService,
    private readonly reportsService: ReportsService,
    private readonly jobsServive: JobsService,
    private readonly router: Router,
    private readonly routingService: RoutingService,
    private readonly userService: UserService,
    private storageService: StorageService
  ) {
    const contact_no = storageService.getData("contact_no") && storageService.getData("contact_no") !== 'null' ? storageService.getData("contact_no") : "";
    this.jobRequestForm = this.fb.group({
      request_type: ["Normal", Validators.required],
      contact_no: [contact_no, Validators.required],
      requester_name: ["", Validators.required],
      order_from: ["", Validators.required],
      main_category: ["", Validators.required],
      sub_category: ["", Validators.required],
      from_location: ["", Validators.required],
      to_location: ["", Validators.required],
      transport_mode: ["", Validators.required],
      remarks: [""],
      porter_id: [""],
      nric: [""],
      urgent: [false],
      return_location: [""],
      patient_name: ["", Validators.pattern('^[a-zA-Z.,_ ]+$')],
      patientsCtrl: ["", Validators.required],
      scan_nric: [""],
      time: ["", Validators.compose([checkAdvanceTime])],
      date: [new Date()],
      due_time: [""],
      patients: this.fb.array([]),
      radioData: [''],
      non_patient_option: ['']
    });

    this.patientsLength =
      (this.jobRequestForm.get("patients").value &&
        this.jobRequestForm.get("patients").value.length) ||
      0;
  }

  ngOnInit() {
    this.getLocations();
    // this.getPorter();
    this.getUserId();
    this.getAdminName();
    // this.getCurrentDateTime();
    this.requestType("Normal");
    this.getCurrentServerTime();
    this.filteredPatient = this.patientCtrl.valueChanges.pipe(
      startWith(""),
      map((state) => (state ? this._filterStates(state) : this.states.slice()))
    );
    this.filteredFromLocations.next(this.locations.slice());
    this.fromLocationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterFromLocations();
      });
    this.filteredToLocations.next(this.locations.slice());
    this.toLocationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterToLocations();
      });
    this.filteredJobType.next(this.subJobCategories.slice());
    this.jobTypeFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterJobTypes();
      });

    this.getJobRequestByid();

    this.jobRequestForm.get("date").valueChanges.subscribe((data) => {
      setTimeout(
        () => this.jobRequestForm.get("time").updateValueAndValidity(),
        400
      );
    });
    this.dueDateConfiGuration.maxDateStart = new Date(
      new Date().setDate(new Date().getDate() + 7)
    );
  }
  _filterStates(value: string): State[] {
    const filterValue = value.toLowerCase();
    return this.states.filter(
      (state) => state.patient_name.toLowerCase().indexOf(filterValue) === 0
    );
  }
  getServerTimeDate(data) {
    this.serverDateTime = moment(data).add(30, "minutes").format("hh:mm a");
    this.jobRequestForm.get("time").setValue(this.serverDateTime);
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.todayTime = moment(res).format("HH:mm");
    });
  }

  getAdminName() {
    const res = this.routingService.decodeToken();
    this.jobRequestForm.get("requester_name").setValue(res.unique_name);
  }

  getDateTime() {
    if (
      this.jobRequestForm.get("time").value &&
      this.jobRequestForm.get("date").value
    ) {
      const time = this.jobRequestForm.get("time").value;
      const formattedDate = _moment(
        this.jobRequestForm.get("date").value
      ).format("YYYY-MM-DD");
      const isEndsWithPm = time.endsWith("pm");
      const date =
        formattedDate +
        "T" +
        (isEndsWithPm
          ? String(Number(time.slice(0, 2)) + 12)
          : time.slice(0, 2)) +
        ":" +
        time.slice(3, 5) +
        ":00";
      this.jobRequestForm.get("due_time").setValue(date);
    }
    selectedDataAdvance = this.jobRequestForm.get("date").value;
  }

  requestType(eventValue) {
    const event = eventValue.toLowerCase();
    this.roundTrip = false;
    this.isUrgent = false;
    // this.mainJobCategories = [];
    // this.subJobCategories = [];
    // this.modesOfTransport = [];
    this.requestTypeValue = event;
    this.getMianJobCategory(event);
    // this.inputValidation.resetControls(this.jobRequestForm, ['main_category', 'sub_category',
    //   'transport_mode', 'urgent', 'return_location']);
    if (event === "advance") {
      this.inputValidation.setValidators(this.jobRequestForm, "time", [
        Validators.required,
        checkAdvanceTime,
      ]);
      this.inputValidation.setValidators(this.jobRequestForm, "date", [
        Validators.required,
      ]);
    } else {
      this.inputValidation.clearValidators(this.jobRequestForm, [
        "time",
        "date",
      ]);
    }
  }

  deleteSelectedPatient(i) {
    const array = this.jobRequestForm.get("patients") as FormArray;
    array.removeAt(i);
    this.patientsLength = array.value.length;
    if (array.value.length === 0) {
      this.jobRequestForm.get("patientsCtrl").reset();
    }
  }

  bookingFrom(event) {
    this.jobRequestForm.get("from_location").setValue(event);
  }

  createPatient(data): FormGroup {
    let patientForm: FormGroup;
    if (data) {
      const isoValueToString = Array.isArray(data.isolation_precaution)
        ? data.isolation_precaution.join("")
        : data.isolation_precaution;
      patientForm = this.fb.group({
        nric: [
          data.hasOwnProperty("nric_no") ? data.nric_no : "",
          Validators.required,
        ],
        patient_name: [
          data.hasOwnProperty("patient_name") ? data.patient_name : "",
          Validators.required,
        ],

        from_bed: [data.hasOwnProperty("bed_no") && data.bed_no !== null ? this.getPatientBedNo(data.bed_no) : ""],
        to_bed: [data.hasOwnProperty("to_bed") ? data.to_bed : ""],
        from_room: "",//[ data.hasOwnProperty("from_room") ? data.from_room : data.hasOwnProperty("bed_no") ? data.bed_no.slice(-4, -2) : "",],
        to_room: "",// [data.hasOwnProperty("to_room") ? data.to_room : ""],
        remarks: [data.hasOwnProperty("remarks") ? data.remarks : ""],
        isolation_precaution: this.fb.group(
          {
            Nil: [isoValueToString && isoValueToString.includes("N") ? true : false],
            Contact: [isoValueToString && isoValueToString.includes("C") ? true : false],
            Droplet: [isoValueToString && isoValueToString.includes("D") ? true : false],
            Airborne: [isoValueToString && isoValueToString.includes("A") ? true : false],
            Protective: [isoValueToString && isoValueToString.includes("P") ? true : false],
          },
          { validators: [this.checkIsolationPrecautionValidation] }
        ),

        equipment: data.equipment ? data.equipment : false,

        fetchback: data.fetchback ? data.fetchback : false,
        sendback: data.sendback ? data.sendback : false,
      });
    } else {
      patientForm = this.fb.group({
        nric: [
          this.changeNricToMaskedValue(
            this.patientDetails ? this.patientDetails.nric_no : ""
          ).toUpperCase(),
          Validators.required,
        ],
        patient_name: [
          this.patientDetails ? this.patientDetails.patient_name : "",
          Validators.required,
        ],
        from_bed: [""],
        to_bed: [""],
        from_room: [""],
        to_room: [""],
        remarks: [""],
        isolation_precaution: this.fb.group(
          {
            Nil: [false],
            Contact: [false],
            Droplet: [false],
            Airborne: [false],
            Protective: [false],
          },
          { validators: [this.checkIsolationPrecautionValidation] }
        ),
        sendback: false,
        equipment: false,
      });
    }

    return patientForm;
  }

  getCurrentServerTime() {
    this.facilityConfig.getServerTimeCurrent().subscribe((data) => {
      this.todayTime = moment(data).format("HH:mm");
      this.serverTimeDate = data;
      time = data;
      this.getServerTimeDate(data);
    });
  }

  addpatient(data, nricBased?, selectedPatientData?) {
    let isPatientCount = 0;
    const patients = this.jobRequestForm.get("patients") as FormArray;
    if (this.activatedRoute.snapshot.params.id) {
      patients.push(this.createPatient(data));
    } else {
      const paramData = {
        name: selectedPatientData ? selectedPatientData.split("|")[0].trim() : data.patient_name
      }
      this.jobsServive.getScanNric(data.nric_no, paramData).subscribe((patient) => {
        if (nricBased) {
          patients.value.filter(val => {
            if (patient && patient.patient_name == val.patient_name && patient.nric == val.nric_no) {
              isPatientCount++;
            }
          })
        }
        if (isPatientCount == 0) {
          patients.push(this.createPatient(patient));
        }
      });
    }
  }


  getPatientBedNo(bedNo) {
    let result = '';
    if (bedNo == null || bedNo.length <= 1) {
      return result;
    }
    let lastChar = bedNo.slice(-1);
    if (/[A-Za-z]/.test(lastChar)) {
      // Last char is an alphabet, take last 3 characters
      result = bedNo.slice(-3);
    } else {
      // Last char is a digit, take last 2 characters
      result = bedNo.slice(-2);
    }
    return result;
  }
  patientsBasedOnLoation() {
    if (
      this.jobRequestForm.get("to_location").value &&
      this.jobRequestForm.get("from_location").value
    ) {
      this.getpatients(
        this.jobRequestForm.get("to_location").value,
        this.jobRequestForm.get("from_location").value
      );
    }
  }

  getpatients(from, to) {
    this.reportsService.getPatientLog(from, to).subscribe((res) => {
      if (res && res.length) {
        res =
          res.filter((value) => {
            // tslint:disable-next-line:no-string-literal
            value["nameNric"] = value.patient_name + " | " + value.nric_no;
            return value;
          }) || [];
        this.states = <Array<State>>res;
      }
      this.states = [
        ...this.states,
        { patient_name: "Other", nric_no: "", bed_no: "", nameNric: "Other" },
      ];
      this.filteredPatients = this.states.slice();
      this.filteredPatient = this.patientCtrl.valueChanges.pipe(
        startWith(""),
        map((state) => (state ? this._filterStates(state) : this.states.slice()))
      );
    });
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locations = res;
      this.toLocationList = res;
      this.filterToLocations();
      this.filterFromLocations();
      this.jobRequestForm
        .get("order_from")
        .patchValue(Number(localStorage.getItem("locationid")));
      if (this.jobRequestForm.get("order_from").value == 0) {
        this.jobRequestForm.get("order_from").setValue(null)
      }
    });
  }

  filterFromLocations() {
    if (!this.locations) {
      return;
    }
    // get the search keyword
    let search = this.fromLocationFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    // this.taskRequest.fromLocationId =
    //   this.locations.length > 1 ? this.locations.slice()[0].id : "";
    if (!search) {
      this.filteredFromLocations.next(<any[]>this.locations.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredFromLocations.next(
      this.locations.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }
  filterToLocations() {
    if (!this.locations) {
      return;
    }
    // get the search keyword
    let search = this.toLocationFilterCtrl.value;
    if (!search) {
      this.filteredToLocations.next(<any[]>this.locations.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredToLocations.next(
      this.locations.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  filterJobTypes() {
    if (!this.subJobCategories) {
      return;
    }
    // get the search keyword
    let search = this.jobTypeFilterCtrl.value;
    if (!search) {
      this.filteredJobType.next(<any[]>this.subJobCategories.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredJobType.next(
      this.subJobCategories.filter(
        (x) => x.sj_category_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  // getPorter() {
  //   this.userService.getMessageStaffActive().subscribe((res) => {
  //     this.porters = res;
  //   });
  // }

  addOtherPatient() {
    if (
      this.jobRequestForm.get("patient_name").value &&
      this.jobRequestForm.get("nric").value
    ) {
      this.patientDetails = {
        nric_no: this.jobRequestForm.get("nric").value,
        patient_name: this.jobRequestForm.get("patient_name").value,
        nameNric:
          this.jobRequestForm.get("patient_name").value +
          " | " +
          this.changeNricToMaskedValue(this.jobRequestForm.get("nric").value),
      };
      this.selectedPatientChanged("", true, this.patientDetails, true);
      this.jobRequestForm.get("patientsCtrl").setValue(this.jobRequestForm.get("patient_name").value);
      this.jobRequestForm.get("nric").reset();
      this.jobRequestForm.get("patient_name").reset();
      this.isOtherPatientAdded = true;
      this.otherpatient = false;
    } else {
      this.toastr.info("Please add patient name and NRIC");
    }
  }

  eventTriggered(event) {
    if (this.jobRequestForm.value.scan_nric == '') {
      this.toastr.warning("Please enter NRIC for search", "Validation failed!");
      return;
    }
    if ((event.keyCode != 17) && (event.keyCode == 13 || event.target.value.length >= 9)) {
      this.scanNricClicked(event.keyCode);
    }
  }

  scanNricClicked(event?) {
    if (this.jobRequestForm.get("scan_nric").value) {
      if (
        this.jobRequestForm.get("to_location").value &&
        this.jobRequestForm.get("from_location").value
      ) {
        const paramData = {
          from: this.jobRequestForm.get("to_location").value,
          to: this.jobRequestForm.get("from_location").value
        }
        this.jobsServive
          .getScanNric(
            this.changeNricToMaskedValue(
              this.jobRequestForm.get("scan_nric").value
            ).toUpperCase(), paramData
          )
          .subscribe((res) => {
            if (res) {
              // tslint:disable-next-line:no-string-literal
              res["nameNric"] = res.patient_name + " | " + res.nric_no;
              this.selectedPatientChanged("", true, res);
              if (this.jobRequestForm.value.patients.length > 0) {
                this.jobRequestForm.get("patientsCtrl").setValue(this.jobRequestForm.value.patients[0].patient_name);
              }
              this.isNRICScan = true;
              this.jobRequestForm.get("scan_nric").reset();
            } else {
              this.otherpatient = true;
              this.jobRequestForm.get("nric").setValue(this.jobRequestForm.get("scan_nric").value);
              this.jobRequestForm.get("scan_nric").reset();
              this.toastr.error("Invalid NRIC");
            }
          });
      } else {
        this.jobRequestForm.get("from_location").markAsTouched();
        this.jobRequestForm.get("to_location").markAsTouched();
        this.toastr.info(
          "Please select from and to location",
          "Validation failed"
        );
      }
    } else {
      this.toastr.info("Please enter NRIC for search", "Validation failed");
    }
  }

  selectedPatientChanged(data, nricBased?, nricdata?, otherAdd?) {
    const value = data;
    if (value || otherAdd || nricBased) {
      if (value === "Other") {
        this.otherpatient = true;
      } else {
        if (otherAdd) {
          this.otherpatient = true;
        } else {
          this.otherpatient = false;
        }
        let isExist;
        let nric;
        if (nricBased) {
          isExist = nricdata;
          nric = nricdata.nameNric.split("|");
        } else {
          nric = value.split("|");
          isExist = this.states.find((pat) => pat.nric_no === nric[1].trim());
        }

        if (isExist) {
          let patients = this.jobRequestForm.get("patients").value;
          if (patients && patients.length) {
            const existingPatient = patients.find(
              (id) => id.nric === nric[1].trim()
            );
            if (!existingPatient) {
              this.addpatient(isExist, nricBased, data);
              patients = this.jobRequestForm.get("patients").value;
              if (patients.length > 1) {
                this.patientsLength = patients.length;
              }
            } else {
              this.toastr.info("NRIC already added");
            }
          } else {
            this.addpatient(isExist, nricBased, data);
            patients = this.jobRequestForm.get("patients").value;
            if (patients.length === 1) {
              this.patientsLength = patients.length;
            }
          }
        } else {
          this.addpatient({ nric_no: nric[1].trim() });
        }
      }
    }
    this.patientCtrl.setValue("");
  }

  clearFormArray(formArray: FormArray) {
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }
  }

  clearingDatabasedOnsubJob(event, clicked, pop?) {
    const existingCategory = this.subJobCategories.find(
      (id) => id.sj_category_id === Number(event)
    );
    if (existingCategory) {
      this.isUrgent = existingCategory.urgent;
      if (!this.isUrgent) {
        this.jobRequestForm.get("urgent").setValue(false);
      }
      this.roundTrip = existingCategory.round_trip;
      if (this.roundTrip) {
        this.inputValidation.setValidators(
          this.jobRequestForm,
          "return_location",
          [Validators.required]
        );
        if (clicked && !pop) {
          this.jobRequestForm
            .get("return_location")
            .setValue(this.jobRequestForm.get("order_from").value);
        }
      } else {
        this.inputValidation.clearValidators(
          this.jobRequestForm,
          "return_location"
        );
        this.jobRequestForm.get("return_location").setValue("");
      }
    }
  }

  async getModesOfTransport(event, clicked) {
    this.inputValidation.resetControls(this.jobRequestForm, [
      "transport_mode",
      "patientsCtrl",
    ]);
    if (clicked && !this.activatedRoute.snapshot.params.id) {
      this.clearFormArray(this.jobRequestForm.get("patients") as FormArray);
      this.patientsLength = 0;
      this.otherpatient = false;
    }

    this.clearingDatabasedOnsubJob(event, clicked);
    if (event && this.jobRequestForm.get("main_category").value) {
      this.selectCategoryDefault(this.jobRequestForm.get("sub_category").value);
      await this.jobsServive
        .getModesOfTransport(
          this.jobRequestForm.get("main_category").value,
          event
        )
        .subscribe((res) => {
          if (
            res &&
            res.transport_mode_list &&
            res.transport_mode_list.length
          ) {
            this.modesOfTransportData = res;
            this.modesOfTransport = res.transport_mode_list.map((data) => {
              return {
                ...data,
                transport_mode_image: JSON.parse(data.transport_mode_image),
              };
            });
            this.activeChip = null;
            if (this.modesOfTransport.length == 1 && !this.equipmentData) {
              this.getCurrentChipSelected(this.modesOfTransport[0], 0);
            }
            if (this.equipmentData) {
              this.activeChip = this.modesOfTransport.findIndex(
                (trans) => trans.transport_mode_name == this.equipmentData
              );
            }

            this.singleJob &&
              this.jobRequestForm
                .get("transport_mode")
                .patchValue(this.singleJob.transport_mode_id);

            this.jobRequestForm
              .get("transport_mode")
              .setValidators(Validators.required);

            this.getExternalMoveLocation(res.external_move);
            this.isPatientMove = res.is_patient_move;
            this.checkIsPatientIsEnable(res.is_patient_move);
            // this.getJobRequestByid();
          } else {
            this.jobRequestForm.get("transport_mode").clearValidators();
            this.jobRequestForm.get("transport_mode").reset();
            this.toastr.info(
              "There is no modes of transport for selected main and sub job category",
              "Data not found"
            );
          }
        });
    }
  }

  getUserId() {
    const res = this.routingService.decodeToken();
    this.uniqueId = res.UserId;
  }

  getMianJobCategory(event) {
    if (event) {
      this.jobsServive.getmjcatogiresByid(event).subscribe((res) => {
        if (res && res.length) {
          this.mainJobCategories = res;
        } else {
          this.subJobCategories = [];
          this.modesOfTransport = [];
          this.jobRequestForm.get("transport_mode").reset();
          this.jobRequestForm.get("main_category").reset();
          this.jobRequestForm.get("sub_category").reset();
          this.toastr.info(
            "There is no main job categories for selected request type",
            "Data not found"
          );
        }
      });
    }
  }

  getsubJobCategory(id) {
    this.isUrgent = false;
    this.roundTrip = false;
    this.inputValidation.resetControls(this.jobRequestForm, [
      "sub_category",
      "transport_mode",
      "urgent",
      "return_location",
    ]);
    if (id) {
      // if (id == 4) {
      //   this.isNonPatientJob = true;
      // } else {
      //   this.isNonPatientJob = false;
      // }
      this.modesOfTransport = [];
      this.subJobCategories = [];
      this.facilityConfig
        .getSubJobCategoryBasedOnRequestType(id, this.requestTypeValue)
        .subscribe((res) => {
          res =
            (res &&
              res.length &&
              res.filter((data) => {
                if (data.status) {
                  return data;
                }
              })) ||
            [];
          if (res && res.length) {
            this.subJobCategories = res;

            this.subJobCategories.length == 1 &&
              this.jobRequestForm.controls["sub_category"].patchValue(
                this.subJobCategories[0].sj_category_id
              );
            this.filterJobTypes();
          } else {
            this.jobRequestForm.get("transport_mode").reset();
            this.jobRequestForm.get("sub_category").reset();
            this.toastr.info(
              "There is no sub job categories for selected main job category",
              "Data not found"
            );
          }
          if (this.activatedRoute.snapshot.params.id) {
            this.clearingDatabasedOnsubJob(
              this.jobRequestForm.get("sub_category").value,
              "clciked",
              "id"
            );
          }
        });
    }
  }

  checkIsPatientIsEnable(subValue) {
    if (subValue) {
      this.isNonPatientJob = true;
    } else {
      this.isNonPatientJob = false;
    }
  }

  getExternalMoveLocation(value) {
    this.facilityConfig
      .getLocationsBySubjob(value)
      .pipe(filter((data) => data.filter((loca) => loca.status)))
      .subscribe((res) => {
        res =
          (res &&
            res.length &&
            res.filter((val) => {
              if (val.status) {
                return val;
              }
            })) ||
          [];
        this.locations = res;
        this.toLocationList = res;
        this.filterToLocations();
        this.filterFromLocations();
        // if (this.toLocationList.length > 1 && value) {
        //   this.jobRequestForm.controls["from_location"].patchValue(
        //     this.locations[0].location_id
        //   );
        //   this.jobRequestForm.controls["order_from"].patchValue(
        //     this.locations[0].location_id
        //   );
        // }
      });
  }

  getJobRequestByid() {
    if (this.activatedRoute.snapshot.params.id) {
      this.editJobFlag = this.activatedRoute.snapshot.params.id;

      const {
        request_type,
        contact_no,
        requester_name,
        order_from,
        main_category,
        sub_category,
        from_location,
        nric,
        urgent,
        return_location,
        patient_name,
        patientsCtrl,
        scan_nric,
      } = this.jobRequestForm.controls;

      if (!this.activatedRoute.snapshot.params.type) {
        request_type.disable();
        contact_no.disable();
        requester_name.disable();
        order_from.disable();
        main_category.disable();
        sub_category.disable();
        // from_location.disable();
        nric.disable();
        urgent.disable();
        return_location.disable();
        patient_name.disable();
        patientsCtrl.disable();
        scan_nric.disable();
      }

      this.jobsServive
        .getJobRequestByid(this.activatedRoute.snapshot.params.id, "All")
        .subscribe(
          (res) => {
            if (res && res.length) {
              res[0].to_location = res[0].to_location_id;
              res[0].request_type = res[0].request_type;
              res[0].sub_category = res[0].sub_category_id;
              res[0].main_category = res[0].main_category_id;
              res[0].order_from = Number(res[0].order_from);
              res[0].from_location = res[0].from_location_id;
              this.jobRequestForm.patchValue(res.length ? res[0] : {});
              this.getModesOfTransport(res[0].sub_category, 'clicked');
              this.jobRequestForm.patchValue(res.length ? res[0] : {});
              this.equipmentData = res[0].transport_mode_name;
              this.singleJob = res[0] || {};
              this.jobRequestForm
                .get("from_location")
                .setValue(res.length ? res[0].from_location : "");
              this.jobRequestForm
                .get("transport_mode")
                .setValue(res.length ? res[0].transport_mode_id : "");
              this.inputValidation.clearValidators(
                this.jobRequestForm,
                "patientsCtrl"
              );
              if (res.length > 0) {
                // this.jobRequestForm.get('patients')['controls'][0].get('radioData').setValue(res[0].equipment ? 'equipment' : res[0].sendback ? 'sendback' : '')
                this.radioData = res[0].equipment ? 'equipment' : res[0].sendback ? 'sendback' : '';
              }
              this.jobRequestForm
                .get("date")
                .patchValue(
                  res[0].due_date ? new Date(res[0].due_date) : new Date()
                );


              this.jobRequestForm
                .get("non_patient_option")
                .setValue(
                  res[0].equipment ? 'Equipment' : res[0].sendback ? 'Sendback' : ''
                );

              this.jobRequestForm
                .get("time")
                .patchValue(
                  moment(
                    res[0].due_date
                      ? res[0].due_date
                      : this.getServerTimeDate(this.serverTimeDate)
                  ).format("hh:mm a")
                );
              selectedDataAdvance = this.jobRequestForm.get("date").value;

              this.jobRequestForm.patchValue({
                remarks:
                  res[0].remarks &&
                    res[0].remarks.split(", Patient remarks:")[0]
                    ? res[0].remarks.split(", Patient remarks:")[0]
                    : "",
              });
              // if (res[0].main_category_id == 4) {
              //   this.isNonPatientJob = true;
              // } else {
              //   this.isNonPatientJob = false;
              // }
              const patint = {
                nric_no: res[0].nric,
                from_room: res[0].from_room,
                to_room: res[0].to_room,
                patient_name: res[0].patient_name,
                bed_no: res[0].from_bed,
                to_bed: res[0].to_bed,
                isolation_precaution: res[0].isolation_precaution
                  ? res[0].isolation_precaution
                  : false,
                equipment: res[0].equipment ? res[0].equipment : false,
                sendback: res[0].sendback ? res[0].sendback : false,
                remarks:
                  res[0].remarks &&
                  res[0].remarks.split(", Patient remarks:")[1],
                radioData: res[0].equipment ? 'equipment' : res[0].sendback ? 'sendback' : ''
              };
              const array = this.jobRequestForm.get("patients") as FormArray;

              // isolation precaultion method end

              if (array.length || array.length == 0) {
                array.removeAt(0);
                this.addpatient(patint);
              } else if (this.isNonPatientJob) {
                this.addpatient(patint);
                this.patientsLength = 1;
              }
              if (res[0].request_type === "advance") {
                this.patchdatenTime(res[0].due_date, res[0].due_date);
              }
              // this.jobRequestForm.get("patients").disable();
              this.jobRequestForm.get("requester_name").disable();
              this.disableBasedOnstatus(res[0].job_status);
              res[0].request_type.toLowerCase() == "advance" &&
                this.requestType("advance");
            }
          },
          (err) => {
            console.log(err);
          }
        );
    }
  }

  resetNewJobPage() {
    this.jobRequestForm.reset();
    this.getJobRequestByid();
    this.getAdminName();
    this.isNonPatientJob = false;
  }
  disabledCheckbox: boolean = false;

  disableBasedOnstatus(status) {
    if (this.activatedRoute.snapshot.params.type && this.activatedRoute.snapshot.params.type == 'recreate' &&
      (status === "Completed" || status === "Cancelled")) {
      this.isEditable = true;
    } else if (
      status === "Completed" ||
      status === "Cancelled"
    ) {
      this.isEditable = false;
      this.disabledCheckbox = true;
      this.jobRequestForm.disable();
    }
    else if (status === "Started") {
      this.isEditable = true;
      this.disabledCheckbox = true;

      this.jobRequestForm.disable();
      this.jobRequestForm.get('remarks').enable();
    }
  }

  patchdatenTime(time, date) {
    this.jobRequestForm.get("date").setValue(new Date(time.slice(0, 10)));
    const timeinAMPM = time.slice(11, 13);
    if (Number(timeinAMPM) > 12) {
      const setTime = timeinAMPM - 12 + ":" + time.slice(14, 16) + " pm";
      this.jobRequestForm.get("time").setValue(setTime);
    } else {
      this.jobRequestForm
        .get("time")
        .setValue(timeinAMPM + ":" + time.slice(14, 16) + " am");
    }
    this.jobRequestForm.get("due_time").setValue(date);
  }
  onCheckboxChange(event: MatCheckboxChange, value: string) {
    if (event.checked) {
      // Uncheck previous selection
      this.jobRequestForm.get('non_patient_option').setValue(value);
    } else if (this.jobRequestForm.get('non_patient_option').value === value) {
      // If unchecking current selection, clear the value
      this.jobRequestForm.get('non_patient_option').setValue(null);
    }
  }
  submit(actiontype) {
    const menuData = JSON.parse(this.storageService.getData("view-status-sub-menus"));
    let navigateMenu;
    const submenuList = [...menuData.map(item => item.id)]
    if (submenuList.includes(3)) {
      navigateMenu = 'jobstatus'
    } else if (submenuList.includes(54)) {
      navigateMenu = 'view-status-by-location'
    } else if (submenuList.includes(55)) {
      navigateMenu = 'view-status-by-job-category'
    }
    this.patientList.filter((data, index) => {
      data.remarks = data.remarks == '' ? this.jobRequestForm.get('patients')['controls'][index].get('remarks').value : data.remarks
      data.from_room = ''; //data.from_room == '' ? this.jobRequestForm.get('patients')['controls'][index].get('from_room').value : data.from_room
      data.to_room = ''; // data.to_room == '' ? this.jobRequestForm.get('patients')['controls'][index].get('to_room').value : data.to_room
      data.to_bed = data.to_bed == '' ? this.jobRequestForm.get('patients')['controls'][index].get('to_bed').value : data.to_bed
    })
    this.jobRequestForm.get('patients').patchValue(this.patientList)
    if (
      this.jobRequestForm.get("request_type").value.toLowerCase() === "advance"
    ) {
      this.getDateTime();
    }
    // this.jobRequestForm.get("main_category").value == 4
    const isPatientMove = this.modesOfTransportData && this.modesOfTransportData["is_patient_move"];
    const isNewRequest = !this.activatedRoute.snapshot.params.id;
    const isOtherPatientNotAdded = !this.isOtherPatientAdded;

    if (isPatientMove && isNewRequest && isOtherPatientNotAdded) {
      this.jobRequestForm.get("patientsCtrl").setValidators(Validators.required);
      this.jobRequestForm.get("patientsCtrl").updateValueAndValidity();
    } else {
      this.jobRequestForm.get("patientsCtrl").clearValidators();
      this.jobRequestForm.get("patientsCtrl").updateValueAndValidity();
    }

    if (!isPatientMove && !isNewRequest && isOtherPatientNotAdded) {
      const patientsArray = this.jobRequestForm.get("patients") as FormArray;
      patientsArray.clear();
      this.jobRequestForm.get("patients").clearValidators();
    }

    // if (
    //   (this.modesOfTransportData && this.modesOfTransportData['is_patient_move'] &&
    //     !this.activatedRoute.snapshot.params.id) && (!this.isOtherPatientAdded)
    // ) {
    //   this.jobRequestForm
    //     .get("patientsCtrl")
    //     .setValidators(Validators.required);
    //   this.jobRequestForm.get("patientsCtrl").updateValueAndValidity();
    // } else {
    //   const patientsArray = this.jobRequestForm.get("patients") as FormArray;
    //   patientsArray.clear();
    //   this.jobRequestForm.get("patients").clearValidators();

    //   this.jobRequestForm.get("patientsCtrl").clearValidators();
    //   this.jobRequestForm.get("patientsCtrl").updateValueAndValidity();
    // }
    if (this.isNRICScan && this.jobRequestForm.value.patients.length > 0) {
      this.jobRequestForm.get("patientsCtrl").clearValidators();
      this.jobRequestForm.get("patientsCtrl").updateValueAndValidity();
    }
    if (this.jobRequestForm.valid) {
      const { patients, contact_no } = this.jobRequestForm.getRawValue();
      const patient = patients.reduce(
        (final, data) => [
          ...final,
          {
            ...data,
            isolation_precaution: Object.keys(data.isolation_precaution).reduce(
              (all, ever) =>
                all + (data.isolation_precaution[ever] ? ever[0] : ""),
              ""
            ),
          },
        ],
        []
      );
      const {
        from_location,
        to_location,
        transport_mode,
        porter_id,
        patients: editPatient,
        remarks,
        due_time,
        non_patient_option
      } = this.jobRequestForm.getRawValue();
      const isEquipment = non_patient_option === 'Equipment' ? "true" : "false";
      const isSendback = non_patient_option === 'Sendback' ? "true" : "false";
      const requestData = !this.activatedRoute.snapshot.params.id || this.activatedRoute.snapshot.params.type
        ? {
          ...this.jobRequestForm.getRawValue(),
          patients: patient,
          equipment: isEquipment,
          sendback: isSendback,
        }
        : {
          to_location,
          from_location,
          transport_mode,
          porter_id,
          from_room: "",// editPatient.length ? editPatient[0].from_room : "",
          from_bed: editPatient.length ? editPatient[0].from_bed : "",
          to_room: "",//editPatient.length ? editPatient[0].to_room : "",
          to_bed: editPatient.length ? editPatient[0].to_bed : "",
          fetchback: editPatient.length ? editPatient[0].fetchback : "",

          equipment: editPatient.length ? editPatient[0].equipment : isEquipment,
          sendback: editPatient.length ? editPatient[0].sendback : isSendback,

          remarks,
          patient_remarks: editPatient.length ? editPatient[0].remarks : "",
          isolation_precaution: editPatient.length
            ? patient[0].isolation_precaution
            : "",
          due_time,
        };
      if (this.activatedRoute.snapshot.params.type) {
        actiontype = 'add'
      }
      const url = this.activatedRoute.snapshot.params.id && !this.activatedRoute.snapshot.params.type
        ? `api/jobrequest/edit/${this.activatedRoute.snapshot.params.id}`
        : "api/jobrequests/add";
      this.jobsServive.jobRequest(requestData, url, actiontype).subscribe(
        () => {
          this.storageService.setData("contact_no", contact_no);
          this.activatedRoute.snapshot.params.page == "location"
            ? this.router.navigateByUrl("/app/view-status-by-location")
            : this.activatedRoute.snapshot.params.page == "job"
              ? this.router.navigateByUrl("/app/view-status-by-job-category")
              : navigateMenu ? this.router.navigateByUrl(`/app/${navigateMenu}`) : '';

          this.toastr.success(
            `Successfully ${actiontype === "add" ? "made" : "updated"
            } job request`,
            "Success"
          );
        },
        (err) => {
          console.log(err);
        }
      );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.jobRequestForm.markAllAsTouched();
      this.makeExpansionOpen = true;
    }
  }
  onChange(event, value, preIndex, i) {
    const isolationPrecaution = {
      Airborne: false,
      Contact: false,
      Droplet: false,
      Nil: false,
      Protective: false
    }
    const isolationValueArray = this.jobRequestForm.get("patients")["controls"][
      i
    ]["controls"]["isolation_precaution"]["controls"] as FormArray;
    if (event.checked) {
      if (value.value == "Nil") {
        Object.keys(isolationValueArray).forEach((data, j) => {
          if (j == 0) {
          } else {
            isolationValueArray[data].patchValue(false);
            isolationValueArray[data].disable();
          }
        });
      } else {
        Object.keys(isolationValueArray).forEach((data, j) => {
          if (j == 0) {
            isolationValueArray[data].patchValue(false);
            isolationValueArray[data].disable();
          }
        });
      }
    } else {
      if (value.value == "Nil") {
        Object.keys(isolationValueArray).forEach((data) => {
          isolationValueArray[data].patchValue(false);
          isolationValueArray[data].enable();
        });
      }
      if (
        Object.keys(isolationValueArray).some(
          (data) => isolationValueArray[data].value == true
        )
      ) {
      } else {
        isolationValueArray[Object.keys(isolationValueArray)[0]].enable();
      }
    }
    isolationPrecaution.Airborne = isolationValueArray['Airborne'].value;
    isolationPrecaution.Contact = isolationValueArray['Contact'].value;
    isolationPrecaution.Droplet = isolationValueArray['Droplet'].value;
    isolationPrecaution.Nil = isolationValueArray['Nil'].value;
    isolationPrecaution.Protective = isolationValueArray['Protective'].value;
    this.patientList[i]['isolation_precaution'] = isolationPrecaution;
  }

  checkIsolationPrecautionValidation(form: FormGroup): any {
    const validate = Object.keys(form.getRawValue()).some(
      (data) => form.getRawValue()[data] == true
    );
    return validate ? null : { precaution: true };
  }

  getCurrentChipSelected(value, index) {
    this.activeChip = index;
    this.jobRequestForm.controls["transport_mode"].patchValue(
      value.transport_mode_id
    );
    // this.taskRequest.modeOfTransportId = assignValue
  }

  isSelectionIsFirst(event) {
    if (event) {
      this.getModesOfTransport("", "clicked");
    }
  }
  selectCategoryDefault(option: string) {
    const { request_type, sub_category } = this.jobRequestForm.controls;
    const subCategoryValue = this.subJobCategories.find(
      (sub) => sub.sj_category_id == sub_category.value
    );
    this.categoryOptionDisable = subCategoryValue
      ? subCategoryValue.display_in
      : "";
    if (
      this.categoryOptionDisable == Category.NAE ||
      this.categoryOptionDisable == Category.NA ||
      this.categoryOptionDisable == Category.NE ||
      this.categoryOptionDisable == Category.N
    ) {
      !this.singleJob && request_type.patchValue("Normal");
    }
    if (
      this.categoryOptionDisable == Category.AE ||
      this.categoryOptionDisable == Category.E
    ) {
      !this.singleJob && request_type.patchValue("Emergency");
    } else if (this.categoryOptionDisable == Category.A) {
      !this.singleJob && request_type.patchValue("Advance");
    }
  }

  changeNricToMaskedValue(value: string): string {
    const lastFourVal = value.slice(value.length - 4, value.length);
    const startChar = value.slice(0, value.length - 4);
    return value.length > 3
      ? startChar
        .split("")
        .reduce((arr, ever) => ["X", ...arr], [])
        .join("") + lastFourVal
      : value;
  }
  // onRadioSelection(event: MatRadioChange, index) {
  //   this.jobRequestForm.value.patients.filter((data, i) => {
  //     if (i == index) {
  //       if (event.value == 'equipment') {
  //         data[event.value] = 1
  //         data.sendback = false
  //       } else if (event.value == 'sendback') {
  //         data[event.value] = true
  //         data.equipment = 0
  //       }
  //     }
  //   })
  //   this.patientList = this.jobRequestForm.value.patients
  // }

  goBack() {
    this.router.navigate(['../../', { 'status': this.singleJob.job_status }], { relativeTo: this.activatedRoute })
  }
}
