import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { promise } from 'protractor';
import { BehaviorSubject } from 'rxjs';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { StorageService } from 'src/app/Apiservices/stoargeService/storage.service';
import { resolve } from 'url';
const FULL_DASH_ARRAY: number = 283;
const RESET_DASH_ARRAY: string = `-57 ${FULL_DASH_ARRAY}`;
@Component({
  selector: 'app-base-timer',
  templateUrl: './base-timer.component.html',
  styleUrls: ['./base-timer.component.scss']
})

export class BaseTimerComponent implements OnInit ,AfterViewInit{
  showTimer : boolean;
  @Output() CallThirtySecFun : EventEmitter<Boolean> = new EventEmitter(); 
  stopTimerForDataLoad$: BehaviorSubject<Boolean> = new BehaviorSubject<Boolean>(false);
  @ViewChild('baseTimerPathRemaining', { static: false, read: ElementRef }) timer: ElementRef;
  @ViewChild('baseTimerLabel', { static: false, read: ElementRef }) timeLabel: ElementRef;
  
  stopTimerForDataLoad: boolean;
  currentTime = this.getCurrentTime();
  TIME_LIMIT: number = 30;
  timePassed: number = -1;
  timeLeft: number = this.TIME_LIMIT;
  timerInterval: any;
  isLoading:boolean ;

  constructor(private readonly loaderService: LoaderService,
    private readonly storageService: StorageService) {
    this.loaderService.isLoading.subscribe(res => {
      this.isLoading = res;
    });
    this.stopTimerForDataLoad$.subscribe((res: boolean) => {
      this.stopTimerForDataLoad = res;
    })
  }

  ngOnInit() {
    this.checkGlobalTimerToggle()
  }

  checkGlobalTimerToggle() {
    let localValue = this.storageService.getData("global_timer_toggle")
    let bool = atob(localValue ? localValue : "")
    this.showTimer = bool === "true" ? true
    : bool === "false" ? false : false
  }

  ngAfterViewInit(): void {
    if (this.showTimer) {
      this.show();
      this.start();
    }
  }
  
  reset(): void {
    clearInterval(this.timerInterval);
    if(this.timer){
      this.resetVars();
      this.timer.nativeElement.setAttribute("stroke-dasharray", RESET_DASH_ARRAY);
    }
  }

  start(withReset: boolean = false): void {
    if (this.showTimer) {
      if (withReset) {
        this.resetVars();
      }
      this.startTimer();
    }
  }

  stopTimerForDataLoadFun(value: boolean) {
    this.stopTimerForDataLoad$.next(value)
  }

  startTimer(): void {
    this.timerInterval = setInterval(() => {
      if (this.isLoading === false && this.stopTimerForDataLoad === false) {
        this.timePassed = this.timePassed += 1;
        this.timeLeft = this.TIME_LIMIT - this.timePassed;
        this.timeLabel!.nativeElement.innerHTML = this.formatTime(this.timeLeft);
        this.setCircleDasharray(); 
        if (this.timeLeft === 0) { 
          this.reset();
          this.currentTime = this.getCurrentTime();
          this.CallThirtySecFun.emit();
          this.startTimer();
        }
      }
    }, 1000);
  }


  resetVars(): void {
    this.timePassed = -1;
    this.timeLeft = this.TIME_LIMIT;
    this.timeLabel!.nativeElement.innerHTML = this.formatTime(this.TIME_LIMIT);
  }
  formatTime(time: number): string {
    const minutes: number = Math.floor(time / 60);
    let seconds: number | string = time % 60;

    if (seconds < 10) {
      seconds = `0${seconds}`;
    }

    return `${minutes}:${seconds}`;
  }

  calculateTimeFraction(): number {
    const rawTimeFraction: number = this.timeLeft / this.TIME_LIMIT;
    return rawTimeFraction - (1 / this.TIME_LIMIT) * (1 - rawTimeFraction);
  }
  
  setCircleDasharray(): void {
    const circleDasharray: string = `${(this.calculateTimeFraction() * FULL_DASH_ARRAY).toFixed(0)} 283`;
    this.timer!.nativeElement.setAttribute("stroke-dasharray", circleDasharray);
  }

  async toggleChanged(value: boolean, event: Event) {
    if (value) {
      this.storageService.setData("global_timer_toggle", btoa("false"));
      this.stop();
      this.reset();
      this.showTimer = false;
    } else {
      this.storageService.setData("global_timer_toggle", btoa("true"));
      this.showTimer = true;
      await this.show();
      this.start();
    }
    event.preventDefault();
    event.stopPropagation();
  }

  getCurrentTime(): string {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const amOrPm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    return `${formattedHours} : ${formattedMinutes} ${amOrPm}`;
  }
  stop() {
    clearInterval(this.timerInterval);
  }

  ngOnDestroy(): void {
    this.stop();
  }

  show(): Promise<boolean> {
    return new Promise(resolve => {
      if (this.timeLabel) {
        this.timeLabel.nativeElement.innerHTML = this.formatTime(this.TIME_LIMIT);
        resolve(true);
      }
      else {
        setTimeout(() => {
          this.show();
        }, 1000)
        resolve(true);
      }

    })
  }
}
