import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { Router, ActivatedRoute } from '@angular/router';
import { ExportExcelService } from 'src/app/Apiservices/export-excel/export-excel.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Component({
  selector: 'app-list-cosy-departments',
  templateUrl: './list-cosy-departments.component.html',
  styleUrls: ['./list-cosy-departments.component.scss']
})
export class ListCosyDepartmentsComponent implements OnInit {

  displayedColumns: string[] = ['Department', 'Department Code', 'Status', 'action'];
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  coSyDepartmentsList: any;
  
  constructor(
    private readonly userService: UserService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private exportExcelService: ExportExcelService
  ) { }

  ngOnInit() {
    this.getCoSyDepartmentList();
  }

  getCoSyDepartmentList() {
    this.userService.getCoSyDepartments().subscribe(res => {
      if (res) {
        this.coSyDepartmentsList = res;
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }
    })
  }

  updateCoSyDepartment(data) {
    this.router.navigate([`./addupdateCoSyDepartments`, { 'id': data.costcenter_id}], { relativeTo: this.activatedRoute })
  }

  exportExcel(){
    const data = this.getFormattedExcelData();
    this.exportExcelService.generateExcel(data, "CoSy Cost Centers");
  }

  getFormattedExcelData(){
    let data: any = {};
    data.header = ["CostCenter", "CostCenterCode", "Location", "IsExternal"];
    data.values = [];
    this.coSyDepartmentsList.forEach(item=>{
      item.station_locations.forEach(loc=>{
        data.values.push([
          item.department_name,
          item.department_code,
          loc.location_name,
          item.IsExternal ? "TRUE" : "FALSE"
        ])
      })
    })

    return data;
  }

}
