import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DatepPickerComponent } from "./datep-picker/datep-picker.component";
import {
  MatNativeDateModule,
  MatInputModule,
  MatSelectModule,
  MatFormFieldModule,
  MatDatepickerModule,
  MatTableModule,
  MatSortModule,
  MatMenuModule,
  MatCheckboxModule,
  MatTooltipModule,
  MatButtonModule,
  MatChipsModule,
  MatSidenavModule,
  MatListModule,
  MatSlideToggleModule,
} from "@angular/material";
import { MatBadgeModule } from "@angular/material";

import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CdkTableModule } from "@angular/cdk/table";
import { MenuLayoutComponent } from "./menu-layout/menu-layout.component";
import { RouterModule } from "@angular/router";
import { TranslatePipePipe } from "../pipes/i18n/translate-pipe.pipe";
import { TimeRangePickerComponent } from "./time-range-picker/time-range-picker.component";
import { NgxMaterialTimepickerModule } from "ngx-material-timepicker";
import { FilePickerComponent } from "./file-picker/file-picker.component";
import { TableForPdfComponent } from "./table-for-pdf/table-for-pdf.component";
import { DynamicTableComponent } from "./dynamic-table/dynamic-table.component";
import { SummaryMenuListComponent } from "./summary-menu-list/summary-menu-list.component";
import { MultitablepdfComponent } from "./multitablepdf/multitablepdf.component";
import { MultiheaderpdfComponent } from "./multiheaderpdf/multiheaderpdf.component";
import { CpjobtypepdfComponent } from "./cpjobtypepdf/cpjobtypepdf.component";
import { FooterComponent } from "./footer/footer.component";
import { MatExpansionModule } from "@angular/material/expansion";
import { BaseTimerComponent } from "./base-timer/base-timer.component";

@NgModule({
  declarations: [
    DatepPickerComponent,
    MenuLayoutComponent,
    TranslatePipePipe,
    TimeRangePickerComponent,
    FilePickerComponent,
    TableForPdfComponent,
    SummaryMenuListComponent,
    DynamicTableComponent,
    MultitablepdfComponent,
    MultiheaderpdfComponent,
    CpjobtypepdfComponent,
    FooterComponent,
    BaseTimerComponent
  ],
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
    MatTableModule,
    FormsModule,
    ReactiveFormsModule,
    MatSortModule,
    NgxMaterialTimepickerModule,
    CdkTableModule,
    RouterModule,
    MatMenuModule,
    MatCheckboxModule,
    MatSelectModule,
    MatTooltipModule,
    MatButtonModule,
    MatBadgeModule,
    MatExpansionModule,
    MatChipsModule,
    MatSidenavModule,
    MatListModule,
    MatSlideToggleModule,
  ],
  exports: [
    MatDatepickerModule,
    FooterComponent,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
    MatTableModule,
    DatepPickerComponent,
    NgxMaterialTimepickerModule,
    MenuLayoutComponent,
    TranslatePipePipe,
    TimeRangePickerComponent,
    MatMenuModule,
    MatSortModule,
    MatCheckboxModule,
    FilePickerComponent,
    MatSelectModule,
    TableForPdfComponent,
    DynamicTableComponent,
    SummaryMenuListComponent,
    MultitablepdfComponent,
    MultiheaderpdfComponent,
    CpjobtypepdfComponent,
    MatTooltipModule,
    MatButtonModule,
    MatBadgeModule,
    MatExpansionModule,
    MatChipsModule,
    MatSidenavModule,
    MatListModule,
    MatSlideToggleModule,
    BaseTimerComponent
  ],
})
export class SharedThemeModule {}
