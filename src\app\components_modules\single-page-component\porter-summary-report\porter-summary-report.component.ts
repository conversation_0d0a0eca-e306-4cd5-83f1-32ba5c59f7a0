import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { FormGroup, FormBuilder, Validators} from '@angular/forms';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { PorterReport } from 'src/app/models/porterReport';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ToastrService } from 'ngx-toastr';
import { ReportsService } from '../../../Apiservices/reports/reports.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import * as moment from 'moment';


@Component({
  selector: 'app-porter-summary-report',
  templateUrl: './porter-summary-report.component.html',
  styleUrls: ['./porter-summary-report.component.scss', '../../../scss/table.scss']
})
export class PorterSummaryReportComponent implements OnInit {
  routes = [
    { path: './../enhancedReports', label: 'Search (ACK)' },
    { path: './../busrouteSearch', label: 'Bus Route Reports' },
    { path: './../porterReports', label: 'Porter Workload' },
    { path: './../pts-reports', label: 'PTS Reports' },
    { path: './../enhanced-location-reports', label: 'Location Reports' }
  ];
  displayedColumns: string[] = ['staff_name', 'central_pool_jobs', 'cp_avg_assign_resp', 'cp_avg_assign_comp', 'station_jobs', 'bus_route_jobs'];
  dataSource: MatTableDataSource<PorterReport>;
  locationType = [];
  pdfData = [];
  showAck = false;
  today = new Date().toUTCString();
 prevMonth = moment(this.today).subtract(1, 'months');
 // tslint:disable-next-line: no-string-literal
 month = this.prevMonth['_d'];
  porterReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: 'col-16',
    label: 'From Date',
    required: false,
    minDateStart: '2000-12-12',
    maxDateStart: '',
    minDateEnd: '',
    maxDateEnd: '',
  };
  ToDate = {
    rangePicker: false,
    collumns: 'col-16',
    label: 'To Date',
    required: false,
    minDateStart: '2000-12-12',
    maxDateStart: '',
    minDateEnd: '',
    maxDateEnd: '',
  };
  exportResult = [];
  constructor(
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    public inputValidation: InputValidationService,
    private readonly loader: LoaderService,
    private readonly report: ReportsService,
    private readonly userService: UserService,
  ) {
   this.createForm();
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }

  createForm() {
    this.porterReportForm = this.fb.group({
      from_date: [this.month, Validators.required],
      to_date: [this.today, Validators.required],
    });
  }


  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe(res => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, 'months');
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth['_d'];
      this.createForm();
      this.searchPorter(this.month, this.today);
    });
  }


  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('porterReport', 'porterReport_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.searchPorter(this.porterReportForm.get('from_date').value, this.porterReportForm.get('to_date').value, 'export');
      // TableUtil.exportToExcel('porterReport', 'porterReport_list');
    }
  }
  mapTableDataforReport(exportResult) {
    let mappedData;
    let exportObj: any[]=[];
    for(var i=0;i<exportResult.length;i++)
    {
      mappedData = {
        // 'Start Location': exportResult[i].start_location ? exportResult[i].start_location.toString().replace("\r\n","") : "--",
        'Staff Name': exportResult[i].staff_name,
        'No. Of Central Pool Jobs': exportResult[i].central_pool_jobs,
        'AVG Assign-Resp(Mints)': exportResult[i].cp_avg_assign_resp,
        'AVG Assign-Comp(Mints)': exportResult[i].cp_avg_assign_comp,
        'Total Station Jobs': exportResult[i].station_jobs,
        'Total Bus Route Jobs': exportResult[i].bus_route_jobs
      };
      exportObj.push(mappedData);
    }
    TableUtil.exportArrayToExcelDynamically(exportObj, "porterReport_list");
  }
  searchPorter(from, to, type?) {
    if (this.porterReportForm.valid) {
      const data = {
        from_date: moment(from).format('YYYY-MM-DD'),
        to_date: moment(to).format('YYYY-MM-DD')
      };
      this.report.getPorterReport(data).subscribe(res => {
        if (type){
          this.exportResult = res;
          this.mapTableDataforReport(this.exportResult);
        } else {
          this.dataSource = new MatTableDataSource(res ? res : []);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          this.setPageSizeOptions();
        }
      }, err => {
        this.toastr.warning('Search Failed', 'failed!');
      });
    }

  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Staff Name': key.staff_name,
          'No. Of Central Pool Jobs': key.central_pool_jobs,
          'AVG Assign-Resp(Mints)': key.cp_avg_assign_resp,
          'AVG Assign-Comp(Mints)': key.cp_avg_assign_comp,
          'Total Station Jobs': key.station_jobs,
          'Total Bus Route Jobs': key.bus_route_jobs
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }
  getDate() {
    if (this.porterReportForm.get('from_date').value && this.porterReportForm.get('to_date').value) {
      if (this.porterReportForm.get('from_date').value >= this.porterReportForm.get('to_date').value) {
        this.porterReportForm.get('to_date').setValue('');
        this.toastr.error('To date should be less then From date', 'Error');
      }
    }
  }
}
