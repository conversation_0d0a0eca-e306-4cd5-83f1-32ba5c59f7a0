import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListBusRoutesComponent } from './list-bus-routes/list-bus-routes.component';
import { AddUpdateBusRoutesComponent } from './add-update-bus-routes/add-update-bus-routes.component';


const routes: Routes = [
  { path: '', component: ListBusRoutesComponent },
  { path: 'addbusroute', component: AddUpdateBusRoutesComponent },
  { path: 'updatebusroute/:id', component: AddUpdateBusRoutesComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BusRoutesRoutingModule { }
