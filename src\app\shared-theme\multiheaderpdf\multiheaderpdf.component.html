<div class="container-table" id="pdf-download">
    <br>
    <div class="row">
        <div class="col-4">
            <img src="./assets/img/UEMS_Solutions_logo.jpg" alt="" style="height: 40px" />
        </div>
        <div class="col-4" style="text-align: center;">
            <h3> <strong>{{title}}</strong></h3>
        </div>
        <div class="col-4" *ngIf="logoUrl">
            <img [src]="logoUrl" alt="logo" style="max-height: 50px; float: right; margin-right: 20px;" />
        </div>
    </div>

    <table *ngIf="eq" class="responstable" class="responstable" style="table-layout: fixed;">
        <caption>
        </caption>
        <col>
        <colgroup span="2"></colgroup>
        <colgroup span="2"></colgroup>
        <tr>
            <td [attr.rowspan]="tis">Equipment</td>
            <th colspan="3" scope="colgroup">Normal</th>
            <th colspan="3" scope="colgroup">Emergency</th>
        </tr>
        <tr>
            <th scope="col">Patient Move</th>
            <th scope="col">Non Patinet Move</th>
            <th scope="col">Total</th>
            <th scope="col">Patient Move</th>
            <th scope="col">Non Patinet Move</th>
            <th scope="col">Total</th>
        </tr>
        <tr *ngFor="let data of pdfData">
            <th scope="row">{{data.Equipment}}</th>
            <td id="">{{data.NPM}}</td>
            <td id="">{{data.NNPM}}</td>
            <td id="">{{data.NormalTotal}}</td>
            <td id="">{{data.EPM}}</td>
            <td id="">{{data.ENPM}}</td>
            <td id="">{{data.EmergencyTotal}}</td>
        </tr>
    </table>

    <table *ngIf="kpi" class="responstable" class="responstable" style="table-layout: fixed;">
        <caption>
        </caption>
        <col>
        <colgroup span="2"></colgroup>
        <colgroup span="2"></colgroup>
        <tr>
            <td [attr.rowspan]="tis">Equipment</td>
            <th colspan="3" scope="colgroup">YTD AVG-20</th>
            <th colspan="3" scope="colgroup">Jan-20</th>
            <th colspan="3" scope="colgroup">Feb-20</th>
            <th colspan="3" scope="colgroup">Mar-20</th>
            <th colspan="3" scope="colgroup">Apr-20</th>
            <th colspan="3" scope="colgroup">May-20</th>
            <th colspan="3" scope="colgroup">Jun-20</th>
            <th colspan="3" scope="colgroup">Jul-20</th>
            <th colspan="3" scope="colgroup">Aug-20</th>
            <th colspan="3" scope="colgroup">Sep-20</th>
            <th colspan="3" scope="colgroup">Oct-20</th>
            <th colspan="3" scope="colgroup">Nov-20</th>
            <th colspan="3" scope="colgroup">Dec-20</th>
        </tr>
        <tr>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
            <th scope="col">PM</th>
            <th scope="col">NPM</th>
            <th scope="col">Total</th>
        </tr>
        <tr *ngFor="let data of pdfData">
            <th scope="row">total</th>
            <td id="">{{data.Stations}}</td>
            <td id="">{{data.YNPM}}</td>
            <td id="">{{data.YNNPM}}</td>
            <td id="">{{data.YTotal}}</td>
            <td id="">{{data.JNPM}}</td>
            <td id="">{{data.JNNPM}}</td>
            <td id="">{{data.JTotal}}</td>
            <td id="">{{data.FNPM}}</td>
            <td id="">{{data.FNNPM}}</td>
            <td id="">{{data.FTotal}}</td>
            <td id="">{{data.MNPM}}</td>
            <td id="">{{data.MNNPM}}</td>
            <td id="">{{data.MTotal}}</td>
            <td id="">{{data.ANPM}}</td>
            <td id="">{{data.ANNPM}}</td>
            <td id="">{{data.ATotal}}</td>
            <td id="">{{data.MAYNPM}}</td>
            <td id="">{{data.MAYNNPM}}</td>
            <td id="">{{data.MAYTotal}}</td>
            <td id="">{{data.JUNPM}}</td>
            <td id="">{{data.JUNNPM}}</td>
            <td id="">{{data.JUTotal}}</td>
            <td id="">{{data.JULNPM}}</td>
            <td id="">{{data.JULNNPM}}</td>
            <td id="">{{data.JULTotal}}</td>
            <td id="">{{data.AUGNPM}}</td>
            <td id="">{{data.AUGNNPM}}</td>
            <td id="">{{data.AUGTotal}}</td>
            <td id="">{{data.SENPM}}</td>
            <td id="">{{data.SENNPM}}</td>
            <td id="">{{data.SETotal}}</td>
            <td id="">{{data.ONPM}}</td>
            <td id="">{{data.ONNPM}}</td>
            <td id="">{{data.OTotal}}</td>
            <td id="">{{data.NNPM}}</td>
            <td id="">{{data.NNNPM}}</td>
            <td id="">{{data.NTotal}}</td>
            <td id="">{{data.DNPM}}</td>
            <td id="">{{data.DNNPM}}</td>
            <td id="">{{data.DTotal}}</td>
        </tr>
    </table>