<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav" style="margin-left: 1%; line-height: 35px">
                  <p>View Feedback</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="feedbckForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Feedback Report
                      </legend>
                      <div class="row">
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="feedbckForm.controls.from_date"
                            [fieldName]="FromDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="feedbckForm.controls.to_date"
                            [fieldName]="ToDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button
                            mat-raised-button
                            (click)="
                              searchFeedBack(month, today);
                              feedbckForm.get('from_date').setValue(month);
                              feedbckForm.get('to_date').setValue(today);
                              filter.value = ''
                            "
                            class="btn btn-white pull-right"
                          >
                            Reset
                          </button>
                          <button
                            mat-raised-button
                            type="submit"
                            (click)="
                              searchFeedBack(
                                feedbckForm.get('from_date').value,
                                feedbckForm.get('to_date').value
                              )
                            "
                            class="btn btn-primary pull-right"
                          >
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>

                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="feedBackTable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <ng-container matColumnDef="requestor_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Requestor Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.requestor_name }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="contact_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Contact Number
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.contact_no }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Location
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.location }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Time
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.time }}</td>
                      </ng-container>

                      <ng-container matColumnDef="feedback">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          FeedBack
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.feedback }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="FeedBack_id">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          View
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <em
                            class="material-icons"
                            style="cursor: pointer"
                            data-target="#viewFeedBack"
                            data-toggle="modal"
                            (click)="ViewFeedBack(row)"
                            >visibility</em
                          >
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf
  [heads]="['Requestor Name', 'Route Name', 'Location', 'Time', 'FeedBack']"
  [title]="'FeedBack'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<!-- View Modal -->
<div class="modal fade" id="viewFeedBack" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <p class="modal-title">FeedBack</p>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <p>Requestor Name :</p>
          </div>
          <div class="col-md-6">
            <p>
              {{
                viewData && viewData.requestor_name
                  ? viewData?.requestor_name
                  : ""
              }}
            </p>
          </div>

          <div class="col-md-6">
            <p>Contact No :</p>
          </div>
          <div class="col-md-6">
            <p>
              {{ viewData && viewData.contact_no ? viewData.contact_no : "" }}
            </p>
          </div>

          <div class="col-md-6">
            <p>Location :</p>
          </div>
          <div class="col-md-6">
            <p>{{ viewData && viewData.location ? viewData.location : "" }}</p>
          </div>

          <div class="col-md-6">
            <p>Feedback :</p>
          </div>
          <div class="col-md-6">
            <p>{{ viewData && viewData.feedback ? viewData.feedback : "" }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
