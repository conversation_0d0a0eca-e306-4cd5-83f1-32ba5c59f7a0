<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export
                                </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xlsx')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555"
                                        (click)="exportTable('csv')" class="nav-link">
                                        <p style="display: inline-block">csv</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="nav" style="margin-left: 1%; line-height: 35px">
                                    <p>View PTS Jobs</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <form [formGroup]="ptsReportForm">
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">
                                                Filter PTS Reports
                                            </legend>
                                            <div class="row">
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="FromDate"
                                                        [control]="ptsReportForm.controls.fromDate"
                                                        [fieldName]="FromDate?.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="ToDate"
                                                        [control]="ptsReportForm.controls.toDate"
                                                        [fieldName]="ToDate?.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>

                                                <div class="col-4">
                                                    <mat-form-field class="alignSelectBox">
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('location');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Location<span class="error-css"></span>
                                                        </mat-label>
                                                        <mat-select disableOptionCentering formControlName="location">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="locationCtrl"
                                                                    [placeholderLabel]="'Find Location...'"
                                                                    [noEntriesFoundLabel]="'no matching location found'">
                                                                </ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option
                                                                *ngFor="let location of filteredLocation | async"
                                                                [value]="location.location_id">{{ location.location_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                    </mat-form-field>
                                                </div>

                                                <div class="col-10"></div>
                                                <div class="col-2">
                                                    <button mat-raised-button type="submit"
                                                        class="btn btn-primary pull-right" (click)="searchByData()">
                                                        Search
                                                    </button>
                                                    <button mat-raised-button (click)="restForm()"
                                                        class="btn btn-white pull-right">
                                                        Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto;"
                                        [ngStyle]="{'height': (dataSource && dataSource.filteredData.length === 0) ? '150px' : '600px'}">
                                        <table id="enhancedReport" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="ptsId">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    PTS ID
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.ptsId ? row?.ptsId : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cannisterId">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cannister ID
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cannisterId ? row?.cannisterId : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="fromLocation">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    From Location
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.fromLocation ? row?.fromLocation : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="toLocation">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    To Location
                                                </th>

                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.toLocation && row?.toLocation !== '' ? (row.toLocation) :
                                                    '--'}}
                                                </td>

                                            </ng-container>

                                            <ng-container matColumnDef="patientName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.patientName ? (row?.patientName) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patientNRIC">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient NRIC
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.patientNRIC ? (row?.patientNRIC) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="wardNo">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Ward
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.wardNo ? (row?.wardNo) : "--"  }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="roomNo">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Room
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{  row?.roomNo ? (row?.roomNo) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="bedNo">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.bedNo ? row?.bedNo : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.status ? row?.status : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="reason">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Reason
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.reason ? row?.reason : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="remarks">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Remarks
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.remarks ? row?.remarks : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="createdBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Created By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.createdBy ? row?.createdBy : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="createdDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Created Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.createdDate ? row?.createdDate : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="acceptedBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Accepted By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.acceptedBy ? row?.acceptedBy : "--" }}
                                                </td>
                                            </ng-container>
                                            <ng-container matColumnDef="acceptedDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Accepted Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.acceptedDate ? row?.acceptedDate : "--" }}
                                                </td>
                                            </ng-container>
                                            <ng-container matColumnDef="rejectedBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Rejected By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.rejectedBy ? row?.rejectedBy : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="rejectedDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Rejected Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.rejectedDate ? row?.rejectedDate : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancelledBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancelledBy ? row?.cancelledBy : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancelledDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancelledDate ? row?.cancelledDate : "--" }}
                                                </td>
                                            </ng-container>

                                            <!-- <ng-container matColumnDef="medication_code">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Medication Code
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.medication_code ? row?.medication_code : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="medication_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Medication Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.medication_name ? row?.medication_name : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="qty">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Qty
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.qty ? row?.qty : "--" }}
                                                </td>
                                            </ng-container> -->

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                                        </table>
                                        <div style="text-align: center;" *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons [pageSize]=50
                    (page)="pageChanged($event)"></mat-paginator>
            </div>
        </div>
    </div>
</div>