<div class="main-content">
  <div class="container-fluid">
    <!-- <app-summary-menu-list [routes]="routes"></app-summary-menu-list> -->
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('csv')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">csv</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav" style="margin-left: 1%; line-height: 35px">
                  <p>View Bus Route</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <!-- <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div> -->

              <div class="col-md-12 filter-margin">
                <form [formGroup]="busRouteForm">
                  <fieldset class="scheduler-border">
                    <legend class="scheduler-border">Filter Bus Route</legend>
                    <div class="row">
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label>Type</mat-label>
                          <mat-select formControlName="route_type">
                            <mat-option
                              *ngFor="let route of routeType"
                              [value]="route.value"
                            >
                              {{ route.displayName }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label>Route Name</mat-label>
                          <input
                            matInput
                            placeholder="Route Name"
                            formControlName="route_name"
                          />
                        </mat-form-field>
                      </div>
                      <div class="col-3">
                        <app-datep-picker
                          [dateConfiguration]="FromDate"
                          [control]="busRouteForm.controls.from_date"
                          [fieldName]="FromDate.label"
                          [fieldType]="'select'"
                          (getDate)="getDate()"
                        >
                        </app-datep-picker>
                      </div>
                      <div class="col-3">
                        <app-datep-picker
                          [dateConfiguration]="ToDate"
                          [control]="busRouteForm.controls.to_date"
                          [fieldName]="FromDate.label"
                          [fieldType]="'select'"
                          (getDate)="getDate()"
                        >
                        </app-datep-picker>
                      </div>
                      <div class="col-3">
                        <app-time-range-picker
                          [rangePicker]="true"
                          [label]="'Start Time'"
                          [control]="busRouteForm.controls.start_time"
                          [fieldName]="'Start Time'"
                          [fieldType]="'select'"
                          [required]="false"
                        >
                        </app-time-range-picker>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label>Start Location</mat-label>
                          <mat-select formControlName="start_location">
                            <mat-option
                              *ngFor="let location of locationType"
                              [value]="location.location_id"
                            >
                              {{ location.location_name }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label>End Location</mat-label>
                          <mat-select formControlName="end_location">
                            <mat-option
                              *ngFor="let location of locationType"
                              [value]="location.location_id"
                            >
                              {{ location.location_name }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-3"></div>

                      <div class="col-8"></div>
                      <div class="col-4">
                        <button
                          mat-raised-button
                          (click)="restForm()"
                          class="btn btn-white pull-right"
                        >
                        <!-- filter.value = '' -->
                          Reset
                        </button>
                        <button
                          mat-raised-button
                          (click)="searchBusRoute()"
                          class="btn btn-primary pull-right"
                        >
                          Search
                        </button>
                      </div>
                    </div>
                  </fieldset>
                </form>
              </div>
              <div class="col-md-12">
                <div class="mat-elevation-z8" style="overflow-x: auto">
                  <table
                    id="enhancedBusRoute"
                    mat-table
                    [dataSource]="dataSource"
                    matSort
                  >
                    <caption></caption>

                    <ng-container matColumnDef="route_no">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Route No
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.route_no ? row?.route_no : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="route_name">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Route Name
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.route_name ? row?.route_name : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="route_type">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Route Type
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.route_type ? row?.route_type : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="created_date">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Creation Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.created_date ? (row?.created_date | localDateConversion: "full") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="start_location">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Start Location
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.start_location ? row?.start_location : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="end_location">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        End Location
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.end_location ? row?.end_location : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="location_count">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        No of Locations
                      </th>
                      <td mat-cell *matCellDef="let row">
                        <a
                          href="{{ row?.location_count }}"
                          data-toggle="modal"
                          data-target="#exampleModal"
                          (click)="locationNames = row.location_list"
                          >{{
                            row?.location_count ? row?.location_count : "-"
                          }}</a
                        >
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="start_time">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Scheduled Start Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.start_time ? (row?.start_time | localDateConversion: "time") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="assign_time">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Assigned Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.assign_time ? (row?.assign_time | localDateConversion: "full") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="actual_start_time">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Start Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.actual_start_time ? (row?.actual_start_time | localDateConversion: "full") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="end_time">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Completion Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.end_time ? (row?.end_time | localDateConversion: "full") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="cancel_time">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Cancel Time
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.cancel_time ? (row?.cancel_time | localDateConversion: "full") : "--" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="resource1">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Resource 1
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.resource1 ? row?.resource1 : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="resource2">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Resource 2
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.resource2 ? row?.resource2 : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="remarks">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Remarks
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.remarks ? row?.remarks : "-" }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="requestor">
                      <th
                        id=""
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                      >
                        Requestor
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row?.requestor ? row?.requestor : "-" }}
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr
                      mat-row
                      *matRowDef="let row; columns: displayedColumns"
                    ></tr>
                  </table>
                  <div
                    *ngIf="dataSource && dataSource.filteredData.length === 0"
                  >
                    No records to display.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          showFirstLastButtons
          [pageSize]=50
          (page)="pageChanged($event)"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf
  [heads]="[
    'Route No',
    'Route Name',
    'Route Type',
    'Creation Time',
    'Start Location',
    'End Location',
    'No of Locations',
    'Scheduled Start Time',
    'Assigned Time',
    'Start Time',
    'Completion Time',
    'Cancel Time',
    'Resource 1',
    'Resource 2',
    'Remarks',
    'Requestor'
  ]"
  [title]="'Enhanced Bus Route'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<!-- Location Modal -->
<div class="modal fade" id="exampleModal" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <p class="modal-title">Locations</p>
      </div>
      <div class="modal-body">
        <ul>
          <ul>
            <li *ngFor="let location of locationNames">{{ location }}</li>
          </ul>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
