import { Router, ActivatedRoute } from '@angular/router';
import { UserService } from './../../../Apiservices/userService/user.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-list-manage-departmets',
  templateUrl: './list-manage-departmets.component.html',
  styleUrls: ['./list-manage-departmets.component.scss']
})
export class ListManageDepartmetsComponent implements OnInit {
  displayedColumns: string[] = ['Department', 'action', 'edit'];  // ['Department', 'Status', 'action'];
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  constructor(
    private readonly userService: UserService,
    private readonly toastr: ToastrService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getDepartments();
  }

  getDepartments() {
    this.userService.getDepartments().subscribe(res => {
      if (res) {
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }
    });
  }

  deleteDepartment(data) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.userService.deleteDepartment(data.department_id).subscribe(res => {
          if (res) {
            this.toastr.success('Successfully deleted Department', 'Success');
            this.getDepartments();
          } else {
            this.toastr.error('Error occurred in deleting Department', 'Error');
          }
        }, err => {
          this.toastr.error('Error occurred in deleting Department', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

  editDepartment(data) {
    this.router.navigate([`/app/manage-departments/addmanagedepartments`],
    { queryParams: {'department_id': data.department_id, 'department_name': data.department_name, 'status': data.status} })
  }

}
