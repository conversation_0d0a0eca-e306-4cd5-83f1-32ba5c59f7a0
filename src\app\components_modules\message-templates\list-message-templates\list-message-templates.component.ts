import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import { ToastrService } from 'ngx-toastr';
import Swal from 'sweetalert2';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
export interface UserData {
  message_template: string;
  message_template_id: string;
}

@Component({
  selector: 'app-list-message-templates',
  templateUrl: './list-message-templates.component.html',
  styleUrls: ['./list-message-templates.component.scss', '../../../scss/table.scss']
})
export class ListMessageTemplatesComponent implements OnInit {
  displayedColumns: string[] = ['message_template', 'edit', 'delete'];
  dataSource: MatTableDataSource<UserData>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];

  constructor(
    private readonly settingsSeervice: SettingsService,
    private readonly toaster: ToastrService,
    private readonly loader: LoaderService
  ) {
  }

  ngOnInit() {
    this.getMessageTemplates();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('messagetable', 'messagetable_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('messagetable', 'messagetable_list');
    }
  }

  getMessageTemplates() {
    this.settingsSeervice.getMessageTemplates().subscribe(res => {
      this.pdfData = res.slice();
      this.pdfData = this.pdfData.filter(key => {
        const a = {
          Message: key.message_template,
        };
        Object.assign(key, a);
        return key;
      });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    });
  }


  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deleteMessageTemplate(id) {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete!',
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No, keep it',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.value) {
        this.settingsSeervice.delteMessageTemplate(id).subscribe(res => {
          if (res) {
            this.toaster.success('Successfully deleted message template', 'Success');
            this.getMessageTemplates();
          } else {
            this.toaster.error('Error occured in deleting message template', 'Error');
          }
        }, err => {
          this.toaster.error('Error occured in deleting message template', 'Error');
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }
}

