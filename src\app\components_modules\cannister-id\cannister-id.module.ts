import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CannisterIdRoutingModule } from './cannister-id-routing.module';
import { CannisterIdComponent } from './list-cannister-ids/cannister-id.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddCannisterIdComponent } from './add-cannister-ids/add-cannister-id.component';


@NgModule({
  declarations: [CannisterIdComponent, AddCannisterIdComponent],
  imports: [
    CommonModule,
    CannisterIdRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule
  ]
})
export class CannisterIdModule { }
