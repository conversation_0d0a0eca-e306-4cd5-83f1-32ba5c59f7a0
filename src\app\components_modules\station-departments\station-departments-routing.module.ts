import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AddUpdateStationDepartmentsComponent } from './add-update-station-departments/add-update-station-departments.component';
import { ListStationDepartmentsComponent } from './list-station-departments/list-station-departments.component';


const routes: Routes = [
  {path: '', component: ListStationDepartmentsComponent},
  {path: 'addupdateStationDepartments', component: AddUpdateStationDepartmentsComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StationDepartmentsRoutingModule { }
