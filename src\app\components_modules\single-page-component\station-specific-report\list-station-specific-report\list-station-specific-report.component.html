<div class="main-content">
  <div class="container-fluid">
    <!-- <app-summary-menu-list [routes]="routes"></app-summary-menu-list> -->
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>View Station Porter Reports</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="stationSpecificReport">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Station Porter Reports
                      </legend>
                      <div class="row">
                        <div class="col-4">
                          <mat-form-field>
                            <mat-label>
                              Report Type<span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select formControlName="report_type">
                              <mat-option
                                *ngFor="let value of reports"
                                [value]="value.value"
                              >
                                {{ value.label }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="
                                  stationSpecificReport.controls.report_type
                                "
                                [fieldName]="'Report Type'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-4">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="stationSpecificReport.controls.from_date"
                            [fieldName]="FromDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-4">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="stationSpecificReport.controls.to_date"
                            [fieldName]="ToDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button
                            mat-raised-button
                            class="btn btn-white pull-right"
                            (click)="reset()"
                          >
                            Reset
                          </button>
                          <button
                            mat-raised-button
                            type="submit"
                            class="btn btn-primary pull-right"
                            (click)="getSpecificReportpor()"
                          >
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>

                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="stationSpecificTable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <ng-container matColumnDef="name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Name
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.name }}</td>
                      </ng-container>

                      <ng-container matColumnDef="count">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Count
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.count }}
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="['Name', 'Count']"
  [title]="'Station Specific Report'"
  [datas]="pdfData"
>
</app-table-for-pdf>
