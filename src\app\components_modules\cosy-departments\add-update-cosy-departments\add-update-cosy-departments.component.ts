import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subject, ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-add-update-cosy-departments',
  templateUrl: './add-update-cosy-departments.component.html',
  styleUrls: ['./add-update-cosy-departments.component.scss']
})
export class AddUpdateCosyDepartmentsComponent implements OnInit {

  departmentForm: FormGroup;
  locationsList: any;
  stationLocations = [];
  protected _onDestroy = new Subject<void>();
  public locationFilterCtrl: FormControl = new FormControl();
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  constructor(
    private readonly fb: FormBuilder,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    public location: Location
  ) {
    
   }

  ngOnInit() {
    this.addDepartmentForm();
    if (this.activatedRoute.snapshot.params.id) {
      this.getCoSyDepartmentById(this.activatedRoute.snapshot.params.id);
    }else{
      this.getLocations();
    }
    if(this.locationsList) {
      this.filteredLocations.next(this.locationsList.slice());
      this.locationFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.filterLocations();
        });
    }
  }

  addDepartmentForm() {
    this.departmentForm = this.fb.group({
      department_name: ['', Validators.required],
      status: [true, Validators.required],
      department_code: ['', Validators.required],
      locations: ['', Validators.required]
    });
  }

  getLocations(locations?) {
    this.facilityConfig.getCoSyDepLocations().subscribe((res) => {
      if (res) {
        this.locationsList = res;
        if (this.activatedRoute.snapshot.params.id && locations) {
          this.locationsList = locations.concat(this.locationsList);
        }
        this.filterLocations();
        this.filteredLocations.next(this.locationsList.slice());
        this.locationFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.filterLocations();
          });
      }      
    });
  }

  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getCoSyDepartmentById(id) {
    this.userService.getCoSyDepartmentById(id).subscribe(res => {
      if (res) {
        this.departmentForm.patchValue(res[0]);
        res[0].station_locations.filter(loc => {
          this.stationLocations.push(loc.location_id)
        })
        this.departmentForm.get('locations').setValue(this.stationLocations);
        this.getLocations(res[0].station_locations);
      }
    })
  }

  saveDepartment(actiontype?) {
    let Id: any;
    if (this.departmentForm.valid) {
      if (actiontype === 'update') {
        Id = Number(this.activatedRoute.snapshot.params.id);
        this.userService.updateCoSyDepartments(Id, this.departmentForm.value).subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully updated CoSy Department`, 'Success');
        }, err => {
          console.log(err);
        });
      } else {
        this.userService.addCoSyDepartments(this.departmentForm.value).subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully added CoSy Department`, 'Success');
        }, err => {
          console.log(err);
        });
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.departmentForm.markAllAsTouched();
    }
  }

}
