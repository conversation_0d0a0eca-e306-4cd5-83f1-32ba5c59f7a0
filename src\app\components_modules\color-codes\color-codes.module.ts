import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ColorCodesRoutingModule } from './color-codes-routing.module';
import { ListColorCodesComponent } from './list-color-codes/list-color-codes.component';
import { AddUpdateColorCodesComponent } from './add-update-color-codes/add-update-color-codes.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListColorCodesComponent, AddUpdateColorCodesComponent],
  imports: [
    CommonModule,
    ColorCodesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class ColorCodesModule { }
