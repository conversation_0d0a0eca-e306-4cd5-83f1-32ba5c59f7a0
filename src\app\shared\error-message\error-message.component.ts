import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ValidationService } from 'src/app/Apiservices/validation/validation.service';

@Component({
  selector: 'app-error-message',
  templateUrl: './error-message.component.html',
  styleUrls: ['./error-message.component.scss']
})
export class ErrorMessageComponent implements OnInit {

  @Input() control: FormControl;
  @Input() fieldName: string;
  @Input() fieldType: string;
  @Input() completeError: string;

  constructor(private readonly validationService: ValidationService) { }

  ngOnInit() {
  }
  get errorMessage() {
    for (const propertyName in (this.control && this.control.errors)) {
      if ((this.control && this.control.errors).hasOwnProperty(propertyName) && this.control.touched) {
        return this.validationService.getValidatorErrorMessage(propertyName, this.fieldType,
          this.fieldName, (this.control && this.control.errors)[propertyName], this.completeError);
      }
    }
    return null;
  }

}
