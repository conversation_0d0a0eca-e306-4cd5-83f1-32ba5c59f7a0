<div class="main-content" *ngIf="showDomCondition">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()"
                >keyboard_backspace</em
              >
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">
                    Add Map Porter
                  </p>
                  <p *ngIf="activatedRoute.snapshot.params.id">
                    Update Map Porter
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="mapPorterForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row flex-wrap">
                        <div class="col-4">
                          <mat-form-field>
                            <mat-label
                              >Mobile Number
                              <span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select
                              formControlName="mobile_number"
                              (ngModelChange)="phoneChange($event)"
                            >
                              <mat-option
                                *ngFor="let val of mobileNumbers"
                                [value]="val.phone_no"
                              >
                                {{ val.phone_no }}</mat-option
                              >
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="mapPorterForm.controls.mobile_number"
                                [fieldName]="'Mobile Number'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-8">
                          <mat-form-field>
                            <mat-label>
                              Off Duty Porter
                              <span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select
                              formControlName="porter_id"
                              (ngModelChange)="porterChange($event)"
                            >
                              <mat-option
                                *ngFor="let val of offDutyPorters"
                                [value]="val.staff_id"
                                >{{ val.staff_name }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="mapPorterForm.controls.porter_id"
                                [fieldName]="'Off Duty Porter'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-4">
                          <mat-form-field>
                            <mat-label
                              >Escort Type
                              <span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select
                              formControlName="porter_type"
                              (ngModelChange)="stationLocationChange($event)"
                            >
                              <mat-option value="Central"> Central </mat-option>
                              <mat-option value="Station"> Station </mat-option>
                              <mat-option value="Express"> Express </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="mapPorterForm.controls.porter_type"
                                [fieldName]="'Escort Type'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div
                          class="col-8"
                          *ngIf="
                            mapPorterForm.controls.porter_type.value ===
                            'Station'
                          "
                        >
                          <mat-form-field>
                            <mat-label
                              >Station Location
                              <span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select formControlName="loc_id_list">
                              <mat-option
                                *ngFor="let val of locations"
                                [value]="val.locationId"
                                >{{ val.locationName }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="mapPorterForm.controls.loc_id_list"
                                [fieldName]="'Station Location'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div
                          class="col-6"
                          *ngIf="
                            mapPorterForm.controls.porter_type.value ===
                            'Express'
                          "
                        >
                          <mat-form-field>
                            <mat-label
                              >Station Location
                              <span class="error-css"
                                ><span class="error-css">*</span></span
                              >
                            </mat-label>
                            <mat-select
                              formControlName="loc_id_list"
                              multiple="true"
                            >
                              <mat-option
                                *ngFor="let val of locations"
                                [value]="val.locationId"
                                >{{ val.locationName }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="mapPorterForm.controls.loc_id_list"
                                [fieldName]="'Station Location'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button
                        mat-raised-button
                        *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right"
                        (click)="save('add')"
                        style="float: right; margin-top: 2%"
                      >
                        Submit
                      </button>
                      <button
                        mat-raised-button
                        *ngIf="activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right"
                        (click)="save('update')"
                        style="float: right; margin-top: 2%"
                      >
                        Update
                      </button>
                      <button
                        mat-raised-button
                        (click)="mapPorterForm.reset(); getMapPorterById()"
                        class="btn btn-white pull-right"
                        style="float: right; margin-top: 2%"
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<section class="container-fluid text-left" *ngIf="!showDomCondition">
  <div class="row py-3">
    <div class="col-12 card p-3">
      <form [formGroup]="mapPorterForm">
        <div class="row">
          <div class="col-2">
            <mat-label class="font_bolder"
              >Mobile Number
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>
            <mat-form-field>
              <input
                matInput
                placeholder="Search mobile no..."
                (keyup)="filterMobileNumber($event.target.value)"
                id="mobileNumberRef"
              />
            </mat-form-field>
            <div class="list-group mobile__number__list">
              <mat-list>
                <mat-list-item
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of filteredMobileNumber; let i = index"
                  (click)="setActiveList(i, val.phone_no)"
                  [ngClass]="{ active: activeIndex == i }"
                >
                  {{ val.phone_no }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.mobile_number"
                [fieldName]="'Mobile Number'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>
          <div class="col-4">
            <mat-label class="font_bolder">
              Off Duty Porter
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>
            <mat-form-field>
              <input
                matInput
                placeholder="Search off duty porter..."
                (keyup)="filterOffDutyPorter($event.target.value)"
                id="onOffDutyPorterRef"
              />
            </mat-form-field>

            <div class="list-group mobile__number__list">
              <mat-list>
                <mat-list-item
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of filteredOffDutyPorter; let i = index"
                  (click)="setActiveListDutyPorter(i, val)"
                  [ngClass]="{ active: activeIndexDutyPorter == i }"
                >
                  {{ val.staff_id }} - {{ val.staff_name }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.porter_id"
                [fieldName]="'Off Duty Porter'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>
          <div class="col-2">
            <mat-label class="font_bolder"
              >Escort Type
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>

            <div
              class="list-group mobile__number__list"
              style="margin-top: 22%"
            >
              <mat-list>
                <mat-list-item
                  type="button"
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of escortTypeList; let i = index"
                  (click)="setActiveListEscortType(i, val.escortType)"
                  [ngClass]="{ active: activeIndexEscortType == i }"
                >
                  {{ val.escortType }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.porter_type"
                [fieldName]="'Escort Type'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>

          <div
            class="col-4"
            *ngIf="mapPorterForm.controls.porter_type.value === 'Station'"
          >
            <mat-label class="font_bolder"
              >Station Location
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>
            <mat-form-field>
              <input
                matInput
                placeholder="Search location..."
                (keyup)="filterStationLocation($event.target.value)"
                id="locationStation"
              />
            </mat-form-field>
            <!-- <mat-select formControlName="loc_id_list" #stationLocationRef>
                                <mat-option *ngFor="let val of locations" [value]="val.location_id">
                                    {{val.location_name}}
                                </mat-option>
                            </mat-select> -->

            <div class="list-group mobile__number__list">
              <mat-list>
                <mat-list-item
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of filteredStationLocation; let i = index"
                  (click)="setActiveListStationLocation(i, val)"
                  [ngClass]="{ active: activeIndexStationLocationStation == i }"
                >
                  {{ val.locationName }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.loc_id_list"
                [fieldName]="'Station Location'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>
          <div
            class="col-4"
            *ngIf="mapPorterForm.controls.porter_type.value === 'Express'"
          >
            <mat-label class="font_bolder"
              >Station Location
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>
            <mat-form-field>
              <input
                matInput
                placeholder="Search location..."
                (keyup)="filterStationLocation($event.target.value)"
              />
            </mat-form-field>
            <div class="list-group mobile__number__list">
              <mat-checkbox (click)="changeCheckbox()" formControlName="selectAll">All</mat-checkbox>
              <mat-list>
                <mat-list-item
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of filteredStationLocation; let i = index"
                  (click)="setActiveListStationLocationExpress(i, val)"
                  [ngClass]="{
                    active:
                      activeIndexStationLocation &&
                      activeIndexStationLocation.includes(val.locationId)
                  }"
                >
                  {{ val.locationName }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.loc_id_list"
                [fieldName]="'Station Location'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>

          <!-- this is task based sub category selection -->
          <div
            class="col-4"
            *ngIf="mapPorterForm.controls.porter_type.value === 'Taskbased'"
          >
            <mat-label class="font_bolder"
              >Job Type
              <span class="error-css"><span class="error-css">*</span></span>
            </mat-label>
            <mat-form-field>
              <input
                matInput
                placeholder="Search job type..."
                (keyup)="filteredJobTypeList($event.target.value)"
              />
            </mat-form-field>

            <div class="list-group mobile__number__list">
              <mat-list>
                <mat-list-item
                  type="button"
                  class="list-group-item list-group-item-action border"
                  *ngFor="let val of filteredJobType$; let i = index"
                  (click)="setActiveListTaskBased(i, val)"
                  [ngClass]="{
                    active:
                      activeIndexTaskBasedSub &&
                      activeIndexTaskBasedSub.includes(val.sj_category_id)
                  }"
                >
                  {{ val.mj_category_name }} - {{ val.sj_category_name }}
                </mat-list-item>
              </mat-list>
            </div>
            <mat-error class="pull-left error-css">
              <app-error-message
                [control]="mapPorterForm.controls.sub_id_list"
                [fieldName]="'Sub Job Category'"
                [fieldType]="'select'"
              >
              </app-error-message>
            </mat-error>
          </div>
          <div class="col-12">
            <button
              mat-raised-button
              *ngIf="!activatedRoute.snapshot.params.id"
              class="btn btn-primary pull-right"
              (click)="save('add')"
              style="float: right; margin-top: 2%"
            >
              Submit
            </button>
            <button
              mat-raised-button
              *ngIf="activatedRoute.snapshot.params.id"
              class="btn btn-primary pull-right"
              (click)="save('update')"
              style="float: right; margin-top: 2%"
            >
              Update
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</section>
