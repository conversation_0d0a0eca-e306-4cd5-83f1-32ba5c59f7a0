import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { SettingsService } from "src/app/Apiservices/settings/settings.service";
import { Location } from "@angular/common";

@Component({
  selector: "app-add-update-mangge-help-file",
  templateUrl: "./add-update-mangge-help-file.component.html",
  styleUrls: ["./add-update-mangge-help-file.component.scss"],
})
export class AddUpdateManggeHelpFileComponent implements OnInit {
  fileForm: FormGroup;
  fileupload: File = null;
  mediaFile: any;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly settingService: SettingsService
  ) {
    this.fileForm = this.fb.group({
      file: [""],
      topic: ["", Validators.required],
      video: [""],
    });
  }

  ngOnInit() {}

  getFile(file) {
    this.fileupload = file;
  }

  getMediaFile(file) {
    this.mediaFile = file;
  }

  save() {
    if (
      this.fileForm.valid &&
      (this.fileForm.get("file").value || this.fileForm.get("video").value)
    ) {
      const { topic } = this.fileForm.value;
      const data = new FormData();
      data.append("file", this.fileupload);
      data.append("file", this.mediaFile);
      this.settingService.addUpdateMangeHelpFile({ data, topic }).subscribe(
        () => {
          this.fileupload = null;
          this.mediaFile = null;
          this.location.back();
          this.toastr.success(`Successfully saved manage file`, "Success");
        },
        (err) => {
          console.log(err);
        }
      );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.fileForm.markAllAsTouched();
    }
  }
}
