import { FormBuilder, FormGroup } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import * as moment from "moment";
import { ReportsService } from '../../../Apiservices/reports/reports.service';

@Component({
  selector: 'app-monthlyraw-report',
  templateUrl: './monthlyraw-report.component.html',
  styleUrls: ['./monthlyraw-report.component.scss']
})
export class MonthlyrawReportComponent implements OnInit {

  months = [{ id: '01', val: 'January' },
  { id: '02', val: 'February' },
  { id: '03', val: 'March' },
  { id: '04', val: 'April' },
  { id: '05', val: 'May' },
  { id: '06', val: 'June' },
  { id: '07', val: 'July' },
  { id: '08', val: 'August' },
  { id: '09', val: 'September' },
  { id: '10', val: 'October' },
  { id: '11', val: 'November' },
  { id: '12', val: 'December' },
  ];
  today = new Date();
  currentYear = this.today.getFullYear();
  // years = [this.currentYear, this.currentYear - 1];
  years = [];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');
  monthlyReportForm: FormGroup;
  constructor(
    private readonly reportService: ReportsService,
    private readonly fb: FormBuilder,
  ) { 
    for (let i = 0; i < 5; i++) {
      this.years.push(this.currentYear - i) 
    }
  }

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.monthlyReportForm = this.fb.group({
      month: [this.currMonth],
      year: [this.years[0]]
    });
  }

  downloadReport() {
    this.reportService.getMonthlyReports(this.monthlyReportForm.value);
  }

}
