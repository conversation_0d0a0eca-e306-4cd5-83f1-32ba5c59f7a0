import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListMessageTemplatesComponent } from './list-message-templates/list-message-templates.component';
import { AddUpdateMessageTemplatesComponent } from './add-update-message-templates/add-update-message-templates.component';


const routes: Routes = [
  {path: '', component: ListMessageTemplatesComponent},
  {path: 'addmessage', component: AddUpdateMessageTemplatesComponent},
  {path: 'updatemessage/:id', component: AddUpdateMessageTemplatesComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MessageTemplatesRoutingModule { }
