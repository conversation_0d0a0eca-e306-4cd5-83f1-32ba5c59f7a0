import { Component, OnInit } from "@angular/core";
import { map, tap } from "rxjs/operators";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { LocalDateConversionPipe } from "src/app/shared/pipes/local-date-conversion.pipe";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { UamReportsService } from "../uam-report/uam-reports.service";

@Component({
  selector: "app-active-users",
  templateUrl: "./active-users.component.html",
  styleUrls: ["./active-users.component.scss"],
  providers: [LocalDateConversionPipe],
})
export class ActiveUsersComponent implements OnInit {
  roles$: any;
  userList$: any;
  allRoles$: any;
  allRolesDictionary: any = {};
  userRoleDataMapping$: any;
  allRoleForExcel$: any;
  pdfData: any;

  constructor(
    private uamReportService: UamReportsService,
    private readonly loader: LoaderService,
    private localDateConversion: LocalDateConversionPipe
  ) {}

  ngOnInit() {
    this.roles$ = this.uamReportService.getUAMRoles();
    this.allRoles$ = this.roles$
      .pipe(
        tap((data: any) => {
          data.map(
            (roles) => (this.allRolesDictionary[roles.role_name] = false)
          );
        })
      )
      .subscribe((data) => this.getUserList());
    this.allRoleForExcel$ = this.roles$.pipe(
      map((allRoleExcel: any) => allRoleExcel.map((data) => data.role_name))
    );
  }

  getUserList() {
    this.userList$ = this.uamReportService
      .getUAMUsers()
      .pipe(map((data: any) => data.filter((user) => !user.status)));
    this.userRoleDataMapping$ = this.uamReportService.getUserRoleDataMapping(
      this.userList$,
      this.allRolesDictionary
    );
    this.setPageSizeOptions();
  }
  async exportTable(fileType) {
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "userReportDumb",
        "users_active_report"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("userReportDumb", "users_active_report");
    }
  }

  setPageSizeOptions() {
    this.userRoleDataMapping$.subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Users: key.username,
              ...key.userRoleAssign.reduce((allRoles, everyRole) => {
                return { [everyRole]: everyRole ? true : false, ...allRoles };
              }, {}),
              "Last Logged in date": this.localDateConversion.transform(
                key.last_login_date,
                "full"
              ),
              "Created Date": this.localDateConversion.transform(
                key.created_date,
                "full"
              ),
              "Created By": this.localDateConversion.transform(
                key.created_by,
                "full"
              ),
              Remarks: key.remarks,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
}
