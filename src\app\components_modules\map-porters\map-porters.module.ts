import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MapPortersRoutingModule } from './map-porters-routing.module';
import { AddUpdateAssignPorterComponent } from './add-update-assign-porter/add-update-assign-porter.component';
import { ListMapPortersComponent } from './list-map-porters/list-map-porters.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { MatListModule } from '@angular/material/list';


@NgModule({
  declarations: [AddUpdateAssignPorterComponent, ListMapPortersComponent],
  imports: [
    CommonModule,
    MapPortersRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatListModule
  ]
})
export class MapPortersModule { }
