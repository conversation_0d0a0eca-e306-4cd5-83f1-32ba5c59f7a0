import * as jspdf from 'jspdf';
import html2canvas from 'html2canvas';
import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root'
})
export class TableUtilPDf {

    static async exportToPdf(tableId: string, name?: string, pageType?) {
        const timeSpan = new Date().toUTCString();
        const prefix = name || 'ExportResult';
        const fileName = `${prefix}-${timeSpan}`;
        var pdfElementName = 'pdf-download';
        if (tableId.includes('hourSummaryReport')) {
            pdfElementName = 'pdf-download-hourlyReport';
        }
        const targetTableElm = document.getElementById(pdfElementName);
        const genrated = await html2canvas(targetTableElm).then(canvas => {
            const imgWidth = pageType ? pageType : 210;
            const pageHeight = 320;
            const imgHeight = canvas.height * imgWidth / canvas.width;
            let heightLeft = imgHeight;
            const contentDataURL = canvas.toDataURL('image/png');
            const pdf = new jspdf(`${pageType ? 'l' : 'p'}`, 'mm', 'true', true);
            let position = 0;
            if (contentDataURL === 'data:,') {
                return 'failed to genrate';
            }
            pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight, '', 'FAST');
            heightLeft -= pageHeight;
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight, '', 'FAST');
                heightLeft -= pageHeight;
            }
            pdf.save(`${fileName}.pdf`);
            return 'generated';
        }).catch(() => {
            return 'failed';
        });
        if (genrated) {
            return genrated;
        }
    }

    static async exportArrayToPdfStyled(arr: any[], name?: string) {
        try {
            let expobj: any[] = [];

            for (var i = 0; i < arr.length; i++) {
                expobj.push({
                    "Sl No": i + 1,
                    "Location": arr[i].Location ? arr[i].Location : "--",
                    "Total Completed": arr[i].TotalComplete ? arr[i].TotalComplete : 0,
                    "Total Cancelled": arr[i].TotalCancelled ? arr[i].TotalCancelled : 0,
                    "Total Jobs": arr[i].TotalJobs ? arr[i].TotalJobs : 0,
                    "% Completed": arr[i].CompletePercentage ? arr[i].CompletePercentage : 0,
                    "% Cancelled": arr[i].CancelledPercentage ? arr[i].CancelledPercentage : 0,
                    "Total %": arr[i].Totalpercentage ? arr[i].Totalpercentage : 0,
                });
            }

            // Create temporary table
            const table = document.createElement('table');
            table.id = 'temp-pdf-table';
            table.style.width = '100%';
            table.style.borderCollapse = 'collapse';
            table.style.fontFamily = 'Arial, sans-serif';
            table.style.fontSize = '10px';

            // Table headers
            const headerRow = document.createElement('tr');
            Object.keys(expobj[0] || {}).forEach(key => {
                const th = document.createElement('th');
                th.textContent = key;
                th.style.border = '1px solid #ccc';
                th.style.padding = '5px';
                th.style.backgroundColor = '#006d77';
                th.style.color = 'white';
                th.style.textAlign = 'center';
                headerRow.appendChild(th);
            });
            table.appendChild(headerRow);

            // Table rows
            expobj.forEach(row => {
                const tr = document.createElement('tr');
                Object.values(row).forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell.toString();
                    td.style.border = '1px solid #ccc';
                    td.style.padding = '5px';
                    td.style.textAlign = 'center';
                    tr.appendChild(td);
                });
                table.appendChild(tr);
            });

            document.body.appendChild(table); // Append before rendering canvas

            const canvas = await html2canvas(table);
            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 210; // A4 width in mm
            const pageHeight = 297;
            const imgHeight = canvas.height * imgWidth / canvas.width;
            let heightLeft = imgHeight;

            const pdf = new jspdf('p', 'mm', 'a4');
            let position = 0;
            let pageNum = 1;

            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            pdf.setFontSize(10);
            pdf.text(`Page ${pageNum}`, imgWidth - 20, pageHeight - 10);
            heightLeft -= pageHeight;

            while (heightLeft > 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pageNum++;
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                pdf.text(`Page ${pageNum}`, imgWidth - 20, pageHeight - 10);
                heightLeft -= pageHeight;
            }

            const timeSpan = new Date().toUTCString();
            const prefix = name || 'ExportResult';
            const fileName = `${prefix}-${timeSpan}.pdf`;
            pdf.save(fileName);

            document.body.removeChild(table);
            return true;
        } catch (err) {
            console.error('PDF Export Error:', err);
            return false;
        }
    }

}
