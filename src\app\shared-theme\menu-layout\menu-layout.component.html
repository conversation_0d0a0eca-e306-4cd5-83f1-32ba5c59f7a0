<nav class="navbar navbar-expand-md bg-light navbar-light" style="height: 50px">
  <div class="navbar-collapse collapse pt-2 pt-md-0" id="navbar2">
    <ul class="navbar-nav">
      <li
        routerLinkActive="active"
        style="height: 25px"
        *ngFor="let menuItem of menuItems"
        class="{{ menuItem.class }} nav-item "
      >
        <!-- condition to check the report menu or not , dont remove might be client ask for this condition for future, then you gonnna thanks to me -->
        <!-- *ngIf="
      menuItem.subMenu && menuItem.title != 'Reports';
      else singlemenu
    "
   -->

        <span>
          <a
            href="javascript:void(0);"
            style="margin-top: -5px; cursor: pointer"
            data-target="sub_menu"
            [matMenuTriggerFor]="sub_menu"
            class="nav-link"
            routerLinkActive="active"
          >
            <em class="material-icons">{{ menuItem.icon }}</em>
            <p style="display: inline-block">
              {{ menuItem.title | translatePipe }}
            </p>
          </a>
          <mat-menu #sub_menu="matMenu">
            <span
              routerLinkActive="active"
              style="height: 25px"
              *ngFor="let subItem of menuItem.subMenu"
              class="{{ subItem.class }} nav-item-submenu"
            >
              <a
                href="javascript:void(0);"
                style="margin-top: -5px; color: #555555"
                class="nav-link"
                [routerLink]="[subItem.path]"
              >
                <em class="material-icons">{{ subItem.icon }}</em>
                <p style="display: inline-block; text-align: left">
                  {{ subItem.title | translatePipe }}
                </p>
              </a>
            </span>
          </mat-menu>
        </span>
        <ng-template #singlemenu>
          <a
            href="javascript:void(0);"
            style="margin-top: -5px"
            class="nav-link"
            [routerLink]="[menuItem.path]"
          >
            <em class="material-icons">{{ menuItem.icon }}</em>
            <p style="display: inline-block">
              {{ menuItem.title | translatePipe }}
            </p>
          </a>
        </ng-template>
      </li>
      <!-- <li style="height: 25px" class="nav-item">
                <a href="javascript:void(0);" style="margin-top:-5px; cursor: pointer;" data-target="sub_menu_language" [matMenuTriggerFor]="sub_menu_language" class="nav-link">
                    <p style="display: inline-block">Language</p>
                </a>
                <mat-menu #sub_menu_language="matMenu">
                    <span routerLinkActive="active" style="height: 25px" class="nav-item">
                        <a href="javascript:void(0);" style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link"
                            (click)="translate.useLang('en')">
                            <p style="display: inline-block">English</p>
                        </a>
                        <a href="javascript:void(0);" style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link"
                            (click)="translate.useLang('fe')">
                            <p style="display: inline-block">French</p>
                        </a>
                    </span>
                </mat-menu>
            </li>
            <li style="height: 25px">

            </li> -->
    </ul>
  </div>
</nav>

<!-- <nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="collapse navbar-collapse" id="navbarNavDropdown">
        <ul class="navbar-nav">
            <li class="nav-item dropdown" style="height: 25px" routerLinkActive="active"
                *ngFor="let menuItem of menuItems">
                <a *ngIf="menuItem.subMenu; else singlemenu" style="margin-top:-5px; cursor: pointer;" class="nav-link"
                    id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
                    <em class="material-icons">{{menuItem.icon}}</em>
                    <p style="display: inline-block">{{menuItem.title | translatePipe}}</p>
                </a>
                <ng-template #singlemenu>
                    <a style="margin-top:-5px" class="nav-link" [routerLink]="[menuItem.path]">
                        <em class="material-icons">{{menuItem.icon}}</em>
                        <p style="display: inline-block">{{menuItem.title | translatePipe}}</p>
                    </a>
                </ng-template>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                    <span style="height: 25px" *ngFor="let subItem of menuItem.subMenu"
                        class="{{subItem.class}} nav-item-submenu">
                        <a style="margin-top:-5px; color: #555555;" class="nav-link submenuitemclass" [routerLink]="[subItem.path]">
                            <em class="material-icons">{{subItem.icon}}</em>
                            <p style="display: inline-block">{{subItem.title | translatePipe}}</p>
                        </a>
                    </span>
                </div>
            </li>
            <li class="nav-item dropdown" style="height: 25px" routerLinkActive="active">
                <a style="margin-top:-5px; cursor: pointer;" class="nav-link" id="navbarDropdownMenuLinkl" role="button"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <p style="display: inline-block">Language</p>
                </a>
                <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLinkl">
                    <span style="height: 25px" class="nav-item">
                        <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link"
                            (click)="translate.useLang('en')">
                            <p style="display: inline-block">English</p>
                        </a>
                        <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link"
                            (click)="translate.useLang('fe')">
                            <p style="display: inline-block">French</p>
                        </a>
                    </span>
                </div>
            </li>
        </ul>
    </div>
</nav> -->
