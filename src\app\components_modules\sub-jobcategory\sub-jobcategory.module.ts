import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SubJobcategoryRoutingModule } from './sub-jobcategory-routing.module';
import { ListSubjobcategoryComponent } from './list-subjobcategory/list-subjobcategory.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateSubjobcategoryComponent } from './add-update-subjobcategory/add-update-subjobcategory.component';


@NgModule({
  declarations: [ListSubjobcategoryComponent, AddUpdateSubjobcategoryComponent],
  imports: [
    CommonModule,
    SubJobcategoryRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class SubJobcategoryModule { }
