import { DataSource } from '@angular/cdk/table';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder } from '@angular/forms';
import { FormGroup } from '@angular/forms';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import * as moment from "moment";
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import Swal from 'sweetalert2';

export interface UserData {
  select: boolean,
  requested_date: string,
  requested_by: string,
  module: string,
  old_name: string,
  old_status: string,
  new_name: string,
  new_status: string,
  reason: string,
  status: string
}

@Component({
  selector: 'app-list-approval-status',
  templateUrl: './list-approval-status.component.html',
  styleUrls: ['./list-approval-status.component.scss']
})
export class ListApprovalStatusComponent implements OnInit {
  approvalForm: FormGroup;
  displayedColumns: any;
  displayedColumnsWithCheckBox: string[] = ['select', 'requestedDate', 'requestedBy', 'module', 'oldName', 'oldStatus', 'newName', 'newStatus', 'reason', 'displayStatus'];
  displayedColumnsWithoutCheckBox: string[] = ['requestedDate', 'requestedBy', 'module', 'oldName', 'oldStatus', 'newName', 'newStatus', 'reason', 'displayStatus'];
  dataSource: MatTableDataSource<UserData>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  prevMonth = moment(new Date()).subtract(1, 'months');
  month = this.prevMonth['_d'];
  statuses = [
    { name: "Pending", id: 0 },
    { name: "Approved", id: 1 },
    { name: 'Rejected', id: 2 }
  ];
  selectAll: any = false;
  selectedApprovals: any = [];
  isDisableActions: any = true;

  constructor(
    private fb: FormBuilder,
    private toaster: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) {
    this.displayedColumns = this.displayedColumnsWithCheckBox;
  }

  ngOnInit() {
    this.createForm();
    this.getApprovalStatus();
  }

  createForm() {
    this.approvalForm = this.fb.group({
      from_date: [this.month],
      to_date: [new Date()],
      status: [0],
      displayStatus: ['pending'],
      id: ['']
    });
  }

  getApprovalStatus(dateType?: any) {
    let fromDate;
    let toDate;
    if (dateType == 'from') {
      const date = new Date(this.approvalForm.get("from_date").value);
      date.setDate(date.getDate() + 1);
      fromDate = new Date(date).toISOString();
    } else if (dateType == 'to') {
      const date = new Date(this.approvalForm.get("to_date").value);
      date.setDate(date.getDate() + 1);
      toDate = new Date(date).toISOString();
    }
    const filterData = {
      fromDate: fromDate || new Date(this.approvalForm.value.from_date).toISOString(),
      toDate: toDate || new Date(this.approvalForm.value.to_date).toISOString(),
      status: this.approvalForm.value.status
    }
    this.dataSource = new MatTableDataSource([]);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.facilityConfig.getApprovalStatus(filterData).subscribe(res => {
      if (res) {
        this.dataSource = new MatTableDataSource(res ? res : []);
        res.map(data => {
          if (this.approvalForm.value.status == 1) {
            data['displayOldCurrentStatus'] = data.newStatus == null ? '' : data.newStatus ? 'Active' : 'In-Active'
            data['displayOldCurrentName'] = data.newName
          } else {
            data['displayOldCurrentStatus'] = data.oldStatus == null ? '' : data.oldStatus ? 'Active' : 'In-Active'
            data['displayOldCurrentName'] = data.oldName
          }
          data['displayStatus'] = data['status']
        })
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      }
    });
  }

  takeAction(filterValue?: string) {
    // this.dataSource.filter = filterValue.trim().toLowerCase();
    // if (this.dataSource.paginator) {
    //   this.dataSource.paginator.firstPage();
    // }
    this.updateApprovals(filterValue);
  }

  onChangeStatus(e?: any) {
    console.log(e, this.approvalForm.value);
    if (e == 1 || e == 2) {
      this.displayedColumns = this.displayedColumnsWithoutCheckBox;
    } else {
      this.displayedColumns = this.displayedColumnsWithCheckBox;
    }
    this.getApprovalStatus();
    this.enableDisableActions();
  }

  updateApprovals(type?: any) {
    const data: any = { 'approvals': this.selectedApprovals }
    Swal.fire({
      title: 'Are you sure?',
      text: `You want to ${type}!`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes'
    }).then((result) => {
      if (result.value) {
        this.facilityConfig.updateApprovals(type, data).subscribe(res => {
          if (res) {
            this.toaster.success('Updated Successfully!', 'Success');
            this.getApprovalStatus();
            this.selectAll = false;
            this.selectedApprovals = [];
            this.enableDisableActions();
          }
        }, err => {
          this.selectedApprovals = [];
          this.selectAll = false;
          this.getApprovalStatus();
          this.enableDisableActions();
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        this.selectedApprovals = [];
        this.selectAll = false;
        this.getApprovalStatus();
        this.enableDisableActions();
        return;
      }
    });
  }

  clearFormValue(formField: string) {
    this.approvalForm.get([formField]).setValue('')
  }

  changeCheckbox(e: any, type: any) {
    let count = 0;
    switch (type) {
      case 'single':
        if (!e.select) {
          this.selectedApprovals.push(e);
        } else {
          this.selectedApprovals.map((data, index) => {
            if (data.id == e.id) {
              this.selectedApprovals.splice(index, 1);
            }
          })
        }
        break;
      case 'all':
        this.dataSource.data.map(res => {
          res.select = !this.selectAll ? true : false;
        })
        this.selectedApprovals = !this.selectAll ? this.dataSource.data : [];
        break;
    }
    this.enableDisableActions();
  }

  enableDisableActions() {
    if (this.selectedApprovals.length == 0 || this.approvalForm.value.status !== 0) {
      this.isDisableActions = true;
    } else {
      this.isDisableActions = false;
    }
  }

  getDate(type?: any) {
    const date = new Date(this.approvalForm.get("from_date").value);
    date.setDate(date.getDate() + 1);
    if (this.approvalForm.get("from_date").value && this.approvalForm.get("to_date").value) {
      if (this.approvalForm.get("from_date").value > this.approvalForm.get("to_date").value) {
        this.approvalForm.get("to_date").setValue("");
        this.toaster.error("To date should be greater then From date", "Error");
      } else {
        this.getApprovalStatus(type);
      }
    }
  }
}