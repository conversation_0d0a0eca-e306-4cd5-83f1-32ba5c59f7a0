<mat-form-field>
  <mat-label *ngIf="label"
    >{{ label }}<span class="error-css" *ngIf="required">*</span>
  </mat-label>
  <input
    matInput
    [ngxTimepicker]="timePick"
    [formControl]="control"
    placeholder="Chose time"
  />
  <ngx-material-timepicker-toggle
    [for]="timePick"
    matSuffix
    style="height: 17px"
    [disabled]="disable"
  >
  </ngx-material-timepicker-toggle>
  <ngx-material-timepicker #timePick></ngx-material-timepicker>

  <mat-error class="pull-left error-css">
    <div class="errormsg">
      <div *ngIf="errorMessage !== null">{{ errorMessage }}</div>
    </div>
  </mat-error>
</mat-form-field>
