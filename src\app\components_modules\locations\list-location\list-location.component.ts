import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { LocationData } from 'src/app/models/locations';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-list-location',
  templateUrl: './list-location.component.html',
  styleUrls: ['../../../scss/table.scss', './list-location.component.scss']
})
export class ListLocationComponent implements OnInit {
  displayedColumns: string[] = ['location_name', 'potering', 'location_sap_code', 'tower_id', 'level_id',
    'contact_no', 'priority', 'location_password', 'has_station_porter',
    'status', 'location_id'];

  dataSource: MatTableDataSource<LocationData>;

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  isExcelClicked: any;
  pdfData = [];
  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
  }

  ngOnInit() {
    this.getLocations();
  }


  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('listLocations', 'locations');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.isExcelClicked = true;
      setTimeout(() => {
        this.isExcelClicked = TableUtil.exportToExcel('listLocations', 'locations');
      }, 0);
    }
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe(res => {
      res = res && res.filter(key => {
        key.has_station_porter = key.has_station_porter ? 'Yes' : 'NO';
        return key;
      });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Location Name': key.location_name,
          Portering: key.potering ? 'Yes' : 'No',
          'SAP Ward Code': key.location_sap_code,
          'Tower Name': key.tower_name,
          'Level Name': key.level_name,
          'Contact Number': key.contact_no,
          Priority: key.priority,
          'Location Pass': key.location_password,
          'Has Station Porter': key.has_station_porter ? 'Yes' : 'No',
          Status: key.status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to Continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatelocation/${data.location_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatelocation/${data.location_id}`], { relativeTo: this.activatedRoute });
    }
  }
}

