import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TransportModesRoutingModule } from './transport-modes-routing.module';
import { ListTransportModesComponent } from './list-transport-modes/list-transport-modes.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateTransportModesComponent } from './add-update-transport-modes/add-update-transport-modes.component';


@NgModule({
  declarations: [ListTransportModesComponent, AddUpdateTransportModesComponent],
  imports: [
    CommonModule,
    TransportModesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class TransportModesModule { }
