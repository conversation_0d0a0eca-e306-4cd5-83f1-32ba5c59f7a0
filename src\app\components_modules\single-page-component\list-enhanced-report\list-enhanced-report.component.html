<div class="main-content">
    <div class="container-fluid">
        <!-- <app-summary-menu-list [routes]="routes"></app-summary-menu-list> -->
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export
                                </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xlsx')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555"
                                        (click)="exportTable('csv')" class="nav-link">
                                        <p style="display: inline-block">csv</p>
                                    </a>
                                    <!-- <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a> -->
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="nav" style="margin-left: 1%; line-height: 35px">
                                    <p>Search ACK</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <!-- <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div> -->

                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <form [formGroup]="enhancedReportForm">
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">
                                                Filter Enhanced Reports
                                            </legend>
                                            <div class="row">
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="FromDate"
                                                        [control]="enhancedReportForm.controls.from_date"
                                                        [fieldName]="FromDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="ToDate"
                                                        [control]="enhancedReportForm.controls.to_date"
                                                        [fieldName]="ToDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>

                                                <div class="col-4">
                                                    <mat-form-field>
                                                        <mat-label>Job No </mat-label>
                                                        <input matInput placeholder="Job No"
                                                            formControlName="order_no" />
                                                    </mat-form-field>
                                                </div>

                                                <div class="col-3">
                                                    <mat-form-field class="alignSelectBox">
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('main_category');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Main Category </mat-label>
                                                        <mat-select disableOptionCentering
                                                            formControlName="main_category"
                                                            (ngModelChange)="getsubJobCategory($event)">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="mainCategoryCtrl"
                                                                    [placeholderLabel]="'Find Main Category...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching main category found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option *ngFor="let data of filteredCategory | async"
                                                                [value]="data.mj_category_id">{{ data.category_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select
                              formControlName="main_category"
                              (ngModelChange)="getsubJobCategory($event)"
                            >
                              <mat-option
                                *ngFor="let data of mainJobCategories"
                                [value]="data.mj_category_id"
                                >{{ data.category_name }}
                              </mat-option>
                            </mat-select> -->
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <mat-form-field>
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('sub_category');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Sub Category </mat-label>
                                                        <mat-select disableOptionCentering
                                                            formControlName="sub_category"
                                                            (ngModelChange)="getModesOfTransport($event)">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="subCategoryCtrl"
                                                                    [placeholderLabel]="'Find Sub Category...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching sub category found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option
                                                                *ngFor="let subJob of filteredSubCategory | async"
                                                                [value]="subJob.sj_category_id">{{
                                                                subJob.sj_category_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select
                              formControlName="sub_category"
                              (ngModelChange)="getModesOfTransport($event)"
                            >
                              <mat-option
                                *ngFor="let subJob of subJobCategories"
                                [value]="subJob.sj_category_id"
                              >
                                {{ subJob.sj_category_name }}
                              </mat-option>
                            </mat-select> -->
                                                        <mat-hint *ngIf="!subJobCategories.length">No Sub Category
                                                            exists</mat-hint>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <mat-form-field class="alignSelectBox">
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('location');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Location<span class="error-css"></span>
                                                        </mat-label>
                                                        <mat-select disableOptionCentering formControlName="location">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="locationCtrl"
                                                                    [placeholderLabel]="'Find Location...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching location found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option
                                                                *ngFor="let location of filteredLocation | async"
                                                                [value]="location.location_id">{{ location.location_name
                                                                }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select formControlName="location">
                              <mat-option
                                *ngFor="let location of locations"
                                [value]="location.location_id"
                              >
                                {{ location.location_name }}
                              </mat-option>
                            </mat-select> -->
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-3">
                                                    <mat-form-field>
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('transport_mode');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Mode of Transport </mat-label>
                                                        <mat-select disableOptionCentering
                                                            formControlName="transport_mode">
                                                            <mat-option>
                                                                <ngx-mat-select-search
                                                                    [formControl]="modeOfTransportCtrl"
                                                                    [placeholderLabel]="'Find Mode of Transport...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching mode of transport found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option
                                                                *ngFor="let transport of filteredModeOfTransports | async"
                                                                [value]="transport.transport_mode_id">{{
                                                                transport.transport_mode_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select formControlName="transport_mode">
                              <mat-option
                                *ngFor="let transport of modesOfTransport"
                                [value]="transport.transport_mode_id"
                              >
                                {{ transport.transport_mode_name }}
                              </mat-option>
                            </mat-select> -->
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4">
                                                    <mat-form-field>
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('status');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Status</mat-label>
                                                        <mat-select disableOptionCentering formControlName="status">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="statusCtrl"
                                                                    [placeholderLabel]="'Find Status...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching status found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option *ngFor="let status of filteredStatus | async"
                                                                [value]="status.condition">{{ status.condition }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select formControlName="status">
                              <mat-option
                                *ngFor="let status of statuss"
                                [value]="status.condition"
                              >
                                {{ status.condition }}
                              </mat-option>
                            </mat-select> -->
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4">
                                                    <mat-form-field style="margin-top: .9%;">
                                                        <mat-label>Patient Name/NRIC </mat-label>
                                                        <input matInput placeholder="Patient Name/NRIC"
                                                            formControlName="nric" />
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-4">
                                                    <mat-form-field>
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('resource');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Resource </mat-label>
                                                        <mat-select disableOptionCentering formControlName="resource">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="resourceCtrl"
                                                                    [placeholderLabel]="'Find Resource...'"
                                                                    [noEntriesFoundLabel]="
                                  'no matching resource found'
                                "></ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option *ngFor="let staff of filteredResources | async"
                                                                [value]="staff.staff_id">{{ staff.staff_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                        <!-- <mat-select formControlName="resource">
                              <mat-option
                                *ngFor="let staff of staffs"
                                [value]="staff.staff_id"
                              >
                                {{ staff.staff_name }}
                              </mat-option>
                            </mat-select> -->
                                                    </mat-form-field>
                                                </div>

                                                <div class="col-3">
                                                    <br />
                                                    <div class="alignInput">
                                                        <mat-checkbox formControlName="ack_required"></mat-checkbox>
                                                        <mat-label>ACK Required<span class="error-css"></span>
                                                        </mat-label>
                                                    </div>
                                                </div>
                                                <div class="col-3"></div>
                                                <div class="col-8"></div>
                                                <div class="col-4">
                                                    <button mat-raised-button type="submit"
                                                        class="btn btn-primary pull-right" (click)="searchByData()">
                                                        Search
                                                    </button>
                                                    <button mat-raised-button (click)="restForm()"
                                                        class="btn btn-white pull-right">
                                                        Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto;"
                                        [ngStyle]="{'height': (dataSource && dataSource.filteredData.length === 0) ? '150px' : '600px'}">
                                        <table id="enhancedReport" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="order_no">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Job No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.order_no ? row?.order_no : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="request_type">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Request Type
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.request_type
                                                    ? row?.request_type == "Emergency"
                                                    ? "Urgent"
                                                    : row?.request_type
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="order_from">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Order From
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.order_from ? row?.order_from : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="created_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Creation Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.created_date
                                                    ? (row?.created_date)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="task_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Task Time
                                                </th>

                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.task_time && row?.task_time !== '' ? (row.task_time) :
                                                    '--'}}
                                                    <!-- <div *ngIf="row?.request_type ==='Normal' || row?.request_type ==='Urgent'; else taskTime">
                            {{ row?.created_date ? (row?.created_date | localDateConversion: "full") : "--" }}
                          </div>
                          <ng-template #taskTime>
                            {{ row?.due_time ? (row?.due_time | localDateConversion: "full") : "--" }}
                          </ng-template> -->
                                                </td>

                                            </ng-container>

                                            <ng-container matColumnDef="assign_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Assigned Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.assign_time
                                                    ? (row?.assign_time)
                                                    : "--"
                                                    }}
                                                    <!--  | localDateConversion: "full" -->
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="start_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Start Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.start_time
                                                    ? (row?.start_time)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="completion_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Completion Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.completion_time
                                                    ? (row?.completion_time)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancel_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.cancel_time
                                                    ? (row?.cancel_time)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="requestor">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Requestor
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.requestor ? row?.requestor : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patient_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.patient_name ? row?.patient_name : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="nric">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    NRIC
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.nric ? row?.nric : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="from_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    From
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.from_location ? row?.from_location : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="from_room">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Room No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.from_room ? row?.from_room : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="from_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.from_bed ? row?.from_bed : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="to_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    To
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.to_location ? row?.to_location : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="to_room">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Room No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.to_room ? row?.to_room : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="to_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.to_bed ? row?.to_bed : "--" }}
                                                </td>
                                            </ng-container>
                                            <!-- return_location start -->
                                            <ng-container matColumnDef="return_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Return
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.return_location ? row?.return_location : "--" }}
                                                </td>
                                            </ng-container>
                                            <!-- return_location ends -->

                                            <!-- return_location start -->
                                            <ng-container matColumnDef="return_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.return_bed ? row?.return_bed : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="remarks">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Remarks
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.remarks ? row?.remarks : "--" }}
                                                </td>
                                            </ng-container>


                                            <ng-container matColumnDef="job_status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Job Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.job_status ? row?.job_status : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="main_category">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Main Category
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.main_category ? row?.main_category : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="sub_category">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Sub Category
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.sub_category ? row?.sub_category : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="transport_mode">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Transport Mode
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.transport_mode ? row?.transport_mode : "--" }}
                                                </td>
                                            </ng-container>

                                            <!-- Std KPI start -->
                                            <ng-container matColumnDef="std_kpi">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Std KPI
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.std_kpi ? row?.std_kpi : "--" }}
                                                </td>
                                            </ng-container>
                                            <!--  Std KPI ends -->


                                            <ng-container matColumnDef="req_resp">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Req-Resp(Mins)
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.req_resp ? row?.req_resp : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resp_comp">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resp-Comp(Mins)
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resp_comp ? row?.resp_comp : "--" }}
                                                </td>
                                            </ng-container>


                                            <!-- within KPI start -->
                                            <ng-container matColumnDef="within_kpi">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Within KPI
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.within_kpi ? "Yes" : "No" }}
                                                </td>
                                            </ng-container>
                                            <!-- winthin KPI ends -->
                                            <ng-container matColumnDef="smart_assigned">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Smart Assigned
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.smart_assigned ? "Yes" : "No" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="isolation_precaution">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Isolation Precaution
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.isolation_precaution ? row?.isolation_precaution : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resource1">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 1
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource1 ? row?.resource1 : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resource2">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 2
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource2 ? row?.resource2 : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="created_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Created By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.created_by ? row?.created_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="modified_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Modified By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.modified_by ? row?.modified_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="modified_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Modified Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.modified_date
                                                    ? (row?.modified_date)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="assigned_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Assigned By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.assigned_by ? row?.assigned_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="porterack_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Acknowledged Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.porterack_time ? row?.porterack_time : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="scan_patient">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient IC scan
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.scan_patient === true ? "Yes" : "No" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patient_scan_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient IC Scanned Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.patient_scan_time}}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="delay_reason">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Delay Reason
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.delay_reason ? row?.delay_reason : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancel_reason">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Reason
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancel_reason ? row?.cancel_reason : "--" }}
                                                </td>
                                            </ng-container>

                                            <!-- cancel_reason start -->
                                            <ng-container matColumnDef="cancel_remarks">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Remarks
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancel_remarks ? row?.cancel_remarks : "--" }}
                                                </td>
                                            </ng-container>
                                            <!-- cancel_reason ends -->

                                            <!-- Cancelled By start -->
                                            <ng-container matColumnDef="cancelled_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancelled By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancelled_by ? row?.cancelled_by : "--" }}
                                                </td>
                                            </ng-container>
                                            <!-- Cancelled By ends -->


                                            <!-- Smart Assigned start -->
                                            <!-- <ng-container matColumnDef="smart_assigned">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Smart Assigned
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.smart_assigned ? row?.smart_assigned : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Smart Assigned ends -->
                                            <!-- return_location ends -->

                                            <!-- Assigned By start -->
                                            <!-- <ng-container matColumnDef="assigned_by">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Assigned By
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.assigned_by ? row?.assigned_by : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Assigned By ends -->

                                            <!-- Responded By start -->
                                            <!-- <ng-container matColumnDef="responded_by">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Responded By
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.responded_by ? row?.responded_by : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Responded By ends -->

                                            <!-- Completed By start -->

                                            <!-- <ng-container matColumnDef="completed_by">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Completed By
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.completed_by ? row?.completed_by : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Completed By ends -->

                                            <!-- Responded Mode start -->
                                            <!-- <ng-container matColumnDef="responded_mode">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Responded Mode
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.responded_mode ? row?.responded_mode : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Responded Mode ends -->

                                            <!-- Completed Mode start -->
                                            <!-- <ng-container matColumnDef="completed_mode">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Completed Mode
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.completed_mode ? row?.completed_mode : "--" }}
                        </td>
                      </ng-container> -->
                                            <!-- Completed Mode ends -->

                                            <ng-container *ngIf="showAck" matColumnDef="ack">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    ACK
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.ack ? row?.ack : "--" }}
                                                </td>
                                                <!-- <td mat-cell *matCellDef="let row">
                         <a
                            *ngIf="row?.ack; else noack"
                            [href]="row.ack"
                            target="_blank"
                            ></a
                          > 
                          <p
                            *ngIf="row?.ack; else noack"
                            (click)="downloadHelpFile(row.ack, 'image')"
                            style="cursor: pointer"
                          >
                            View Image
                          </p>
                          <ng-template #noack> -- </ng-template>
                        </td>-->
                                            </ng-container>


                                            <ng-container matColumnDef="resource1_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 1 Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource1_date ? row?.resource1_date : "--" }}
                                                </td>
                                            </ng-container>
                                            <ng-container matColumnDef="resource2_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 2 Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource2_date ? row?.resource2_date : "--" }}
                                                </td>
                                            </ng-container>
                                            <ng-container matColumnDef="delayreason_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Delay Reason Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.delayreason_date ? row?.delayreason_date : "--" }}
                                                </td>
                                            </ng-container>
                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons [pageSize]=50
                    (page)="pageChanged($event)"></mat-paginator>
            </div>
        </div>
    </div>
</div>
<app-table-for-pdf [heads]="[
    'Job No',
    'Request Type',
    'Order From',
    'Task Time',
    'Assigned Time',
    'Start Time',
    'Completion Time',
    'Cancel Time',
    'Requestor',
    'Patient',
    'NRIC',
    'From',
    'Bed No',
    'To',
    'Bed No',
    'Return',
    'Bed No',
    'Job Status',
    'Main Category',
    'Sub Category',
    'Transport Mode',
    'Std KPI',
    'Req-Resp(Mins)',
    'Resp-Comp(Mins)',
    'Within KPI',
    'Smart Assigned',
    'Isolation Precaution',
    'Remarks',
    'Resource 1',
    'Resource 2',
    'Creation Time',
    'Created By',
    'Modified By',
    'Modified Date',
    'Assigned By',
    'Delay Reason',
    'Cancel Reason',
    'Cancel Remarks',
    'Cancelled By'
  ]" [title]="'Enhanced Report'" [datas]="pdfData">
</app-table-for-pdf>
<ng-template #ackImageRef>
    <div mat-dialog-content>
        <mat-toolbar class="row mb-2">
            <p class="col-11">Ack image view</p>
            <div class="col-1">
                <span class="material-icons" style="cursor: pointer" (click)="dialogRef.close()">
                    close
                </span>
            </div>
        </mat-toolbar>

        <div>
            <img [src]="mediaUrl.url" alt="ack image" class="img-fluid" />
        </div>
    </div>
</ng-template>