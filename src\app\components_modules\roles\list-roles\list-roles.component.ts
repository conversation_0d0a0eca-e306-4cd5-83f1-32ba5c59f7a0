import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
export interface Roles {
  role_name: string;
  status: boolean;
  role_Id: string;
  user_count: number;

}
@Component({
  selector: 'app-list-roles',
  templateUrl: './list-roles.component.html',
  styleUrls: ['./list-roles.component.scss', '../../../scss/table.scss']
})
export class ListRolesComponent implements OnInit {

  displayedColumns: string[] = ['role_name', 'status', 'user_count', 'role_Id'];
  dataSource: MatTableDataSource<Roles>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly userService: UserService,
    private readonly loader: LoaderService
  ) {
  }

  ngOnInit() {
    this.getRoles();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('rolestable', 'roles_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('rolestable', 'roles_list');
    }
  }

  getRoles() {
    this.userService.getRoles().subscribe(res => {
      res = res && res.filter(role => {
        role.status = role.status ? 'Active' : 'Inactive';
        return role;
      });
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Role Name': key.role_name,
          Status: key.status ? 'Active' : 'Inactive',
          'Active Users': key.user_count,
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }
}

