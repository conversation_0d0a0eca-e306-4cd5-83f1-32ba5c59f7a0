import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthLandingPageRoutingModule, Routing } from './auth-landing-page-routing.module';
import { HomeComponent } from './home/<USER>';
import { SharedModule } from '../shared/shared.module';
import { SharedThemeModule } from '../shared-theme/shared-theme.module';
import { SmartAssignComponent } from '../components_modules/smart-assign/smart-assign.component';
import { DependencyModule } from '../dependency/dependency.module';
import { DashboardComponent } from './dashboard/dashboard.component';
import { ExternalDashboardComponent } from './external-dashaboard/external.dashboard.component';
import { GridViewSettingsComponent } from '../components_modules/grid-view-settings/grid-view-settings.component';


@NgModule({
  declarations: [HomeComponent, SmartAssignComponent, DashboardComponent, ExternalDashboardComponent, GridViewSettingsComponent],
  imports: [
    CommonModule,
    // AuthLandingPageRoutingModule,
    Routing,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class AuthLandingPageModule { }
