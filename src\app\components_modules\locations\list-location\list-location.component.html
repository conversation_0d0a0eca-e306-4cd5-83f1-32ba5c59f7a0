<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right;" class="">
                                <button mat-raised-button color="primary" routerLink="./addlocation">Add
                                    Location </button>
                            </p>
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language"
                                    style="margin: 0;">
                                    Export </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('sxls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li>
                                    <p>View Location
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <div class="row">
                                            <div class="col-3">
                                                <div class="col">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2">
                                                <button class="btn btn-sm  btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="listLocations" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>

                                            <ng-container matColumnDef="location_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Location
                                                    Name
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.location_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="potering">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Portering
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <mat-checkbox disabled *ngIf="!isExcelClicked"
                                                        [checked]="row.potering == 1 ? true : false">
                                                    </mat-checkbox>
                                                    <span *ngIf="isExcelClicked">
                                                        {{row.potering ? 'Yes' : 'No'}}
                                                    </span>
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="location_sap_code">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> SAP Ward
                                                    Code
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.location_sap_code}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="tower_id">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Tower Name
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.tower_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="level_id">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Level Name
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.level_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="contact_no">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Contact
                                                    Number
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.contact_no}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="priority">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Priority
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.priority}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="location_password">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Location
                                                    Pass
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.location_password}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="has_station_porter">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Has Station
                                                    Porter</th>
                                                <td mat-cell *matCellDef="let row"> {{row.has_station_porter}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Status</th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.approval_status}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="location_id">
                                                <th id="" mat-header-cell *matHeaderCellDef> Edit </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        routerLink="./updatelocation/{{row.location_id}}">edit</em>
                                                        <!-- (click)="navigateToUpdate(row)" -->
                                                </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf
    [heads]="['Location Name', 'Portering', 'SAP Ward Code', 'Tower Name', 'Level Name', 'Contact Number', 'Priority', 'Location Pass', 'Has Station Porter', 'Status']"
    [title]="'Locations'" [datas]="pdfData">
</app-table-for-pdf>