import { Component, OnInit, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { TotalWorkload } from '../../../../models/totalWorkload';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import * as moment from 'moment';

@Component({
  selector: 'app-sp-job-type',
  templateUrl: './sp-job-type.component.html',
  styleUrls: ['./sp-job-type.component.scss', '../../../../scss/table.scss']
})
export class SpJobTypeComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['item', 'ytd', 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
  dates;
  pdfData = [];
  subscription: any;
  today = new Date();
  currentYear = this.today.getFullYear();
  years = [this.currentYear, this.currentYear - 1];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');
  dataSource = new MatTableDataSource<TotalWorkload>();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  constructor(
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
  ) {
  }

  ngOnInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.filterDtaes();
  }


  getspJobType(year, month) {
    this.reportsService.getkpiReports('spjobtype', year, month).subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  filterDtaes() {
    this.subscription = this.reportsService.searchDates.subscribe(res => {
      this.dates = res;
      if (res.year && res.month) {
        this.getspJobType(res.year, res.month);
      }
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('stationpool_table', 'stationpool_table');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('stationpool_table', 'stationpool_table');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          '': key.item,
          'Ytd Avg': key.ytd,
          Jan: key.jan,
          Feb: key.feb,
          Mar: key.mar,
          Apr: key.apr,
          May: key.may,
          Jun: key.jun,
          Jul: key.jul,
          Aug: key.aug,
          Sep: key.sep,
          Oct: key.oct,
          Nov: key.nov,
          Dec: key.dec
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
