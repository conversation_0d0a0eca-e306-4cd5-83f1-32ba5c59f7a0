import { ToastrService } from 'ngx-toastr';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { TableUtil } from '../../../utuilities/tablexlsxutility/tableXslxUtility';

@Component({
  selector: 'app-rtls-messages-report',
  templateUrl: './rtls-messages-report.component.html',
  styleUrls: ['./rtls-messages-report.component.scss']
})
export class RtlsMessagesReportComponent implements OnInit {
  routes = [
    { path: "./../messages-report", label: "Messages Report" },
    { path: "./../rtls-messages-reports", label: "RTLS Messages Report" }
  ];

  displayedColumns: string[] = [
    "message",
    "messageDate",
  ];
  dataSource: MatTableDataSource<[]>;
  exportResult: any;
  rtlsMessagesReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  count = 0;
  constructor(
    private _fb: FormBuilder,
    private reportService: ReportsService,
    private toastr: ToastrService
  ) {
    this.createForm();
    this.searchByData();
  }

  createForm() {
    this.rtlsMessagesReportForm = this._fb.group({
      from_date: [null],
      to_date: [null],
    });
  }

  ngOnInit() { }

  searchByData(type?: any, paginationData?: any) {
    const data = {
      "fromDate": this.rtlsMessagesReportForm.value.from_date,
      "toDate": this.rtlsMessagesReportForm.value.to_date,
      // "pageNo": type && type == 'next' ? Number(paginationData.offset) + 1 : 1,
      // "pageSize": (type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xlsx') ? this.count : this.paginator.pageSize) || 50,
    }
    this.reportService.getRTLSMessageReports(data).subscribe(res => {
      this.exportResult = res;
      if (type && type == 'next') {
        this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
        this.exportResult.push(...res);
        this.exportResult.length = res[0].count;
        this.dataSource = new MatTableDataSource<any>(this.exportResult);
        this.dataSource._updateChangeSubscription();
        this.dataSource.paginator = this.paginator;
      } else if (type && (type == 'xlsx' || type == 'csv')) {
        this.mapTableDataforReport(this.exportResult, type);
      } else {
        this.paginator.pageIndex = 0
        // this.exportResult.length = res[0].count;
        // this.count = res[0].count;
        this.paginator.length = res.length;;
        this.dataSource = new MatTableDataSource(this.exportResult || []);
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
      }
    })
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.searchByData('next', paginationData);
  }

  mapTableDataforReport(exportResult, type?) {
    let mappedData;
    console.log(exportResult);
    let exportObj: any[] = [];
    for (var i = 0; i < exportResult.length; i++) {
      mappedData = {
        'Message': exportResult[i].message,
        'Message Date': exportResult[i].messageDate,
      };
      exportObj.push(mappedData);
    }
    if (type == 'csv') {
      TableUtil.exportArrayToCsvDynamically(exportObj, "messagesReport_list");
    } else {
      TableUtil.exportArrayToExcelDynamically(exportObj, "messagesReport_list");
    }
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    this.searchByData(fileType);
  }

  restForm() {
    this.paginator.pageIndex = 0;
    this.paginator.pageSize = 50;
    this.createForm();
    this.searchByData();
  }

  clearFormValue(formField: string) {
    this.rtlsMessagesReportForm.get([formField]).setValue('')
  }

  getDate() {
    if (
      this.rtlsMessagesReportForm.get("from_date").value &&
      this.rtlsMessagesReportForm.get("to_date").value
    ) {
      if (
        this.rtlsMessagesReportForm.get("from_date").value >
        this.rtlsMessagesReportForm.get("to_date").value
      ) {
        this.rtlsMessagesReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be greater then From date", "Error");
      }
    }
  }

}
