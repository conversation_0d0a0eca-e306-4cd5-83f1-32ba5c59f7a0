import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-add-update-medication-codes',
  templateUrl: './add-update-medication-codes.component.html',
  styleUrls: ['./add-update-medication-codes.component.scss']
})
export class AddUpdateMedicationCodesComponent implements OnInit {
  medicationForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) { }

  ngOnInit() {
    this.addMedicationForm();
    this.getMedicationById()
  }


  addMedicationForm() {
    this.medicationForm = this.fb.group({
      MedicationName: ['', Validators.required],
      MedicationCode: ['', Validators.required],
      Status: ['true', Validators.required]
    });
  }

  getMedicationById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getMedicationById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          const [{ MedicationCode, MedicationName, Status }] = res
          this.medicationForm.patchValue({
            MedicationName: MedicationName,
            MedicationCode: MedicationCode,
            Status: Status === true ? 'true' : 'false'
          });
        });
      this.medicationForm.controls['MedicationCode'].disable()
    }
  }
  saveMedication(actiontype) {
    let medicationId: any;
    if (this.medicationForm.valid) {
      const data = this.medicationForm.value;
      data.Status = data.Status === 'true' ? true : false;
      if (actiontype === 'update') {
        medicationId = Number(this.activatedRoute.snapshot.params.id);
      }
      this.facilityConfig.addUpadteTower(
        data, actiontype === 'save' ?
        'api/MedicationCodes/add' : `api/MedicationCodes/edit/${medicationId}`, actiontype)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} Medication`, 'Success');
        }, err => {
          console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.medicationForm.markAllAsTouched();
    }
  }
}


