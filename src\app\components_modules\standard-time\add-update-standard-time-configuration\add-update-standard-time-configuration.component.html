<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Standard Time Configuration</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Standard Time Configuration</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="stdTimeConfigurationForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Standard Time Code <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Standard Time Code"
                                                            formControlName="std_code">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.std_code"
                                                                [fieldName]="'Standard Time Code'"
                                                                [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Start Time(Min) <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput type="number" (keyup)="standardTotalTime()"
                                                            (click)="standardTotalTime()"
                                                            (keydown)="inputValidation.onlyNumbers($event)"
                                                            autocomplete="off" placeholder="Start Time"
                                                            formControlName="start_time">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.start_time"
                                                                [fieldName]="'Start Time'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>End Time(Min) <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput type="number" (keyup)="standardTotalTime()"
                                                            (click)="standardTotalTime()" autocomplete="off"
                                                            (keydown)="inputValidation.onlyNumbers($event)"
                                                            placeholder="End Time" formControlName="end_time">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.end_time"
                                                                [fieldName]="'End Time'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Total Time(Min) <span class="error-css"></span>
                                                        </mat-label>
                                                        <input matInput type="number" placeholder="Total Time"
                                                            formControlName="total_time">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.total_time"
                                                                [fieldName]="'Total Time'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Normal KPI(Min) <span class="error-css">*</span>
                                                        </mat-label>
                                                        <input matInput (keydown)="inputValidation.onlyNumbers($event)"
                                                            autocomplete="off" placeholder="Normal KPI"
                                                            formControlName="normal_kpi">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.normal_kpi"
                                                                [fieldName]="'Normal KPI'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Advance KPI(Min) <span class="error-css">*</span>
                                                        </mat-label>
                                                        <input matInput (keydown)="inputValidation.onlyNumbers($event)"
                                                            autocomplete="off" placeholder="Advance KPI"
                                                            formControlName="advance_kpi">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.advance_kpi"
                                                                [fieldName]="'Advance KPI'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Emergency KPI(Min) <span class="error-css">*</span>
                                                        </mat-label>
                                                        <input matInput (keydown)="inputValidation.onlyNumbers($event)"
                                                            autocomplete="off" placeholder="Emergency KPI"
                                                            formControlName="emergency_kpi">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="stdTimeConfigurationForm.controls.emergency_kpi"
                                                                [fieldName]="'Emergency KPI'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveStdTime('add')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveStdTime('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="stdTimeConfigurationForm.reset(); getstdTimeById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>