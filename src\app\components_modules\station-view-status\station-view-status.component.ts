import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, MatSort, MatTableDataSource } from "@angular/material";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { BaseTimerComponent } from "src/app/shared-theme/base-timer/base-timer.component";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";

@Component({
  selector: "app-station-view-status",
  templateUrl: "./station-view-status.component.html",
  styleUrls: ["./station-view-status.component.scss"],
})
export class StationViewStatusComponent implements OnInit {
  displayedColumns: string[] = [
    "order_no",
    "station_location",
    "staff_name",
    "from_location",
    "to_location",
    "return_to",
    "job_type",
    "request_time",
    "respond_time",
    "completion_time",
    "patient_info",
  ];
  jobImage = {
    "Multiple Job": "assets/icons/multi_way.png",
    "Round Trip": "assets/icons/two-way-arrows.png",
    "One-way Trip": "assets/icons/right-arrow.png",
  };
  dataSource: MatTableDataSource<any>;
  pdfData = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(BaseTimerComponent, { static: true }) baseTimer: BaseTimerComponent;

  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.getStation();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "stationtable",
        "station_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("stationtable", "station_list");
    }
  }

  getStation() {
    this.baseTimer.stopTimerForDataLoadFun(true);
    this.facilityConfig.getStationStatus().subscribe((res) => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
      this.baseTimer.stopTimerForDataLoadFun(false);
    });
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Order No": key.order_no,
              "Station Location": key.station_location,
              "Staff Name": key.staff_name,
              From: key.from_location,
              To: key.to_location,
              "Job Type": key.job_type,
              "Request Time": key.request_time,
              "Respond Time": key.respond_time,
              "Completion Time": key.completion_time,
              "Cancel Time": key.cancel_time,
              "Patient Name/NRIC No.": key.patient_info,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  callThirtySecFun(){
    this.getStation();
  }
}
