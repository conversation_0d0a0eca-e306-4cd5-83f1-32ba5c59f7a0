import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';

@Component({
  selector: 'app-add-update-manage-mobile',
  templateUrl: './add-update-manage-mobile.component.html',
  styleUrls: ['./add-update-manage-mobile.component.scss']
})
export class AddUpdateManageMobileComponent implements OnInit {
  manageMobileForm: FormGroup;
  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService
  ) {
    this.manageMobileForm = this.fb.group({
      mobile_no: ['', Validators.required],
      phone_tag: ['', Validators.required],
    });
  }
  ngOnInit() {
    this.getmobileById();
  }
  getmobileById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getManageMobileById(this.activatedRoute.snapshot.params.id).
      subscribe(res => {
          this.manageMobileForm.patchValue(res);
    });
  }
}

saveMobile(actiontype) {
    let mobileId: any;
    if (this.manageMobileForm.valid) {
      const data = this.manageMobileForm.value;
      if (actiontype === 'update') {
        mobileId = Number(this.activatedRoute.snapshot.params.id);
      }
      this.facilityConfig.addUpdateManageMobile(
        data, actiontype === 'save' ?
        'api/managephones/add' : `api/managephones/edit/${mobileId}`, actiontype)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} mobile no.`, 'Success');
        }, err => {
          console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.manageMobileForm.markAllAsTouched();
    }
  }

}
