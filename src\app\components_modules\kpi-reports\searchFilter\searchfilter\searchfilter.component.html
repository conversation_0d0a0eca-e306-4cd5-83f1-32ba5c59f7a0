    <form [formGroup]="kpiForm">
        <br>
        <fieldset class="scheduler-border">
            <legend class="scheduler-border">Filter Porter Summary Report </legend>
            <div class="row">
                <div class="col-2">
                    <mat-form-field>
                        <mat-select formControlName="month">
                            <mat-option *ngFor="let value of months" [value]="value.id">
                                {{value.val}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="col-2">
                    <mat-form-field>
                        <mat-select formControlName="year">
                            <mat-option *ngFor="let val of years" [value]="val">
                                {{val}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="col-4">
                    <button mat-raised-button type="submit" class="btn btn-primary pull-left" (click)="selectedDateValue()">Search</button>
                    <button mat-raised-button class="btn btn-white pull-left" (click)="resetClicked()">Reset</button>
                </div>
            </div>
        </fieldset>
    </form>