@import "../../../../assets/scss/navbar.scss";
.main-content {
  width: 83%;
  margin: 10px auto;
}
.mat-radio-button ~ .mat-radio-button {
  margin-left: 16px;
}

.col-6 {
  flex: 0 0 100% !important;
}

button {
  cursor: pointer;
}
.alignInput {
  float: left;
  display: inline-flex;
  mat-checkbox {
    padding-right: 20px;
  }
}
mat-checkbox-inner-container {
  height: 20px;
  width: 20px;
}
.nav li {
  width: auto;
}
.nav-tabs-wrapper {
  display: inline-flex;
  em {
    float: left;
    cursor: pointer;
    margin-top: 10px;
  }
}

.example-section {
  display: flex;
  align-content: center;
  align-items: center;
  height: 60px;
}

.example-margin {
  margin: 0 10px;
}

fieldset.scheduler-border-log {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 1em 0 0.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
}

legend.scheduler-border-log {
  font-weight: normal !important;
  color: darkblue;
  font-size: 13px;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}

legend {
  display: block;
}
.card .card-body {
  padding: 2rem 2.25rem;
}
.pt-2 {
  padding-top: 0.5rem !important;
}
.card-body {
  flex: 1 1 auto;
  padding: 1rem 1rem;
}
.mb-8 {
  margin-bottom: 2rem !important;
}
.h-60px {
  height: 70px !important;
}
.fw-bolder {
  font-weight: 600 !important;
}
.fs-8 {
  font-size: 0.85rem !important;
}
.badge {
  display: inline-block;
  padding: 0.5em 0.85em;
  font-size: 0.85rem;
  font-weight: 600;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.475rem;
}
.badge-light-primary {
  color: #6c757d !important;
  background-color: #f1faff;
}
.card.card-xl-stretch {
  height: calc(100% - var(--bs-gutter-y));
}
.card {
  border: 0;
  // box-shadow: 0 0 20px 0 rgb(76 87 125 0 2);
}
.mb-xl-8 {
  margin-bottom: 2rem !important;
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid #eff2f5;
  border-radius: 0.475rem;
  margin-bottom: 0px !important;
  margin-top: 0px !important;
  padding: 0.5em 0 !important;
  // box-shadow: 0 0 20px 0 rgb(76 87 125 0 2);
}
.card-body-log {
  padding: 0px !important;
  // white-space: nowrap !important
}
.date-margin {
  margin: 1em 3em;
}
.badge-align {
  margin: 10px -10px;
}
.logs-header {
  background-color: #ecececb3 !important; // #ececec
  color: #3f51b5 !important;
  font-weight: bold;
  padding: 0.5em;
  margin-left: 0px !important;
  margin-right: 0px !important;
  white-space: nowrap !important;
  // font-size: .875rem !important;
}