<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey p-0">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <div class="float-right d-flex" style="margin: -5px 0">
                <div class="mr-5" style="margin-top: 7px">
                  <mat-form-field>
                    <mat-label>Location</mat-label>
                    <mat-select
                      multiple="true"
                      [(ngModel)]="location_id"
                      [disableOptionCentering]="true"
                      (ngModelChange)="location_id"
                      [(value)]="selected"
                    >
                    <!-- (selectionChange)="getjobRequestsById()" -->
                      <mat-option>
                        <ngx-mat-select-search
                          [formControl]="locationFilterCtrl"
                          [placeholderLabel]="'Find Location...'"
                          [noEntriesFoundLabel]="'no matching location found'"
                        ></ngx-mat-select-search>
                      </mat-option>
                      <mat-option #allSelected (click)="
                                            baseTimer.reset();
                                            baseTimer.start();
                                            toggleAllSelection()
                                            " [value]="'All'">All</mat-option>
                      <mat-option
                        *ngFor="let location of filteredLocations | async"
                        [value]="location.locationId" (click)="
                                            baseTimer.reset();
                                            baseTimer.start();
                                            tosslePerOne(allSelected.viewValue);">
                        {{ location.locationName }}</mat-option
                      >
                    </mat-select>
                  </mat-form-field>
                </div>
                <app-base-timer (CallThirtySecFun)="callThirtySecFun()"></app-base-timer>
                <div class="mt-3 mr-3">
                  <button
                    class="mr-3"
                    mat-raised-button
                    color="primary"
                    (click)="gotoCreateJob()"
                  >   <!-- routerLink="./addjobrequest" -->
                    Make Job Request
                  </button>
                  <!-- <button
                    mat-raised-button
                    color="primary"
                    [matMenuTriggerFor]="sub_menu_language"
                  >
                    Export
                  </button> -->
                </div>
              </div>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('sxls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li style="line-height: 64px; padding-left: 2%">
                  <p>View Job Request</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 mb-3">
                  <ng-container *ngFor="let colorCode of colorCodes">
                    <!-- new style of legends -->
                    <button
                      mat-stroked-button
                      color="warn"
                      [matTooltip]="colorCode.tool_tip"
                      *ngIf="
                        colorCode.condition !== 'Escalated';
                        else showEscalate
                      "
                      matTooltipPosition="above"
                      matTooltipClass="example-tooltip-red"
                      [matBadge]="colorCode.count"
                      matBadgeOverlap="true"
                      (click)="
                        baseTimer.reset();
                        baseTimer.start();
                        actionStatus.value = colorCode.display;
                        actionStatus.color_code = colorCode.color_code;
                        resetData();
                        getjobRequests(
                          colorCode.statusCode,
                          colorCode.condition
                        )
                      "
                      [ngStyle]="{
                        color: colorCode.color,
                        border: '1px solid' + colorCode.color_code,
                        backgroundColor: colorCode?.show_active
                          ? colorCode.color_code
                          : colorCode.background
                      }"
                      (mouseover)="
                        hoverLegendColor(
                          colorCode.color_code,
                          colorCode,
                          '#FFFFFF'
                        )
                      "
                      (mouseout)="
                        hoverLegendColor('#FFFFFF', colorCode, '#000000')
                      "
                      class="mx-4 float-left"
                    >
                      {{ colorCode.display }}
                    </button>

                    <ng-template #showEscalate>
                      <span
                        class="pull-left status-action float-right"
                        [matTooltip]="colorCode.tool_tip"
                        style="margin-left: 33px"
                        matTooltipPosition="above"
                        matTooltipClass="example-tooltip-red"
                      >
                        <div
                          [ngStyle]="{
                            'background-color': colorCode.color_code
                          }"
                          class="tinyBox"
                        ></div>
                        <span>{{ colorCode.display }}</span>
                      </span>
                    </ng-template>
                  </ng-container>

                  <span
                    class="pull-left status-action float-right d-inline-flex"
                    matTooltip="Scheduled Jobs"
                    matTooltipPosition="above"
                  >
                    <div class="material-icons pull-left">alarm</div>
                    <p>Advance Job</p>
                  </span>
                  <span
                    matTooltip="Urgent Jobs"
                    matTooltipPosition="above"
                    class="pull-left status-action d-inline-flex float-right"
                  >
                    <div class="material-icons pull-left material__icon_custom">
                      priority_high
                    </div>
                    <p>Urgent</p>
                  </span>
                  <span
                        class="status-action float-right"
                        [matTooltip]="'Acknowledged'"
                        style="margin-left: 33px"
                        matTooltipPosition="above"
                        matTooltipClass="example-tooltip-red"
                      >
                        <div
                          [ngStyle]="{
                            'background-color': '#1effe7'
                          }"
                          class="tinyBox"
                        ></div>
                        <span>Acknowledged</span>
                      </span>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8">
                    <table
                      id="jobrequesttable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>
                      <!-- <ng-container matColumnDef="color_code">
                                                <th id="" mat-header-cell *matHeaderCellDef>
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <div [ngStyle]="{'background-color':row?.color_code}"
                                                        class="tinyBox">
                                                    </div>
                                                </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="order_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="width: 171px"
                        >
                          Job No
                        </th>
                        <td
                          mat-cell
                          *matCellDef="let row"
                          (mouseenter)="viewJobDetailsHover(row)"
                        >
                          <section style="display: flex">
                            <div class="" style="flex: 1">
                              <!-- equipment type -->
                              <em
                                *ngIf="row?.transport_mode_image?.i_class"
                                class="{{
                                  row?.transport_mode_image?.class
                                }} iconsize"
                              ></em>
                              <img
                                *ngIf="row?.transport_mode_image?.img"
                                [src]="row?.transport_mode_image?.class"
                                style="width: 20px; height: 20px"
                                alt="no image found"
                              />
                              <!-- equipment type -->
                              <div
                                [ngStyle]="{
                                  'background-color': row?.color_code
                                }"
                                [mdePopoverTriggerFor]="jobDetailsPopover"
                                [mdePopoverTriggerOn]="'hover'"
                                class="tinyBox"
                              ></div>
                              <span
                                *ngIf="
                                  row?.request_type.toLowerCase() === 'emergency'
                                "
                              >
                                <div
                                  class="material-icons material__icon_custom"
                                >
                                  priority_high
                                </div>
                              </span>
                              <span
                                *ngIf="
                                  row?.request_type.toLowerCase() === 'advance'
                                "
                              >
                                <div class="material-icons">alarm</div>
                              </span>
                            </div>
                            <a
                              href="javascript:void(0);"
                              style="flex: 1"
                              routerLink="./updatejobrequest/{{ row?.order_id }}"
                              [queryParams]="{ page: 'location' }"
                              >{{ row?.order_no }}</a
                            >
                          </section>
                          <div  *ngIf="row?.job_status === 'Assigned' && row.arrival_time && row.arrival_time !==''"
                                [ngStyle]="{
                                  'background-color': '#1effe7'
                                }"
                                [mdePopoverTriggerFor]="jobDetailsPopover"
                                [mdePopoverTriggerOn]="'hover'"
                                class="tinyBox"
                              >
                          </div>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="request_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Task time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <ng-container
                            *ngIf="
                              row?.request_type.toLowerCase() !== 'advance';
                              else requestTime
                            "
                          >
                            {{
                              row?.request_time
                                ? (row?.request_time
                                  | localDateConversion: "date")
                                : "--"
                            }}
                            <br />
                            {{
                              row?.request_time &&
                                (row?.request_time | localDateConversion: "time")
                            }}
                          </ng-container>
                          <ng-template #requestTime>
                            {{
                              row?.due_date
                                ? (row?.due_date | localDateConversion: "date")
                                : "--"
                            }}
                            <br />
                            {{
                              row?.due_date &&
                                (row?.due_date | localDateConversion: "time")
                            }}
                          </ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="from_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          From
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row?.from_location
                              ? getSliceIndex(row?.from_location,row?.from_location)
                              : "--"
                          }}
                          <br *ngIf="row?.from_location && getSliceIndex(row?.from_location,row?.from_location)?.length>0" />
                          {{
                            row?.from_location
                              ? (row?.from_location
                                | slice: row?.from_location.lastIndexOf("-") + 1)
                              : "--"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="to_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="width: 190px"
                        >
                          To
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row?.to_location
                              ? getSliceIndex(row?.to_location,row?.from_location)
                              : "--"
                          }}
                          <br *ngIf=" row?.to_location && getSliceIndex(row?.to_location,row?.from_location)?.length>0" />

                          {{
                            row?.to_location
                              ? (row?.to_location
                                | slice: row?.from_location.lastIndexOf("-") + 1)
                              : "--"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="task">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Task
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <!-- {{ row?.main_category + " - " }}
                          <br /> -->
                          {{ row?.sub_category }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="assign_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Assigned time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.assign_time | localDateConversion: "time" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="cancellation_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Cancel time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <!-- {{
                            row?.cancellation_time | localDateConversion: "date"
                          }}
                          <br /> -->
                          {{ row?.cancel_time | localDateConversion: "time" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="start_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          {{
                            actionStatus?.value &&
                            actionStatus?.value.includes("Responded")
                              ? "Response time"
                              : "Start time"
                          }}
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <!-- {{ row?.start_time | localDateConversion: "date" }}
                          <br /> -->
                          {{ row?.start_time | localDateConversion: "time" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="completion_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Completion time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <!-- {{
                            row?.completion_time | localDateConversion: "date"
                          }} -->
                          {{ row?.end_time | localDateConversion: "time" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="patient_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Info
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.patient_name }} - {{ row?.nric }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="remarks">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                        Remarks
                        </th>
                        <td mat-cell *matCellDef="let req">
                          {{ req?.remarks }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="ack_by">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                        Ack by
                        </th>
                        <td mat-cell *matCellDef="let req">
                          {{ req?.ack_by }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="porter_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Porter Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <!-- {{ row?.porter_name ? row?.porter_name : "--" }} -->
                          <p
                            data-toggle="modal"
                            data-target="#actionModal"
                            style="cursor: pointer"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Reassign';
                              getStaff();
                              openDialogAction()
                            "
                            *ngIf="
                            actionStatus.value != 'Completed'  &&
                            actionStatus.value != 'Cancelled'
                          "
                          >
                            {{ row?.porter_name ? row?.porter_name : "--" }}
                          </p>
                          <p
                            
                          *ngIf="
                          actionStatus.value === 'Completed'  ||
                          actionStatus.value === 'Cancelled'
                        "
                        >
                          {{ row?.porter_name ? row?.porter_name : "--" }}
                        </p>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="cancel_reason">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Cancel reason
                        </th>
                        <td mat-cell *matCellDef="let req">
                          {{ req?.cancel_reason }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="delay_reason">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Delay reason
                        </th>
                        <td mat-cell *matCellDef="let req">
                          {{ req?.delay_reason }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="order_id">
                        <th id="" mat-header-cell *matHeaderCellDef>Actions</th>
                        <td mat-cell *matCellDef="let row">
                          <!--     style="background: #28A745;" -->
                          <button
                            mat-raised-button
                            class="btn btn-sm btn-default"
                            color="primary"
                            data-target="#actionModal"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Assign';
                              getStaff();
                              createActionForm();
                              openDialogAction();
                              baseTimer.reset()
                            "
                            *ngIf="
                              actionStatus.value === 'In theQueue' ||
                              actionStatus.value === 'INQ'
                            "
                          >
                            <span>Assign</span>
                          </button>

                          <!-- style="background: #343A40;" -->
                          <button
                            mat-raised-button
                            class="btn btn-sm cancel__button"
                            data-target="#actionModal"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Cancel';
                              getReasons('cancel');
                              createActionForm();
                              openDialogAction();
                              baseTimer.reset()
                            "
                            *ngIf="
                              actionStatus.value === 'In theQueue' ||
                              actionStatus.value === 'INQ' ||
                              actionStatus.value === 'Assigned' ||
                              actionStatus.value === 'Responded' ||
                              actionStatus.value === 'Started' ||
                              actionStatus.value === 'Advance'
                            "
                          >
                            <span>Cancel</span>
                          </button>

                          <!--  style="background: #007BFF;" -->
                          <button
                            data-toggle="modal"
                            mat-raised-button
                            class="btn btn-sm btn-default"
                            color="primary"
                            data-target="#actionModal"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Respond';
                              createActionForm();
                              openDialogAction();
                              baseTimer.reset()
                            "
                            *ngIf="actionStatus.value === 'Assigned'"
                          >
                            <span>Respond</span>
                          </button>

                          <!--   style="background: #28A745;" -->
                          <!-- <button data-toggle="modal" mat-raised-button
                                                        class="btn btn-sm btn-default" color="primary"
                                                        data-target="#actionModal"
                                                        (click)="jobNumber.order_id = row.order_id; jobNumber.order_no = row.order_no; jobNumber.actionType = 'Reassign'; getStaff()"
                                                        *ngIf="isButtonList.includes('ReAssign') && actionStatus.value === 'Assigned'">
                                                        <span>Reassign</span>
                                                    </button> -->

                          <!-- style="background: #007BFF;"  -->
                          <button
                            data-toggle="modal"
                            mat-raised-button
                            class="btn btn-sm btn-default"
                            color="primary"
                            data-target="#actionModal"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Delay Reason';
                              getReasons('delay');
                              getDelayReasons(row?.order_no);
                              createActionForm();
                              openDialogAction();
                              baseTimer.reset()
                            "
                            *ngIf="
                              actionStatus.value === 'Assigned' ||
                              actionStatus.value === 'Responded' ||
                              actionStatus.value === 'Started'
                            "
                          >
                            <span>Delay Reason</span>
                          </button>

                          <!--   style="background: #28A745;" -->
                          <button
                            data-toggle="modal"
                            mat-raised-button
                            class="btn btn-sm btn-default"
                            color="primary"
                            data-target="#actionModal"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Complete';
                              createActionForm();
                              openDialogAction();
                              baseTimer.reset()
                            "
                            *ngIf="
                              actionStatus.value === 'Responded' ||
                              actionStatus.value === 'Started'
                            "
                          >
                            <span>Complete</span>
                          </button>

                          <!--  style="background: #FFC107;" -->
                          <button
                            class="btn btn-sm btn-default"
                            mat-raised-button
                            color="primary"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Return Equipment';
                              actionTaken();
                              createActionForm()
                            "
                            *ngIf="
                              row?.job_status === 'Completed' &&
                              !row?.equipment && !row.parentJob
                            "
                          >
                            <span> Return Equipment</span>
                          </button>

                          <!-- style="background: #FFC107;" -->
                          <button
                            class="btn btn-sm btn-default"
                            mat-raised-button
                            color="primary"
                            (click)="
                              jobNumber.order_id = row.order_id;
                              jobNumber.order_no = row.order_no;
                              jobNumber.actionType = 'Fetchback';
                              actionTaken();
                              createActionForm()
                            "
                            *ngIf="
                              row?.job_status === 'Completed' &&
                              !row?.sendback && !row.parentJob &&
                              row?.nric !== '' && row?.nric !== null
                            "
                          >
                            <span>Fetchback</span>
                          </button>
                          <button
                            class="btn btn-sm btn-default"
                            color="primary"
                            mat-raised-button
                            *ngIf="row?.job_status.toLowerCase() === 'cancelled' || row?.job_status.toLowerCase() === 'completed'"
                            (click)="updateJob(row)"
                            data-toggle="modal"
                            data-target="#exampleModal"
                          >
                            Recreate Job
                          </button>
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      >
                        <mat-paginator
                          [pageSizeOptions]="[5, 10, 20]"
                          showFirstLastButtons
                        >
                        </mat-paginator>
                      </tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
          [length]="length"
          (page)="pageChanged($event);"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="[
    'Job No',
    'Creation',
    'From',
    'To',
    'Equipment',
    'Patient Info',
    'Porter Name'
  ]"
  [title]="'Job Requests'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<mde-popover
  #appPopover="mdePopover"
  mdePopoverPositionY="below"
  mdePopoverPositionX="after"
  [mdePopoverOverlapTrigger]="false"
>
  <div class="popover-mde-one"></div>
</mde-popover>

<mde-popover
  #jobDetailsPopover="mdePopover"
  [mdePopoverOverlapTrigger]="false"
  [mdePopoverPositionY]="'above'"
  [mdePopoverPositionX]="'after'"
  [mdeFocusTrapEnabled]="false"
  [mdePopoverOffsetX]="30"
  [mdePopoverOffsetY]="30"
>
  <div class="popover-mde">
    <div class="row">
      <div class="col-md-12 p-1">
        <div
          [ngStyle]="{ 'background-color': hoveredJob?.color_code }"
          class="tinyBox"
        ></div>
        <h5>
          {{ hoveredJob?.RequestType }} 
          <!-- Request -->
          <!-- <small> [Job Number - {{ hoveredJob?.order_no }}]</small> -->
          <small> Job Number - {{ hoveredJob?.order_no }}</small>
        </h5>
      </div>
      <div class="clearfix"></div>
    </div>
    <div class="row">
      <div class="col-12 p-1">
        <div class="row border">
          <!-- <div class="col-2">
            <p>Job Request Details</p>
          </div> -->

          <!-- //////////////// -->
          <div class="col-4">
            <p>
              Booking From:
              <span>{{
                hoveredJob?.from_location ? hoveredJob?.from_location : "--"
              }}</span>
            </p>
          </div>

          <!-- //////////////// -->
          <div class="col-3">
            <p>
              Request Date:
              <span>{{
                hoveredJob?.request_time
                  ? (hoveredJob?.request_time | localDateConversion: "full")
                  : "--"
              }}</span>
            </p>
          </div>

          <!-- //////////////// -->
          <div class="col-3">
            <p>
              Requester Name:
              <span>{{
                hoveredJob?.requester_name ? hoveredJob?.requester_name : "--"
              }}</span>
            </p>
          </div>
          <div class="col-2">
            <p>
              Contact number:
              <span>{{
                hoveredJob?.contact_no ? hoveredJob?.contact_no : "--"
              }}</span>
            </p>
          </div>
        </div>
      </div>
      <div class="col-md-6 popoverbox p-1">
        <fieldset class="scheduler-border">
          <legend class="scheduler-border">Job Request Details</legend>
          <div class="row">
            <span class="col-3 text-aglignment">
              Job Category :</span>
              <span class="col-9 text-aglignment">{{
                hoveredJob?.main_category ? hoveredJob?.main_category : "--"
              }}
            </span>
          </div>
          <div class="row">
            <span class="col-3 text-aglignment">Job Type :</span>
            <span class="col-9 text-aglignment">{{
              hoveredJob?.sub_category ? hoveredJob?.sub_category : "--"
            }}
            </span>
          </div>
          <div class="row">
            <span class="col-3 text-aglignment">From :</span>
            <span class="col-9 text-aglignment">{{
              hoveredJob?.from_location ? hoveredJob?.from_location : "--"
            }} {{
              hoveredJob?.from_room ? ', ' + hoveredJob?.from_room : "--"
            }} {{
              hoveredJob?.from_bed ? ', ' + hoveredJob?.from_bed : "--"
            }}
          </span>
        </div>
        <div class="row">
          <span class="col-3 text-aglignment">To :</span>
          <span class="col-9 text-aglignment">{{
            hoveredJob?.to_location ? hoveredJob?.to_location : "--"
            }} {{
            hoveredJob?.to_room ? ', ' + hoveredJob?.to_room : "--"
            }} {{
              hoveredJob?.from_bed ? ', ' + hoveredJob?.to_bed : "--"
            }}
          </span>
        </div>
        <div class="row">
          <span class="col-3 text-aglignment">Remarks :</span>
          <span class="col-9 text-aglignment">{{
            hoveredJob?.remarks ? hoveredJob?.remarks : "--"
            }}
          </span>
        </div>
        <div class="row">
          <span class="col-3 text-aglignment">{{hoveredJob?.request_type !== 'Advance' ? 'Request Time' :  'Due time'}} :</span>
          <span class="col-9 text-aglignment" *ngIf="hoveredJob?.request_type !== 'Advance'; else showDueTime">
            {{hoveredJob?.request_time ? (hoveredJob?.request_time | localDateConversion: "full") : "--"}}
          </span>
          <ng-template #showDueTime>
            <span class="col-9 text-aglignment">{{
              hoveredJob?.due_date
                ? (hoveredJob?.due_date | localDateConversion: "full")
                : "--"
              }}
          </span>
          </ng-template>
        </div>
        </fieldset>
      </div>
      <!-- <div class="col-md-4 popoverbox">
                <fieldset class="scheduler-border">
                    <legend class="scheduler-border">Job Details</legend>
                    <p>Equipment:
                        <small>{{hoveredJob?.transport_mode_name ? hoveredJob?.transport_mode_name : '--'}}</small></p>

                  
                </fieldset>
            </div> -->
      <div class="col-md-6 popoverbox">
        <fieldset class="scheduler-border">
          <legend class="scheduler-border">Patient Details</legend>
          <div class="row">
            <span class="col-4 text-aglignment">Patient Name :</span>
            <span class="col-8 text-aglignment">{{ hoveredJob?.patient_name ? hoveredJob?.patient_name : "--" }}</span>
          </div>  
          <div class="row">
            <span class="col-4 text-aglignment">IC :</span>
            <span class="col-8 text-aglignment">{{ hoveredJob?.nric ? hoveredJob?.nric : "--" }}</span>
          </div>
          <div class="row">
            <span class="col-4 text-aglignment">Isolation Precaution Required :</span>
            <span class="col-8 text-aglignment">{{ hoveredJob?.isr_value ? hoveredJob?.isr_value : "--" }}</span>
          </div>
          <div class="row">
            <span class="col-4 text-aglignment">Return Equipment :</span>
            <span class="col-8 text-aglignment">{{ hoveredJob?.equipment ? "Yes" : "No" }}</span>
          </div>
          <div class="row">
            <span class="col-4 text-aglignment">Fetch back :</span>
            <span class="col-8 text-aglignment">{{ hoveredJob?.sendback ? "Yes" : "No" }}</span>
          </div>
          <div class="col-12">
            <span class="col-9"> </span>
          </div>
        </fieldset>
      </div>
      <div class="col-md-12 popoverbox">
        <fieldset class="scheduler-border">
          <legend class="scheduler-border">Other Details</legend>
          <div class="row">
            <div class="col-4">
              <div class="row">
                <span class="col-5">Porter Name :</span>
                <span class="col-7">{{
                  hoveredJob?.porter_name ? hoveredJob?.porter_name : "--"
                }}</span>
              </div>
              <div class="row">
                <span class="col-5">Assign Time : </span>
                <span class="col-7">{{
                  hoveredJob?.assign_time
                    ? (hoveredJob?.assign_time | localDateConversion: "full")
                    : "--"
                }}</span>
              </div>
              <div class="row">
                <span class="col-5">Assigned By :</span>
                <span class="col-7">{{
                  hoveredJob?.assigned_by ? hoveredJob?.assigned_by : "--"
                }}</span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="col-5">Respond Time :</span>
                <span class="col-7">{{
                  hoveredJob?.start_time
                    ? (hoveredJob?.start_time | localDateConversion: "full")
                    : "--"
                }}</span>
              </div>

              <div class="row">
                <span class="col-5">Completion Time :</span>
                <span class="col-7">{{
                  hoveredJob?.end_time
                    ? (hoveredJob?.end_time | localDateConversion: "full")
                    : "--"
                }}</span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="col-5">Cancel Time :</span>
                <span class="col-7">{{
                  hoveredJob?.cancel_time
                    ? (hoveredJob?.cancel_time | localDateConversion: "full")
                    : "--"
                }}</span>
              </div>
              <div class="row">
                <span class="col-5">Cancelled By :</span>
                <span class="col-7">{{
                  hoveredJob?.cancelled_by ? hoveredJob?.cancelled_by : "--"
                }}</span>
              </div>
              <div class="row">
                <span class="col-5">Cancellation Reason :</span>
                <span class="col-7">{{
                  hoveredJob?.cancel_reason ? hoveredJob?.cancel_reason : "--"
                }}</span>
              </div>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
</mde-popover>
