import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class InputValidationService {

  constructor() { }

  clearValidators(formGroup: any, controls) {
    if (typeof controls === 'object') {
      controls.map(control => {
        formGroup.get(control).clearValidators();
        formGroup.get(control).updateValueAndValidity();
        formGroup.get(control).markAsTouched();
      });
    } else {
      formGroup.get(controls).clearValidators();
      formGroup.get(controls).updateValueAndValidity();
      formGroup.get(controls).markAsTouched();
    }
  }

  resetControls(formGroup: any, controls) {
    if (typeof controls === 'object') {
      controls.map(control => {
        formGroup.get(control).reset();
      });
    } else {
      formGroup.get(controls).reset();
    }
  }

  setValidators(formGroup: any, controls, validation) {
    formGroup.get(controls).setValidators(validation);
    formGroup.get(controls).updateValueAndValidity();
    // formGroup.get(controls).markAsTouched();
  }



  onlyNumbers(ev) {
    const pattern = /^[0-9]*$/;
    if (ev.key !== 'Backspace' && ev.key !== 'Tab' && ev.code !== 'Backspace' && ev.code !== 'Tab') {
      if (!pattern.test(ev.key) &&
        ((ev.key !== 'Tab') ||
          (ev.code !== 'Tab')) &&
        ((ev.key !== 'Backspace') ||
          (ev.code !== 'Backspace'))) {
        ev.preventDefault();
        return false;
      }
    }
  }

  monthConvert(month) {
    return {
      '01': 'January',
      '02': 'February',
      '03': 'March',
      '04': 'April',
      '05': 'May',
      '06': 'June',
      '07': 'July',
      '08': 'August',
      '09': 'September',
      10: 'October',
      11: 'November',
      12: 'December',
    }[month];
  }

}
