import { TestBed } from '@angular/core/testing';

import { RoutingService } from './routing.service';
import { HttpClient } from '@angular/common/http';
import { JasmineDependencyModule } from 'src/app/jasmine-dependency/jasmine-dependency.module';

describe('RoutingService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [JasmineDependencyModule],
    providers: [HttpClient]
  }));

  it('should be created', () => {
    const service: RoutingService = TestBed.get(RoutingService);
    expect(service).toBeTruthy();
  });
});
