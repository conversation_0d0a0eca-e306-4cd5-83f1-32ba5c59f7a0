import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListLevelsComponent } from './list-levels/list-levels.component';
import { AddUpdateLevelComponent } from './add-update-level/add-update-level.component';


const routes: Routes = [
  {path: '', component: ListLevelsComponent},
  {path: 'addlevel', component: AddUpdateLevelComponent},
  {path: 'updatelevel/:id', component: AddUpdateLevelComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LevelsRoutingModule { }
