import { Injectable } from '@angular/core';
import { HttpService } from '../httpService/http.service';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MasterDataService {

  constructor(
    private readonly httpService: HttpService
  ) { }

  getColorCodes(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/colorcodes`);
  }

  getJobStatus(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/colorcodes/jobstatus`);
  }

  getModeOfTransports(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/transportmodes`);
  }

  delteColoCode(id): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/colorcodes/delete/${id}`);
  }

  getColorCodeById(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/colorcodes/${id}`);
  }

  addUpdateColorCode(data, id) {
    return this.httpService.put(`${environment.base_url}api/colorcodes/edit/${id}`, data);
  }

  getmainJobCategory(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/mjcategories`);
  }

  getMainJobcategoryById(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/mjcategories/${id}`);
  }

  addUpdateMainJobCategory(data, url, action) {
    if (action === 'add') {
      return this.httpService.post(`${environment.base_url}${url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url}${url}`, data);
    }
  }

  addUpadteTransportMode(data, url, action): Observable<any> {
    if (action === 'save') {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  getTransportModeById(id) {
    return this.httpService.get(`${environment.base_url}api/transportmodes/${id}`);
  }

  getSubJobCategory() {
    return this.httpService.get(`${environment.base_url}api/sjcategories`);
  }
  addUpadteSubJobCategory(data, url, action): Observable<any> {
    if (action === 'save') {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }
  getStationTransport() {
    return this.httpService.get(`${environment.base_url}api/stationmappings`);
  }
  getTransportSubList() {
    return this.httpService.get(`${environment.base_url}api/stationmappings/transporttypes`);
  }
  addUpadteStationTransport(data, url): Observable<any> {
    return this.httpService.post(`${environment.base_url + url}`, data);
  }

  getStationTransportById(id) {
    return this.httpService.get(`${environment.base_url}api/stationmappings/${id}`);
  }
  deleteStationTransport(id, id2): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/stationmappings/${id}/delete`);
    // return this.httpService.delete(`${environment.base_url}api/stationmappings/${id}/delete`/${id2});
  }
  getSubjobById(id) {
    return this.httpService.get(`${environment.base_url}api/sjcategories/${id}`);
  }

  deleteMedicationCode(id): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/MedicationCodes/delete/${id}`);
  }

  deleteAllMedicationCodes(id?: any): Observable<any> {
    return this.httpService.delete(`${environment.base_url}api/MedicationCodes/delete`);
  }

  uploadMedicationCodes(allMeds): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/MedicationCodes/upload`,
      allMeds
    );
  }

}
