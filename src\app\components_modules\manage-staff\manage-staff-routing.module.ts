import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListManageStaffComponent } from './list-manage-staff/list-manage-staff.component';
import { AddUpdateManageStaffComponent } from './add-update-manage-staff/add-update-manage-staff.component';
import { PorterLiveStatusComponent } from './porter-live-status/porter-live-status.component';


const routes: Routes = [
  {path: '', component: ListManageStaffComponent},
  {path: 'addmanagestaff', component: AddUpdateManageStaffComponent},
  {path: 'updatemanagestaff/:id', component: AddUpdateManageStaffComponent},
  {path: 'porterlivestatus', component: PorterLiveStatusComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageStaffRoutingModule { }
