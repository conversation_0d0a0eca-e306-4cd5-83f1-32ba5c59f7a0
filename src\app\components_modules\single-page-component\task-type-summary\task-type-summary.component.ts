import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { TransportReport } from "src/app/models/transportationReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import * as moment from "moment";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-task-type-summary",
  templateUrl: "./task-type-summary.component.html",
  styleUrls: ["./task-type-summary.component.scss", "../../../scss/table.scss"],
})
export class TaskTypeSummaryComponent implements OnInit {
  routes = [
    // { path: './../transportReports', label: 'Task Type Summary' },
    // { path: './../locationSearch', label: 'Location Summary' },
    // { path: './../equipmentmove', label: 'Equipment Summary' },
    // { path: './../hourReport', label: 'Hourly Summary' },
    // { path: './../kpiReport', label: 'KPI Report' },
  ];
  displayedColumns: string[] = [
    "TrasportationType",
    // "TotalRequested",
    "TotalComplete",
    "TotalCancelled",
    "TotalJobs",
    "CompletePercentage",
    "CancelledPercentage",
    "Totalpercentage"
  ];
  dataSource: MatTableDataSource<TransportReport>;
  pdfData = [];
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  TransportReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  filter = {
    value: ''
  }
  constructor(
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly reports: ReportsService,
    private readonly toastr: ToastrService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }
  ngOnInit() {
    this.getCurrentDateTime();
  }

  createForm() {
    this.TransportReportForm = this.fb.group({
      from_date: [this.month, Validators.required],
      to_date: [moment().toDate()],
    });
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.searchTransportReport(this.month, this.today);
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "tranportReport",
        "tranportReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("tranportReport", "tranportReport_list");
    }
  }

  searchTransportReport(fromValue, toValue) {
    if (this.TransportReportForm.valid) {
      const fromdate = moment(fromValue).format("YYYY/MM/DD");
      const todate = moment(toValue).format("YYYY/MM/DD");
      this.reports.getTaskTypeReport(fromdate, todate).subscribe((res) => {
        this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.setPageSizeOptions();
      });
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Transportation Type": key.TrasportationType,
              // Urgent: key.Urgent,
              // "Non Urgent": key.PatientMove,
              // //"Non-Patient Move": key.NonPatientMove,
              // "Total Requested": key.TotalRequested,
              "Total Completed": key.TotalComplete,
              "Total Cancelled": key.TotalCancelled,
              "Total Jobs": key.TotalJobs,
              "% Completed": key.CompletePercentage,
              "% Cancelled": key.CancelledPercentage,
              "Total %": key.Totalpercentage
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  getDate() {
    if (
      this.TransportReportForm.get("from_date").value &&
      this.TransportReportForm.get("to_date").value
    ) {
      if (
        this.TransportReportForm.get("from_date").value >=
        this.TransportReportForm.get("to_date").value
      ) {
        this.TransportReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
