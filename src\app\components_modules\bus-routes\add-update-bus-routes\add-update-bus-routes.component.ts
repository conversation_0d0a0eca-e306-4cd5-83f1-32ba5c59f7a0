import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";

@Component({
  selector: "app-add-update-bus-routes",
  templateUrl: "./add-update-bus-routes.component.html",
  styleUrls: ["./add-update-bus-routes.component.scss"],
})
export class AddUpdateBusRoutesComponent implements OnInit {
  busRouteForm: FormGroup;
  routeType = ["Ad-hoc", "Scheduled"];
  locations = [];
  selectedLocations = [];
  towers = [];
  isSelectAll = false;
  staff = [];
  val: any = [];
  startLocationPrevious = "";
  endLocationPrevious = "";
  weeks = [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday",
  ];
  departmentList$: any;
  departmentsIds = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly userService: UserService
  ) {
    this.busRouteForm = this.fb.group({
      route_type: ["", Validators.required],
      route_name: ["", Validators.required],
      is_sequential: false,
      start_location: ["", Validators.required],
      rel_locations: [[], Validators.required],
      end_location: ["", Validators.required],
      start_time: ["", Validators.required],
      working_weekdays: [[]],
      staff: [""],
      remarks: [""],
      active: ["true", Validators.required],
      department_id: ["", Validators.required],
    });
  }

  ngOnInit() {
    this.getLocations();
    this.getStaff();
    this.getDepartmentList();
  }

  adhocClicked() {
    if (!this.activatedRoute.snapshot.params.id) {
      this.busRouteForm.get("working_weekdays").setValue([]);
    }
  }

  getDepartmentList() {
    this.facilityConfig.getUserDepartments().subscribe(res => {
      if (res) {
        this.departmentList$ = res
        res.filter(data => {
        this.departmentsIds.push(data.departmentId)
      })
      this.getBusRouteById();
      }
    })
    
  }

  getBusRouteById() {
    let LocationsArray = [];
    if (this.activatedRoute.snapshot.params.id) {
      this.busRouteForm.get("route_type").disable();
      this.busRouteForm.get("route_name").disable();
      const data = {
        'departments': this.departmentsIds
      }
      this.facilityConfig
        .getBusRouteById(this.activatedRoute.snapshot.params.id, data)
        .subscribe((data) => {
          if (data && data.length > 0) {
            const res =
            (data && data.length && data[0]) ||
            (data && typeof data === "object" ? data : {});
          const weekdays = [];
          Object.keys(res).forEach((key) => {
            this.weeks.forEach((week) => {
              if (key === week && res[key]) {
                weekdays.push(key);
              }
            });
          });
          if (JSON.stringify(weekdays) === JSON.stringify(this.weeks)) {
            // weekdays.push("check");
          }
          // tslint:disable-next-line: no-string-literal
          res["working_weekdays"] = weekdays;
          this.busRouteForm.patchValue(res);
          this.busRouteForm
            .get("active")
            .patchValue(res.active ? "true" : "false");
          const selectedLocations = res.rel_locations || [];
          selectedLocations.forEach((val) => {
            this.locations.forEach((location, i) => {
              if (location.location_id === val.location_id) {
                this.selectedLocations.push(location);
                this.locations.splice(i, 1);
              }
            });
          });
          this.selectLocation();
          }
        });
    }
  }
  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locations = res;
    });
  }
  getStaff() {
    this.userService.getMessageStaffActive().subscribe((res) => {
      // res = res && res.length && res.filter(vals => {
      //   if (vals.status) {
      //     return vals;
      //   }
      // }) || [];
      this.staff = res;
    });
  }
  saveBusRoute(actiontype) {
    let busrouteId: any;
    if (this.busRouteForm.valid && this.selectedLocations.length > 1) {
      const val =
        this.busRouteForm.get("active").value === "true" ? true : false;
      this.busRouteForm.get("active").setValue(val);
      const staffVal =
        this.busRouteForm.get("staff").value === ""
          ? 0
          : this.busRouteForm.get("staff").value;
      this.busRouteForm.get("staff").setValue(staffVal);
      const convertedTime = this.convertTime12to24(
        this.busRouteForm.get("start_time").value
      );
      this.busRouteForm.get("start_time").setValue(convertedTime);
      const data = this.busRouteForm.get("working_weekdays").value || [];
      let x = {};
      x = Object.assign({}, this.busRouteForm.value);
      this.weeks.forEach((week) => {
        x[week] = false;
        data.forEach((selectedDays) => {
          if (week === selectedDays) {
            x[selectedDays] = true;
          }
        });
      });
      if (actiontype === "update") {
        busrouteId = Number(this.activatedRoute.snapshot.params.id);
      }
      this.facilityConfig
        .addBusRoute(
          x,
          actiontype === "save"
            ? "api/busroutes/add"
            : `api/busroutes/scheduled/edit/${busrouteId}`,
          actiontype
        )
        .subscribe(
          (res) => {
            this.location.back();
            this.toastr.success(
              `Successfully ${
                actiontype === "save" ? "added" : "updated"
              } Busroute`,
              "Success"
            );
          },
          (err) => {}
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.busRouteForm.markAllAsTouched();
    }
  }
  convertTime12to24(time12h: string) {
    let timeString;
    if (
      this.activatedRoute.snapshot.params.id &&
      !time12h.endsWith("pm") &&
      !time12h.endsWith("am")
    ) {
      return time12h;
    } else if (time12h.endsWith("pm")) {
      let hour: any = time12h.slice(0, 2);
      hour = hour !== "12" ? Number(hour) + 12 : 12;
      const minutes = time12h.slice(3, 5);
      timeString = String(hour) + ":" + minutes + ":" + "00";
    } else {
      if (time12h.endsWith("am")) {
        let hour: any = time12h.slice(0, 2);
        hour = hour !== "12" ? hour : "00";
        const minutes = time12h.slice(3, 5);
        timeString = String(hour) + ":" + minutes + ":" + "00";
      }
    }
    return timeString;
  }

  selectAll() {
    this.isSelectAll = this.isSelectAll ? false : true;
    if (this.isSelectAll) {
      const weeks = this.weeks.slice();
      // weeks.push("check");
      this.busRouteForm.get("working_weekdays").setValue(weeks);
    } else {
      this.busRouteForm.get("working_weekdays").setValue([]);
    }
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    this.selectLocation();
  }

  selectLocation() {
    const location = [];
    if (this.selectedLocations.length == 0) {
      this.getBusRouteById();
    } else {
      this.selectedLocations.forEach((loc, i) => {
        location.push({ location_id: loc.location_id, order: i });
      });
    }
    this.busRouteForm.get("rel_locations").setValue(location);
    if (location.length > 0) {
      this.busRouteForm
        .get("start_location")
        .patchValue(location[0].location_id);
      this.busRouteForm
        .get("end_location")
        .patchValue(location[location.length - 1].location_id);
    }
    this.startLocationPrevious = this.busRouteForm.get("start_location").value;
    this.endLocationPrevious = this.busRouteForm.get("end_location").value;
  }
}
