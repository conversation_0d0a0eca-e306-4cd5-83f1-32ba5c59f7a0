import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import { FilesDownloadService } from "src/app/Apiservices/filesDownload/files-download.service";
import { MasterDataService } from "src/app/Apiservices/masterData/master-data.service";
import Swal from 'sweetalert2';

@Component({
  selector: "app-add-update-manage-staff",
  templateUrl: "./add-update-manage-staff.component.html",
  styleUrls: ["./add-update-manage-staff.component.scss"],
})
export class AddUpdateManageStaffComponent implements OnInit {
  fileupload: File = null;
  fieldTextType: boolean;
  locations = [];
  selectedLocations = [];
  fileUploaded = false;
  imageUrl: any;
  skillLevels = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
  manageStaffForm: FormGroup;
  fileName = "";
  dateOfJoiningDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "Date Of Joining",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };

  dateOfResignPicker = {
    rangePicker: false,
    collumns: "col-16",
    label: "Date Of Resign",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  subJobList$: any;
  selectedJobType: any = [];
  passwordError: boolean;
  editLogs: any = [];
  isLoginCodeExists: boolean = false;

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    public inputValidation: InputValidationService,
    private readonly fileDownloadservice: FilesDownloadService,
    private masterDataService: MasterDataService
  ) {
    this.manageStaffForm = this.fb.group({
      staff_name: ["", Validators.required],
      skill_level: ["", Validators.required],
      gender: ["male", Validators.required],
      staff_type: ["", Validators.required],
      mobile_no: ["", Validators.required],
      meal_time1: this.fb.group({
        to: [""],
        from: [""],
      }),
      password: [
        "",
        Validators.compose([Validators.required, Validators.minLength(12)]),
      ],
      employee_no: [null, Validators.compose([Validators.required, Validators.pattern('^[0-9]+$')])],
      meal_time2: this.fb.group({
        to: [""],
        from: [""],
      }),
      meal_time3: this.fb.group({
        to: [""],
        from: [""],
      }),
      EnableSA: [true],
      staff_doj: [""],
      staff_dor: [""],
      photo: [""],
      status: ["true"],
      locations: [[], Validators.required],
      sub_categories: [[]],
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getLocations();
    // !this.activatedRoute.snapshot.params.id && this.fetchEmployeeNumber();
    this.getSubJobList();
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locations = res;
      this.getPortersById();
    });
  }

  getDate() {
    if (
      this.manageStaffForm.get("staff_doj").value &&
      this.manageStaffForm.get("staff_dor").value
    ) {
      if (
        this.manageStaffForm.get("staff_doj").value >=
        this.manageStaffForm.get("staff_dor").value
      ) {
        this.manageStaffForm.get("staff_dor").setValue("");
        this.toastr.error(
          "Date of joining should be less then date of resign",
          "Error"
        );
      }
    }
  }

  porterTypeChange(type) {
    if (
      (type && type.toLowerCase() === "central") ||
      (type && type.toLowerCase() == "busroute")
    ) {
      this.manageStaffForm.get("locations").clearValidators();
      this.manageStaffForm.get("locations").updateValueAndValidity();
      this.manageStaffForm.get("locations").setValue([]);

      // remove sub category validation
      this.manageStaffForm.get("sub_categories").setValue([]);
      this.manageStaffForm.get("sub_categories").clearValidators();
      this.manageStaffForm.get("sub_categories").updateValueAndValidity();
      this.selectedLocations = [];
      this.selectedJobType = [];
    } else if (type == "Taskbased") {
      this.manageStaffForm
        .get("sub_categories")
        .setValidators([Validators.required]);
      this.manageStaffForm.get("sub_categories").updateValueAndValidity();

      // remove validation on location
      this.manageStaffForm.get("locations").clearValidators();
      this.manageStaffForm.get("locations").updateValueAndValidity();
      this.manageStaffForm.get("locations").setValue([]);
      this.selectedLocations = [];
    } else {
      this.manageStaffForm
        .get("locations")
        .setValidators([Validators.required]);
      this.manageStaffForm.get("locations").updateValueAndValidity();

      // remove sub category validation
      this.manageStaffForm.get("sub_categories").clearValidators();
      this.manageStaffForm.get("sub_categories").updateValueAndValidity();
      this.manageStaffForm.get("sub_categories").setValue([]);
      this.selectedJobType = [];
    }
  }

  getSubJobList() {
    this.masterDataService.getSubJobCategory().subscribe(
      (data) => (this.subJobList$ = data),
      () => console.error("no job type found")
    );
  }

  timeChanged(group) {
    if (
      this.manageStaffForm.get(group).get("from").value &&
      this.manageStaffForm.get(group).get("to").value
    ) {
      if (
        this.manageStaffForm.get(group).get("from").value ===
        this.manageStaffForm.get(group).get("to").value
      ) {
        this.manageStaffForm
          .get(group)
          .get("to")
          .setErrors({ customError: "Start & End time should not match" });
      }
    }
  }

  getPortersById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.userService
        .getPortersById(this.activatedRoute.snapshot.params.id)
        .subscribe((res: any) => {
          res.meal_time1 = JSON.parse(res.meal_time1);
          res.meal_time2 = JSON.parse(res.meal_time2);
          res.meal_time3 = JSON.parse(res.meal_time3);
          res.status = res.status ? "true" : "false";
          if (res.photo) {
            this.getFileFromserver(res.photo);
            this.fileName = res.photo.slice();
            res.photo = "";
          }
          this.editLogs = res.edit_logs || []
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          console.log(this.editLogs);
          debugger;
          res.employee_no=res.staff_id;
          this.manageStaffForm.patchValue(res);
          if (res.locations.length > 1) {
            const selectedLocations = res.locations || [];
            selectedLocations.forEach((val) => {
              this.locations.forEach((location, i) => {
                if (location.location_id === val.loc_id) {
                  this.selectedLocations.push(location);
                  this.locations.splice(i, 1);
                }
              });
            });
            this.createLocations();
          } else {
            const selectedJobType = res.sub_categories || [];
            selectedJobType.forEach((val) => {
              this.subJobList$.forEach((location, i) => {
                if (location.sj_category_id === val) {
                  this.selectedJobType.push(location);
                  this.subJobList$.splice(i, 1);
                }
              });
            });
            this.createSubJobCategory();
          }
        });
    }
  }

  getFileFromserver(url) {
    this.userService.downloadImages(url).subscribe((res) => {
      const blob = new Blob([res.body], {
        type: "image/png",
      });
      const oFReader = new FileReader();
      oFReader.readAsDataURL(blob);
      oFReader.onload = (oFREvent) => {
        this.imageUrl = oFREvent && oFREvent.target;
      };
    });
  }

  getFile(file) {
    if (file) {
      this.fileupload = file;
      const oFReader = new FileReader();
      oFReader.readAsDataURL(this.fileupload);
      oFReader.onload = (oFREvent) => {
        this.imageUrl = oFREvent && oFREvent.target;
      };
      this.fileUploaded = false;
    } else {
      this.fileupload = file;
      this.imageUrl = null;
      this.fileUploaded = false;
    }
  }

  uploadFile() {
    const data = new FormData();
    data.append("file", this.fileupload);
    this.userService.uploadImges(data).subscribe(
      (res) => {
        if (res.length && res[0].Filename) {
          this.fileName = res[0].Filename;
        }
        this.fileUploaded = true;
        this.toastr.success(`Successfully uploaded photo`, "Success");
      },
      (err) => {
        console.log(err);
      }
    );
  }

  checkPasswordValidation(): boolean {
    let combinationValue = 0; // combination value should satisfied minimum 2
    const { password } = this.manageStaffForm.value;
    const isNumberReg = new RegExp(/.*[0-9].*/gm);
    const isSpecialReg = new RegExp(
      /.*[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~].*/gm
    );
    const isLetterLowerReg = new RegExp(/.*[a-z].*/gm);
    const isLetterUpperReg = new RegExp(/.*[A-Z].*/gm);

    if (isNumberReg.test(password)) {
      combinationValue = combinationValue + 1;
    }
    if (isSpecialReg.test(password)) {
      combinationValue = combinationValue + 1;
    }
    if (isLetterLowerReg.test(password)) {
      combinationValue = combinationValue + 1;
    }
    if (isLetterUpperReg.test(password)) {
      combinationValue = combinationValue + 1;
    }

    this.passwordError = combinationValue >= 2 ? true : false;
    return this.passwordError;
  }
  save(action) {
    if (
      this.checkPasswordValidation() &&
      this.manageStaffForm.valid &&
      (!this.fileupload || this.fileUploaded) &&
      this.imageUrl
    ) {
      if (this.manageStaffForm.value.approval_status == 'Pending') {
        Swal.fire({
          title: 'This request is already under process',
          text: `Are you sure, You want to update?`,
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: 'No',
          confirmButtonText: 'Yes'
        }).then((result) => {
          if (result.value) {
            this.addUpadteRoleUserSTff(action);
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            return;
          }
        });
      } else {
        this.addUpadteRoleUserSTff(action);
      }
    } else {
      if (!this.imageUrl) {
        this.toastr.warning(
          "Please select a proper image",
          "Validation failed!"
        );
      } else {
        this.toastr.warning(
          "Please enter all highlighted fields",
          "Validation failed!"
        );
      }

      !this.passwordError &&
        this.toastr.warning(
          "Password should have at least two combination of (uppercase, lowercase, digits, special characters)"
        );
      this.manageStaffForm.markAllAsTouched();
    }
  }

  addUpadteRoleUserSTff(action) {
    this.manageStaffForm.value.meal_time1 = JSON.stringify(
      this.manageStaffForm.value.meal_time1
    );
    this.manageStaffForm.value.meal_time2 = JSON.stringify(
      this.manageStaffForm.value.meal_time2
    );
    this.manageStaffForm.value.meal_time3 = JSON.stringify(
      this.manageStaffForm.value.meal_time3
    );

    this.manageStaffForm.value.photo = this.fileName;
    const url = this.activatedRoute.snapshot.params.id
      ? `api/managestaff/edit/${this.activatedRoute.snapshot.params.id}`
      : "api/managestaff/add";
    this.userService
      .addUpadteRoleUserSTff(
        {
          ...this.manageStaffForm.value,
          employee_no: this.manageStaffForm.getRawValue().employee_no,
        },
        url,
        action
      )
      .subscribe(
        () => {
          this.toastr.success(
            `Successfully ${action === "save" ? "Created" : "Updated"
            } Porter`,
            "Success"
          );
          this.location.back();
        },
        (err) => {
          console.log(err);
        }
      );
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    this.createLocations();
  }

  createLocations() {
    const location = [];
    this.selectedLocations.forEach((loc) => {
      location.push(loc.location_id);
    });
    this.manageStaffForm.get("locations").setValue(location);
  }

  fetchEmployeeNumber() {
    this.facilityConfig.getLastEmployeeNumber().subscribe((empId) => {
      this.makeLastEmpNumber(empId);
    });
  }

  dropSubCategoryList(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    this.createSubJobCategory();
  }
  createSubJobCategory() {
    const subJob = [];
    this.selectedJobType.forEach((loc) => {
      subJob.push(loc.sj_category_id);
    });
    this.manageStaffForm.get("sub_categories").setValue(subJob);
  }
  makeLastEmpNumber(id: string) {
    const toIncrement = id.split("").filter((data) => !/^[A-Za-z0]/.test(data));
    const incrementedValue = Number(toIncrement);

    const newValue = id.split(toIncrement.join(""))[0];
    const incrementValue = incrementedValue + 1;
    // this.manageStaffForm.controls["employee_no"].patchValue(
    //   `${newValue}${incrementValue}`
    // );
  }

  getMobileNumber(value) {
    const getMobileValue = this.manageStaffForm.controls["mobile_no"].value;
    // this.manageStaffForm.controls["password"].patchValue(getMobileValue);
  }

  toggleFieldTextType() {
    this.fieldTextType = !this.fieldTextType;
  }

  checkLoginCodeExists(loginCode: any) {
    debugger;
    this.isLoginCodeExists = false;
    this.userService.checkLoginCodeExists(loginCode).subscribe((res) => {
      if (res) {
        this.isLoginCodeExists = res;
        this.manageStaffForm.get('employee_no').setErrors({ alreadyExists: true })
      } else {
        this.isLoginCodeExists = res;
        const currentErrors = this.manageStaffForm.get('employee_no').errors;
        if (currentErrors) {
          delete currentErrors['alreadyExists'];
          this.manageStaffForm.get('employee_no').setErrors(currentErrors);
        }
      }
    });
  }

  onBlurCheckPhoneNumber() {
    this.isLoginCodeExists = false;
    const loginCode = this.manageStaffForm.get('employee_no').value;
    if (!loginCode || this.manageStaffForm.get('employee_no').invalid) {
      this.isLoginCodeExists = false;
      return;
    }
    this.checkLoginCodeExists(loginCode);
  }

  allowOnlyNumbers(event: KeyboardEvent): void {
    const charCode = event.key.charCodeAt(0);
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

}
