import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StationDepartmentsRoutingModule } from './station-departments-routing.module';
import { ListStationDepartmentsComponent } from './list-station-departments/list-station-departments.component';
import { AddUpdateStationDepartmentsComponent } from './add-update-station-departments/add-update-station-departments.component';
import { DependencyModule } from '../../dependency/dependency.module';
import { SharedThemeModule } from '../../shared-theme/shared-theme.module';
import { SharedModule } from '../../shared/shared.module';
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";


@NgModule({
  declarations: [ListStationDepartmentsComponent, AddUpdateStationDepartmentsComponent],
  imports: [
    CommonModule,
    StationDepartmentsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    NgxMatSelectSearchModule
  ]
})
export class StationDepartmentsModule { }
