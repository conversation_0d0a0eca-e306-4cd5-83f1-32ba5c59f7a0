import { Component, OnInit } from "@angular/core";
import { TranslateService } from "src/app/Apiservices/translate/translate.service";
import { ActivatedRoute, Router } from "@angular/router";
import { RoutingService } from "src/app/Apiservices/routingService/routing.service";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-menu-layout",
  templateUrl: "./menu-layout.component.html",
  styleUrls: ["./menu-layout.component.scss"],
})
export class MenuLayoutComponent implements OnInit {
  menuItems: any[];

  constructor(
    public translate: TranslateService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly routingService: RoutingService,
    private readonly userService: UserService,
    private readonly router: Router
  ) {}
  ngOnInit() {
    this.createMenu();
  }

  createMenu() {
    this.menuItems = [];
    this.activatedRoute.data.subscribe((res) => {
     
      if (res && res[0] && res[0].length !== 0) {
        res[0].filter((menu, i) => {
          let x = {};
          if (menu.menu !== "Reports") {
            x = {
              title: menu.menu,
              path: "/app/dashboard",
              icon: "",
              class: "",
            };
          } else {
            x = {
              title: menu.menu,
              path: "/app/singlepage",
              icon: "",
              class: "",
            };
          }

          if (
            menu.submenu_list &&
            menu.submenu_list.length !== 0
          ) {
            // if (menu.menu !== "Reports") {
            // tslint:disable-next-line: no-string-literal
            x["subMenu"] = [];
            menu.submenu_list.filter((submenu, j) => {
              const y = {
                title: this.replaceMenuName(submenu.name),
                path: this.routingService.urlAssignToMenu(submenu.name),
                icon: "",
                class: "",
                subMenu: false,
              };
              // tslint:disable-next-line: no-string-literal
              x["subMenu"].push(y);
            });
          }
          this.menuItems.push(x);
          // } else {
          //   // this.menuItems.push({title: "Reports", path: "/app/reports", icon: "", class: ""})
          // }
        });

        const isDahboardAccesible = this.menuItems.find(
          (menu) => menu.title === "Home"
        );

        if (!isDahboardAccesible) {
          if (
            this.menuItems.length &&
            window.location.href.includes("/app/dashboard")
          ) {
            this.router.navigateByUrl(`${this.menuItems[0].subMenu[0].path}`);
          }
        }

        this.userService.menulist = this.menuItems;
        this.userService.menus.next(this.menuItems);
      }
    });
  }

  replaceMenuName(name) {
    return name == "Job Sub Categories"
      ? "Job Type"
      : name == "Mode of Transport"
      ? "Equipment"
      : name == "Location Transport Mapping"
      ? "Location-Job Category mapping"
      : name;
  }
}
