import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-mian-job-category',
  templateUrl: './add-update-mian-job-category.component.html',
  styleUrls: ['./add-update-mian-job-category.component.scss']
})
export class AddUpdateMianJobCategoryComponent implements OnInit {

  roles = [];
  menuList = [];
  mainJobCategoryForm: FormGroup;
  editLogs: any = [];
  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly masterDataService: MasterDataService,
    private readonly userService: UserService
  ) {
    this.mainJobCategoryForm = this.fb.group({
      category_name: ['', [Validators.required]],
      display_in: this.fb.group({
        normal: true,
        advance: true,
        emergency: true
      }),
      short_description: ['', Validators.required],
      role_id_list: [[]],
      sj_other: [true],
      status: ['true'],
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getMainJobcategoryById();
    this.getRoles();
  }
  getRoles() {
    this.userService.getRoles().subscribe(res => {
      res = res && res.length && res.filter(val => {
        if (val.status) {
          return val;
        }
      }) || [];
      this.roles = res;
    });
  }
  getMainJobcategoryById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.masterDataService.getMainJobcategoryById(this.activatedRoute.snapshot.params.id)
        .subscribe(res => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          res.status = res.status ? 'true' : 'false';
          const x = {
            normal: res.display_in && res.display_in.includes('N') ? true : false,
            advance: res.display_in && res.display_in.includes('A') ? true : false,
            emergency: res.display_in && res.display_in.includes('E') ? true : false
          };
          res.display_in = x;
          this.mainJobCategoryForm.patchValue(res);
        });
    }
  }

  mapDisplayIn() {
    const data = this.mainJobCategoryForm.get('display_in').value;
    let displayData = '';
    Object.keys(data).forEach(key => {
      if (data[key]) {
        displayData ? displayData = displayData + key[0] : displayData = key[0];
      }
    });
    this.mainJobCategoryForm.value.display_in = displayData.toUpperCase();
  }
  save(actiontype) {
    if (this.mainJobCategoryForm.valid) {
      this.mapDisplayIn();
      const data = this.mainJobCategoryForm.value;
      data.status = data.status === 'true' ? true : false;
      if (data.sj_other === '' || null) { data.sj_other = false; }
      const finalValues = this.userService.getOnlyFilledObjects(data);
      if (data.approval_status && data.approval_status == 'Pending') {
        Swal.fire({
          title: 'This request is already under process',
          text: `Are you sure, You want to update?`,
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: 'No',
          confirmButtonText: 'Yes'
        }).then((result) => {
          if (result.value) {
            this.updateJobCategory(data, actiontype, finalValues);
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            return;
          }
        });
      } else {
        this.updateJobCategory(data, actiontype, finalValues);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.mainJobCategoryForm.markAllAsTouched();
    }
  }

  updateJobCategory(data, actiontype, finalValues) {
    this.masterDataService.addUpdateMainJobCategory(
      finalValues, actiontype === 'add' ?
      'api/mjcategories/add' : `api/mjcategories/edit/${this.activatedRoute.snapshot.params.id}`, actiontype)
      .subscribe(res => {
        this.location.back();
        this.toastr.success(`Successfully ${actiontype === 'add' ? 'added' : 'updated'} job category`, 'Success');
      }, err => {
        console.log(err);
      });
  }

  reset() {
    this.mainJobCategoryForm.get('status').setValue('true');
    this.mainJobCategoryForm.get('sj_other').setValue(true);
    this.mainJobCategoryForm.get('display_in').get('normal').setValue(true);
    this.mainJobCategoryForm.get('display_in').get('advance').setValue(true);
    this.mainJobCategoryForm.get('display_in').get('emergency').setValue(true);
  }
}

