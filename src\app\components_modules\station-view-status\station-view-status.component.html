<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
            
              <!-- <p style="float: right;" class="mr-3">
                                <button mat-raised-button color="primary" routerLink="./addtower">Add
                                    Tower </button>
                            </p> -->
                        <div class="float-right d-flex" style="gap:10px">
                          <app-base-timer (CallThirtySecFun)="callThirtySecFun()"></app-base-timer>
                          <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                            Export
                          </button>
                        </div>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Station Job View Status</p>
                </li>
              </ul>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <fieldset class="scheduler-border">
                    <legend></legend>
                    <div class="row">
                      <div class="col-3">
                        <div class="col">
                          <mat-form-field>
                            <input
                              matInput
                              placeholder="Search..."
                              #filter
                              (keydown)="applyFilter($event.target.value)"
                            />
                            <mat-icon matSuffix>search</mat-icon>
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="col-2">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="filter.value = ''; applyFilter(filter.value)"
                        >
                          <em class="fa fa-minus-square-o"></em>Reset
                        </button>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div class="col-md-12">
                  <div class="" style="overflow-x: auto">
                    <table
                      id="stationtable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <ng-container matColumnDef="order_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Order No
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <img
                            [src]="jobImage[row.job_type]"
                            [alt]="row.job_type"
                            width="18"
                            height="18"
                            [matTooltip]="row.job_type"
                            style="cursor: pointer; margin-right: 2%"
                          />
                          {{ row.order_no ? row.order_no : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="station_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Station Location
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.station_location ? row.station_location : "-"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="staff_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Staff Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.staff_name ? row.staff_name : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="from_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          From
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.from_location ? row.from_location : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="to_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          To
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.to_location ? row.to_location : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="return_to">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                         Return To
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.return_location ? row.return_location : "-" }}
                        </td>
                      </ng-container> 

                      <ng-container matColumnDef="job_type">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Job Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.job_category ? row.job_category : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="request_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Request Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.request_time
                              ? (row.request_time | localDateConversion: "full")
                              : "-"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="respond_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Respond Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.respond_time
                              ? (row.respond_time | localDateConversion: "full")
                              : "-"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="completion_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Completion Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.completion_time
                              ? (row.completion_time
                                | localDateConversion: "full")
                              : "-"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="patient_info">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Name/NRIC No.
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.patient_name ? row.patient_name : "-" }}
                          /
                          {{ row.nric ? row.nric : "-" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="ack">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Acknowledgement
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.ack }}
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          [pageSize]="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
<app-table-for-pdf
  [heads]="['Tower Name', 'Short Description', 'Status']"
  [title]="'Towers'"
  [datas]="pdfData"
>
</app-table-for-pdf>
