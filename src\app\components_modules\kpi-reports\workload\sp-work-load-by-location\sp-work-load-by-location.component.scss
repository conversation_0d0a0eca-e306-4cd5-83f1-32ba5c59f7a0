th.mat-header-cell, td.mat-cell {
    text-align: center !important;
    border: 1px solid #CCC !important;
    padding: 0 !important;
  
}
::ng-deep .spWorkLoad .mat-sort-header-container {
    padding: 0px 8px 0px 16px;
  }
.spWorkLoad tr:first-child th:first-child{
    border-bottom: none !important;  
}
.spWorkLoad tr:nth-child(2) th:first-child{
    border-top: none !important;
    padding-bottom: 42px !important;
}