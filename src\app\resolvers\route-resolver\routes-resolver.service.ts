import { Injectable } from '@angular/core';
import { Resolve, Router } from '@angular/router';
import { UserService } from 'src/app/Apiservices/userService/user.service';

@Injectable({
  providedIn: 'root'
})
export class RoutesResolverService implements Resolve<any> {

  constructor(
    private readonly userService: UserService,
    private readonly router: Router
  ) { }

  resolve() {
    this.userService.menus.subscribe((res: any) => {
      let submenus = [];
      if (res && res.length !== 0 && ((res[1] && res[1].subMenu) || res[0].path || res[0].subMenu)) {
        res.filter((menu: any) => {
          if (menu.subMenu) {
            menu.subMenu.filter(submneu => {
              submenus.push({ path: submneu.path });
            });
          }
        });
        const isPatchExist = submenus.find((path: any) => window.location.href.includes(path.path) && path.path !== '/app/dashboard');
        if (isPatchExist) {
          submenus = [];
          return true;
        } else {
          this.router.navigateByUrl('/app/dashboard');
        }
      }
    });
  }
}
