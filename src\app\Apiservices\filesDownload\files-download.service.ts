import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class FilesDownloadService {

  constructor() { }

  fileUrlCreate(blob: Blob) {
    return URL.createObjectURL(blob);
  }

  downloadFromBlob(blob: Blob, fileName: string) {
    const url = URL.createObjectURL(blob);
    this.downloadFromUrl(url, fileName);
  }
  downloadFromUrl(signedURL: string, fileName: string) {
    const popupCheck = window.open('');
    if (popupCheck == null || typeof (popupCheck) === 'undefined') {
      alert('Please enable popup to open documents in new tab, Check in right side of browser url bar and allow');
      return true;
    } else {
      popupCheck.close();
      const a = document.createElement('a');
      document.body.appendChild(a);
      a.setAttribute('style', 'display: none');
      a.setAttribute('target', '_blank');
      a.href = signedURL;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(signedURL);
      a.remove();
      return false;
    }
  }

}
