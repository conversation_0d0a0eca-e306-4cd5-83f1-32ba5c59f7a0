import { Injectable } from "@angular/core";
import { HttpService } from "../httpService/http.service";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";
import { HttpHeaders } from "@angular/common/http";

@Injectable({
  providedIn: "root",
})
export class SettingsService {
  constructor(private readonly httpService: HttpService) {}

  getDelayReasons(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/delayreasons`);
  }

  getDelayReason(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/delayreasons/${id}`
    );
  }

  addUpdateDelayReason(data, actionType, id) {
    if (actionType === "add") {
      return this.httpService.post(
        `${environment.base_url}api/delayreasons/add`,
        data
      );
    } else {
      return this.httpService.put(
        `${environment.base_url}api/delayreasons/edit/${id}`,
        data
      );
    }
  }

  getCanceltemplates(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/canceltemplates`);
  }

  getCanceltemplate(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/canceltemplates/${id}`
    );
  }

  addUpdateCanceltemplate(data, actionType, id) {
    if (actionType === "add") {
      return this.httpService.post(
        `${environment.base_url}api/canceltemplates/add`,
        data
      );
    } else {
      return this.httpService.put(
        `${environment.base_url}api/canceltemplates/edit/${id}`,
        data
      );
    }
  }

  getMessageTemplates(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/messagetemplates`);
  }

  delteMessageTemplate(id): Observable<any> {
    return this.httpService.delete(
      `${environment.base_url}api/messagetemplates/delete/${id}`
    );
  }

  getMessageTemplate(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/messagetemplates/${id}`
    );
  }

  addUpdateMessageTemplate(data, actionType, id?) {
    if (actionType === "add") {
      return this.httpService.post(
        `${environment.base_url}api/messagetemplates/add`,
        data
      );
    } else {
      return this.httpService.put(
        `${environment.base_url}api/messagetemplates/edit/${id}`,
        data
      );
    }
  }

  addUpdateMangeHelpFile({ data, topic }): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/uploadfiles?topic=${topic}`,
      data
    );
  }

  getManageFiles(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/managehelpfiles`);
  }

  getManageFileByid(id): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/managefile/${id}`);
  }

  delteHelpFile(id): Observable<any> {
    return this.httpService.delete(
      `${environment.base_url}api/managehelpfiles/delete/${id}`
    );
  }

  downloadHelpFile(id): Observable<any> {
    return this.httpService.getFiles(
      `${environment.base_url}api/download/files?filename=${id}`
    );
  }

  downloadHelpFileTraining(id): Observable<any> {
    return this.httpService.getFiles(
      `${environment.base_url}api/download/files?filename=${id}`
    );
  }
  downloadAckReportFile(id): Observable<any> {
    return this.httpService.getFiles(
      `${environment.base_url}api/download/ackfiles?filename=${id}`
    );
  }

  getFeedbackEmail(): Observable<any> {
    return this.httpService.get(`${environment.base_url}api/feedback/email`);
  }
  updateFeedbackEmail(data): Observable<any> {
    return this.httpService.put(
      `${environment.base_url}api/feedback/email/edit`,
      data
    );
  }
}
