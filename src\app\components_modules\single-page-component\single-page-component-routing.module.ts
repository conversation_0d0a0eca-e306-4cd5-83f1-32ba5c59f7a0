import { RtlsMessagesReportComponent } from './rtls-messages-report/rtls-messages-report.component';
import { MessagesReportComponent } from './messages-report/messages-report.component';
import { EnhancedLocationReportComponent } from './enhanced-location-report/enhanced-location-report.component';
import { PtsReportsComponent } from './pts-reports/pts-reports.component';
import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ListAdtPatientlogComponent } from "./list-adt-patientlog/list-adt-patientlog.component";
import { UpdateFeedbackEmailComponent } from "./update-feedback-email/update-feedback-email.component";
import { ListOngoingBusrouteComponent } from "./list-ongoing-busroute/list-ongoing-busroute.component";
import { ListEnhancedReportComponent } from "./list-enhanced-report/list-enhanced-report.component";
import { ListViewFeedbackComponent } from "./list-view-feedback/list-view-feedback.component";
import { PorterSummaryReportComponent } from "./porter-summary-report/porter-summary-report.component";
import { BusRouteSearchComponent } from "./bus-route-search/bus-route-search.component";
import { EquipmentMoveReportComponent } from "./equipment-move-report/equipment-move-report.component";
import { TaskTypeSummaryComponent } from "./task-type-summary/task-type-summary.component";
import { LocationReportComponent } from "./location-report/location-report.component";
import { HourlySummaryReportComponent } from "./hourly-summary-report/hourly-summary-report.component";
import { KpiSummaryReportComponent } from "./kpi-summary-report/kpi-summary-report.component";
import { ListStationPorterComponent } from "./station-porter-report/list-station-porter/list-station-porter.component";
import { AdSettingsComponent } from "./ad-settings/ad-settings.component";
// tslint:disable-next-line: max-line-length
import { ListStationSpecificReportComponent } from "./station-specific-report/list-station-specific-report/list-station-specific-report.component";
import { SinglePageComponent } from "./single-page.component";
import { UamReportComponent } from "./uam-report/uam-report.component";
import { RoleComponent } from "./role/role.component";
import { UsersReportComponent } from "./users-report/users-report.component";
import { DisabledUsersComponent } from "./disabled-users/disabled-users.component";
import { ActiveUsersComponent } from "./active-users/active-users.component";
import { MonthlyrawReportComponent } from "./monthlyraw-report/monthlyraw-report.component";

const routes: Routes = [
  {
    path: "",
    component: SinglePageComponent,
    children: [
      {
        path: "adtpatientlogs",
        component: ListAdtPatientlogComponent,
        data: { name: "ADT Patient Logs" },
      },
      {
        path: "enhancedReports",
        component: ListEnhancedReportComponent,
        data: { name: "Enhanced Reports" },
      },
      {
        path: "feedbackList",
        component: ListViewFeedbackComponent,
        data: { name: "Feedback" },
      },
      {
        path: "porterReports",
        component: PorterSummaryReportComponent,
        data: { name: "Enhanced Reports" },
      },
      {
        path: "busrouteSearch",
        component: BusRouteSearchComponent,
        data: { name: "Enhanced Reports" },
      },
      {
        path: "pts-reports",
        component: PtsReportsComponent,
        data: { name: "Enhanced Reports" },
      },
      {
        path: "enhanced-location-reports",
        component: EnhancedLocationReportComponent,
        data: { name: "Location Reports" },
      },

      // uam reports
      {
        path: "uam-report",
        component: UamReportComponent,
        data: {
          name: "UAM reports",
        },
      },
      {
        path: "role-report",
        component: RoleComponent,
        data: {
          name: "UAM reports",
        },
      },
      {
        path: "user-report",
        component: UsersReportComponent,
        data: {
          name: "UAM reports",
        },
      },
      {
        path: "disable-user-report",
        component: DisabledUsersComponent,
        data: {
          name: "UAM reports",
        },
      },
      {
        path: "active-user-report",
        component: ActiveUsersComponent,
        data: {
          name: "UAM reports",
        },
      },

      // uam reports
      {
        path: "",
        redirectTo: "adtpatientlogs",
        pathMatch: "full",
      },

      // enhance search
      {
        path: "busrouteSearch",
        component: BusRouteSearchComponent,
        data: { name: "Enhanced Reports" },
      },

      {
        path: "porterReports",
        component: PorterSummaryReportComponent,
        data: { name: "Enhanced Reports" },
      },

      // summary reports
      {
        path: "stationSpecificReport",
        component: ListStationSpecificReportComponent,
        data: { name: "Station Reports" },
      },
      {
        path: "stationReport",
        component: ListStationPorterComponent,
        data: { name: "Station Reports" },
      },

      {
        path: "transport-reports",
        component: TaskTypeSummaryComponent,
        data: { name: "Summary Reports" },
      },
      {
        path: "location-summary",
        component: LocationReportComponent,
        data: { name: "Summary Reports" },
      },
      {
        path: "equipment-move",
        component: EquipmentMoveReportComponent,
        data: { name: "Summary Reports" },
      },
      {
        path: "hourly-summary",
        component: HourlySummaryReportComponent,
        data: { name: "Summary Reports" },
      },
      {
        path: "kpi-report",
        component: KpiSummaryReportComponent,
        data: { name: "Summary Reports" },
      },
      // audit report
      {
        path: "audit-report",
        loadChildren: () =>
          import("./audit-report/audit-report.module").then(
            (m) => m.AuditReportModule
          ),
        data: { name: "audit-report" },
      },
      {
        path: "monthlyraw-report",
        component: MonthlyrawReportComponent,
        data: { name: "Monthly Reports" },
      },
      {
        path: "messages-reports",
        component: MessagesReportComponent,
        data: { name: "Message Reports" },
      },
      {
        path: "rtls-messages-reports",
        component: RtlsMessagesReportComponent,
        data: { name: "Message Reports" },
      },
    ],
  },
  { path: "ongoingbus", component: ListOngoingBusrouteComponent },
  { path: "ads", component: AdSettingsComponent },
  { path: "feedbackEmail", component: UpdateFeedbackEmailComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SinglePageComponentRoutingModule {}
