import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../Apiservices/userService/user.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-add-manage-departmets',
  templateUrl: './add-manage-departmets.component.html',
  styleUrls: ['./add-manage-departmets.component.scss']
})
export class AddManageDepartmetsComponent implements OnInit {
  departmentForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    public location: Location
  ) { }

  ngOnInit() {
    this.addDepartmentForm();
    if (this.activatedRoute.snapshot.queryParams.department_id) {
      this.departmentForm.patchValue(this.activatedRoute.snapshot.queryParams);
    }
  }

  updateDepartment(id?) {
    this.userService.updateDepartmentById(id, this.departmentForm.value).subscribe(res => {
      this.location.back();
      this.toastr.success(`Successfully updated Department`, 'Success');
    })
  }

  addDepartmentForm() {
    this.departmentForm = this.fb.group({
      department_id: [''],
      department_name: ['', Validators.required],
      status: ['true', Validators.required]
    });
  }

  saveDepartment(actiontype?) {
    let Id: any;
    if (this.departmentForm.valid) {
      const data = this.departmentForm.value;
      data.status = data.status === 'true' ? true : false;
      if (actiontype === 'update') {
        Id = Number(this.activatedRoute.snapshot.queryParams.department_id);
        this.updateDepartment(Id);
      } else {
        this.userService.addUpdateDepartment(this.departmentForm.value)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} Department`, 'Success');
        }, err => {
          console.log(err);
        });
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.departmentForm.markAllAsTouched();
    }
  }

}
