import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ViewStatusByLocationComponent } from './view-status-by-location.component';

describe('ViewStatusByLocationComponent', () => {
  let component: ViewStatusByLocationComponent;
  let fixture: ComponentFixture<ViewStatusByLocationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ViewStatusByLocationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewStatusByLocationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
