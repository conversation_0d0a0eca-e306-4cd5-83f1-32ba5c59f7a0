import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListUsersComponent } from './list-users/list-users.component';
import { AddUpdateUserComponent } from './add-update-user/add-update-user.component';


const routes: Routes = [
  {path: '', component: ListUsersComponent},
  {path: 'adduser', component: AddUpdateUserComponent},
  {path: 'updateuser/:id', component: AddUpdateUserComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserAdministrativeRoutingModule { }
