import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SmartAssignComponent } from './smart-assign.component';

describe('SmartAssignComponent', () => {
  let component: SmartAssignComponent;
  let fixture: ComponentFixture<SmartAssignComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SmartAssignComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SmartAssignComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
