<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p class="float-right">
                <button
                  mat-raised-button
                  color="primary"
                  href="javascript:void(0);"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('sxls')"
                    >
                      xsls
                    </p>
                  </a>

                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Search Jobs</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12" style="margin-bottom: 5px">
                  <fieldset class="scheduler-border">
                    <legend class="scheduler-border">Filter Jobs</legend>
                    <div class="row">
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label>NRIC / Patient Name</mat-label>
                          <input
                            matInput
                            type="text"
                            [(ngModel)]="taskRequest.patient"
                            #userName="ngModel"
                          />
                        </mat-form-field>
                      </div>

                      <div class="col-3" style="margin-top: -4px">
                        <mat-form-field>
                          <span
                            matSuffix
                            style="cursor: pointer"
                            (click)="clearFormValue('location_id')"
                          >
                            <mat-icon>clear</mat-icon>
                          </span>
                          <mat-label>Location</mat-label>
                          <mat-select
                            #singleSelect
                            [(ngModel)]="taskRequest.location_id"
                            [disableOptionCentering]="true"
                          >
                            <mat-option>
                              <ngx-mat-select-search
                                [formControl]="locationFilterCtrl"
                                [placeholderLabel]="'Find Location...'"
                                [noEntriesFoundLabel]="
                                  'no matching location found'
                                "
                              ></ngx-mat-select-search>
                            </mat-option>

                            <mat-option
                              *ngFor="let location of filteredLocations | async"
                              [value]="location.location_id"
                            >
                              {{ location.location_name }}</mat-option
                            >
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <span
                            matSuffix
                            style="cursor: pointer"
                            (click)="clearFormValue('date')"
                          >
                            <mat-icon>clear</mat-icon>
                          </span>
                          <mat-label>Choose a date</mat-label>
                          <input
                            matInput
                            [matDatepicker]="picker"
                            [(ngModel)]="taskRequest.date"
                          />
                          <mat-datepicker-toggle matSuffix [for]="picker">
                          </mat-datepicker-toggle>
                          <mat-datepicker #picker></mat-datepicker>
                        </mat-form-field>
                      </div>
                      <div class="col-3">
                        <button
                          mat-raised-button
                          type="submit"
                          class="btn btn-primary pull-left"
                          (click)="getjobRequests()"
                        >
                          Search
                        </button>
                        <!-- <button mat-raised-button type="button" class="btn btn-primary "
                                                    style="width: auto;"
                                                    routerLink="/app/jobrequests/addjobrequest">Create New Job</button> -->
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8">
                    <table
                      id="jobrequesttable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <!-- <ng-container matColumnDef="color_code">
                                                <th id="" mat-header-cell *matHeaderCellDef>
                                                    Color Code
                                                </th>
                                                <td mat-cell *matCellDef="let row">

                                                </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="order_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Job No
                        </th>

                        <td mat-cell *matCellDef="let row">
                          <div style="display: flex">
                            <div
                              [ngStyle]="{ 'background-color': row.color_code }"
                              class="tinyBox"
                            ></div>
                            <p style="flex: 2">{{ row.order_no }}</p>
                          </div>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="request_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Creation
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.request_time
                              ? (row.request_time | localDateConversion: "full")
                              : "--"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="from_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          From
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.from_location ? row.from_location : "--" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="to_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          To
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.to_location ? row.to_location : "--" }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="patient_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Info
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.patient_name }} -
                          <br />
                          {{ row.nric }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="porter_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Porter Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <p
                            data-toggle="modal"
                            data-target="#actionModal"
                            style="cursor: pointer"
                            matTooltip="Reassign Job"
                            aria-label="tooltip that display the assign reassign value"
                            matTooltipPosition="above"
                          >
                            {{ row.porter_name ? row.porter_name : "--" }}
                          </p>
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="transport_mode_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Equipment
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.transport_mode_name }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="task">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Task
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.main_category + " - " }}
                          <br />
                          {{ row.sub_category }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="request_type">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Urgent
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.request_type === "emergency" ? "Yes" : "No" }}
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      >
                        <mat-paginator
                          [pageSizeOptions]="[5, 10, 20]"
                          showFirstLastButtons
                        >
                        </mat-paginator>
                      </tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="['Job No', 'Creation', 'From', 'To', 'Equipment', 'Task', 'Urgent']"
  [title]="'Job Requests'"
  [datas]="pdfData"
>
</app-table-for-pdf>
