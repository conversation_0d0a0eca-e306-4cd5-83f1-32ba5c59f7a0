import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { MnageFileData } from "src/app/models/manageFile";
import Swal from "sweetalert2";
import { ToastrService } from "ngx-toastr";
import { SettingsService } from "src/app/Apiservices/settings/settings.service";
import { FilesDownloadService } from "src/app/Apiservices/filesDownload/files-download.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";

@Component({
  selector: "app-list-mangge-help-file",
  templateUrl: "./list-mangge-help-file.component.html",
  styleUrls: [
    "./list-mangge-help-file.component.scss",
    "../../../scss/table.scss",
  ],
})
export class ListManggeHelpFileComponent implements OnInit {
  pdfData = [];
  displayedColumns: string[] = [
    "filename",
    "download_id",
    "media",
    "delete_id",
  ];
  dataSource: MatTableDataSource<MnageFileData>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild("downloadZipLink", { static: true }) downloadZipLink: ElementRef;
  mediaUrl: { url: any; type: any };

  constructor(
    private readonly toaster: ToastrService,
    private readonly settingService: SettingsService,
    private readonly filesDownloadService: FilesDownloadService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.getManageFile();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "managehelptable",
        "managehelptable_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("managehelptable", "managehelptable_list");
    }
  }

  getManageFile() {
    this.settingService.getManageFiles().subscribe((res) => {
      this.dataSource = new MatTableDataSource(res || []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  downloadHelpFile(id, method) {
    this.mediaUrl = { type: "image", url: "" };
    this.settingService.downloadHelpFileTraining(id).subscribe(
      (res) => {
        if ((res && method == "pdf") || (res && method == "ppt")) {
          if (this.checkBrowserInstance()) {
            const url = window.navigator.msSaveOrOpenBlob(res.body);
          } else {
            var fileURL = URL.createObjectURL(res.body);
            window.open(fileURL, "_blank");
          }
        } else if (method == "video" || method == "image") {
          this.mediaUrl = { url: res.url, type: res.body["type"] };
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  checkBrowserInstance() {
    if (
      !!navigator.userAgent.match(/Trident.*rv\:11\./) ||
      !!document["documentMode"]
    ) {
      return true;
    } else {
      return false;
    }
  }

  showMediaFile(value: Array<any>, extension) {
    debugger;
    let regEx = new RegExp("");
    if (extension == "pdf") {
      regEx = new RegExp("(.*?).(pdf|ppt|pptx)$");
      return value.find((data) => regEx.test(data.filename));
    } else if (extension == "media") {
      regEx = new RegExp("(.*?).(jpeg|jpg|mp4)$");
      return value.find((data) => regEx.test(data.filename));
    }
  }
  isVideoOrImage(value) {
    let regEx = new RegExp("");
    regEx = new RegExp("(.*?).(jpeg|jpg|mp4)$");
    const videoFile = value.find((data) => regEx.test(data.filename));
    return videoFile.filename.includes("jpeg")
      ? "image"
      : videoFile.filename.includes("jpeg")
      ? "image"
      : "video";
  }

  deleteHelpFile(id) {
    Swal.fire({
      title: "Are you sure?",
      text: "You want to delete!",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No, keep it",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.value) {
        this.settingService.delteHelpFile(id).subscribe(
          (res) => {
            this.toaster.success("Successfully deleted help file", "Success");
            this.getManageFile();
          },
          (err) => {
            console.log(err);
          }
        );
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "File Name": key.filename,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
  checkIfFileMedia(media, method) {
    return media.reduce(
      (filename, data) => (data.filename.indexOf(method) ? data.filename : ""),
      ""
    );
  }
}
