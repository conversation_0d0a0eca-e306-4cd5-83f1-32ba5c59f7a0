import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListMapPortersComponent } from './list-map-porters/list-map-porters.component';
import { AddUpdateAssignPorterComponent } from './add-update-assign-porter/add-update-assign-porter.component';


const routes: Routes = [
  {path: '', component: ListMapPortersComponent},
  {path: 'addmapporter', component: AddUpdateAssignPorterComponent},
  {path: 'updatemapporter/:id', component: AddUpdateAssignPorterComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MapPortersRoutingModule { }
