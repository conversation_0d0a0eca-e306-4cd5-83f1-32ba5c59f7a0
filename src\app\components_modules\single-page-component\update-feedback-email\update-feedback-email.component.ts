import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import { regex } from 'src/app/regex/regex';

@Component({
  selector: 'app-update-feedback-email',
  templateUrl: './update-feedback-email.component.html',
  styleUrls: ['./update-feedback-email.component.scss']
})
export class UpdateFeedbackEmailComponent implements OnInit {
  feedbackForm: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly settingService: SettingsService
  ) {
    this.feedbackForm = this.fb.group({
      // tslint:disable-next-line: max-line-length
      email: ['', Validators.compose([Validators.required, Validators.pattern(regex.email)])],
    });
  }

  ngOnInit() {
    this.getFeedbackEmail();
  }

  getFeedbackEmail() {
    this.settingService.getFeedbackEmail().subscribe(res => {
      this.feedbackForm.patchValue(res);
    });

  }

  saveFeedbackEmail() {
    if (this.feedbackForm.valid) {
      this.settingService.updateFeedbackEmail(this.feedbackForm.value).subscribe(() => {
        this.toastr.success(`Successfully updated feedBack email`, 'Success');
      }, err => {
        console.log(err);
      });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.feedbackForm.markAllAsTouched();
    }
  }

}
