import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListManageMobileComponent } from './list-manage-mobile/list-manage-mobile.component';
import { AddUpdateManageMobileComponent } from './add-update-manage-mobile/add-update-manage-mobile.component';


const routes: Routes = [
  {path: '', component: ListManageMobileComponent},
  {path: 'addmanagemobile', component: AddUpdateManageMobileComponent},
  {path: 'updatemanagemobile/:id', component: AddUpdateManageMobileComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageMobileRoutingModule { }
