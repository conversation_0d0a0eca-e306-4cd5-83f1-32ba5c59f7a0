import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { ViewStatusByLocationRoutingModule } from "./view-status-by-location-routing.module";
import { ViewStatusByLocationComponent } from "./view-status-by-location.component";
import { ReactiveFormsModule } from "@angular/forms";
import { SharedModule } from "src/app/shared/shared.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { JobRequestsModule } from "../job-requests/job-requests.module";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";

@NgModule({
  declarations: [ViewStatusByLocationComponent],
  imports: [
    CommonModule,
    ViewStatusByLocationRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    JobRequestsModule,
    NgxMatSelectSearchModule,
    SharedThemeModule
  ],
})
export class ViewStatusByLocationModule {}
