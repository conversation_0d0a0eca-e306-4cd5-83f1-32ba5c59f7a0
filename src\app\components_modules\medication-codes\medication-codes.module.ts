import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MedicationCodesRoutingModule } from './medication-codes-routing.module';
import { MedicationCodesComponent } from './medication-codes.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateMedicationCodesComponent } from './add-update-medication-codes/add-update-medication-codes.component';
import { ReactiveFormsModule } from '@angular/forms';


@NgModule({
  declarations: [MedicationCodesComponent, AddUpdateMedicationCodesComponent],
  imports: [
    CommonModule,
    MedicationCodesRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class MedicationCodesModule { }
