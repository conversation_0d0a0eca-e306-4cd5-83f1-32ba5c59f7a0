import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListSubjobcategoryComponent } from './list-subjobcategory/list-subjobcategory.component';
import { AddUpdateSubjobcategoryComponent } from './add-update-subjobcategory/add-update-subjobcategory.component';

const routes: Routes = [
  {path: '', component: ListSubjobcategoryComponent},
  {path: 'addsubjobcategory', component: AddUpdateSubjobcategoryComponent},
  {path: 'updatesubjobcategory/:id', component: AddUpdateSubjobcategoryComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SubJobcategoryRoutingModule { }
