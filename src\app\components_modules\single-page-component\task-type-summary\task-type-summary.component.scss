@import "../../../../assets/scss/navbar.scss";
td,
th {
  width: 10%;
}
fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000 !important;
  height: 158px;
}

legend.scheduler-border {
  font-size: 1em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}
legend {
  display: block;
}
.btn-primary {
  margin-right: 10px;
}
.spacing {
  margin-bottom: 12px;
}

.mat-column-TotalJobs {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-TrasportationType {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 20% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-Totalpercentage {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

.mat-column-TotalRequested {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-TotalComplete {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-TotalCancelled {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-CompletePercentage {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.mat-column-CancelledPercentage {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 15% !important;
  width: 15% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}