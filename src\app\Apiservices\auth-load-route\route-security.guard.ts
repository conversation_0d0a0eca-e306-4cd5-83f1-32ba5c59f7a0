import { Injectable } from '@angular/core';
import { CanLoad, Route, UrlSegment, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserService } from '../userService/user.service';
import { map, first } from 'rxjs/operators';
import { RoutingService } from '../routingService/routing.service';

@Injectable({
  providedIn: 'root'
})
export class RouteSecurityGuard implements CanLoad {
  constructor(
    private readonly userService: UserService,
    private readonly router: Router,
    private readonly routingService: RoutingService
  ) { }
  canLoad(
    route: Route,
    segments: UrlSegment[]): Observable<boolean> | Promise<boolean> | boolean {
    if (this.userService.menulist) {
      const res: any = this.userService.menulist;
      let submenus = [];
      if (res && res.length !== 0 && ((res[1] && res[1].subMenu) || res[0].path || res[0].subMenu)) {
        res.filter((menu: any) => {
          if (menu.subMenu) {
            menu.subMenu.filter(submneu => {
              if(submneu.path){
                submenus.push({ path: submneu.path });
              }
            });
          }
        });
        const isPatchExist = submenus.find((path: any) => path.path.includes(route.data.data));
        if (isPatchExist) {
          submenus = [];
          return true;
        } else {
          this.router.navigateByUrl('/');
        }
      }
    } else {
      return this.userService.menu().pipe(map((auth) => {
        const menuItems: any = [];
        if (auth && auth[0] && auth[0].length !== 0) {
          auth.filter((menu, i) => {
            const x = {
              title: menu.menu,
              path: '/app/dashboard',
              icon: '', class: ''
            };
            if (menu.submenu_list && menu.submenu_list.length !== 0 && menu.menu !== 'Home') {
              // tslint:disable-next-line: no-string-literal
              x['subMenu'] = [];
              menu.submenu_list.filter((submenu, j) => {
                const y = {
                  title: submenu.name,
                  path: this.routingService.urlAssignToMenu(submenu.name),
                  icon: '', class: '', subMenu: false
                };
                // tslint:disable-next-line: no-string-literal
                x['subMenu'].push(y);
              });
            }
            menuItems.push(x);
          });
          const submenuitems = [];
          if (menuItems && menuItems.length !== 0 && ((menuItems[1] && menuItems[1].subMenu) ||
            menuItems[0].path || menuItems[0].subMenu)) {
            menuItems.filter((menu: any) => {
              if (menu.subMenu) {
                menu.subMenu.filter(submneu => {
                  submenuitems.push({ path: submneu.path });
                });
              }
            });
          }
          const isPatchExist = submenuitems.find((path: any) => path.path.includes(route.data.data));
          if (isPatchExist) {
            return true;
          } else {
            this.router.navigateByUrl('/');
            return;
          }
        }
      })).pipe(first());
    }
  }
}
