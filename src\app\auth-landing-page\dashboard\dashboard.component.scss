.chart-wrapper {
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  background: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.live-chart-wrapper {
  box-shadow: 5px 5px 10px 3px grey;
}

.main-content {
  width: 100%;
  margin: 10px auto;
}

#kpiurgent {
  --fill: #0669ad;
  --percentage: 0;
}

.nav-tabs-wrapper {
  display: inline-flex;

  i {
    float: left;
    cursor: pointer;
    margin-top: 10px;
  }
}

.doghunt-bottom-title {
  color: grey;
  font-weight: 100;
  padding: 0px 60px;
}

.multi-graph {
  width: 300px;
  height: 150px;
  position: relative;
  color: #fff;
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  overflow: hidden;
  box-sizing: border-box;

  &:before {
    content: "";
    width: 300px;
    height: 150px;
    border: 50px solid rgba(0, 0, 0, 0.15);
    border-bottom: none;
    position: absolute;
    box-sizing: border-box;
    transform-origin: 50% 0%;
    border-radius: 300px 300px 0 0;
    left: 0;
    top: 0;
  }

  .graph {
    width: 300px;
    height: 150px;
    border: 50px solid var(--fill);
    border-top: none;
    position: absolute;
    transform-origin: 50% 0% 0;
    border-radius: 0 0 300px 300px;
    left: 0;
    top: 100%;
    z-index: 5;
    animation: 1s fillGraphAnimation ease-in;
    transform: rotate(calc(1deg * (var(--percentage) * 1.8)));
    box-sizing: border-box;
    cursor: pointer;

    &:after {
      // content: attr(data-name) ;
      content: attr(data-name) " " counter(varible) "%";
      counter-reset: varible var(--percentage);
      background: var(--fill);
      box-sizing: border-box;
      border-radius: 2px;
      color: #fff;
      font-weight: 200;
      font-size: 12px;
      height: 30px;
      padding: 3px 5px;
      top: 0px;
      position: absolute;
      left: 0;
      transform: rotate(calc(-1deg * var(--percentage) * 1.8))
        translate(-30px, 0px);
      transition: 0.2s ease-in;
      transform-origin: 0 50% 0;
      opacity: 0;
    }

    &:hover {
      opacity: 0.8;

      &:after {
        opacity: 1;
        left: 30px;
      }
    }
  }
}
//#serviceRequests div:first-child div:first-child {
  //padding: 12px;
//}
mat-radio-button {
  padding: 10px;
}
@keyframes fillAnimation {
  0% {
    transform: rotate(-45deg);
  }

  50% {
    transform: rotate(135deg);
  }
}

@keyframes fillGraphAnimation {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }
}

.jobsoutallign {
  float: right;
  margin-top: -100px;
  margin-right: 60px;
}

.highcharts-figure .chart-container {
  width: 300px;
  height: 200px;
  float: left;
}

.highcharts-figure,
.highcharts-data-table table {
  width: 600px;
  margin: 0 auto;
}

.highcharts-data-table table {
  font-family: Verdana, sans-serif;
  border-collapse: collapse;
  border: 1px solid #ebebeb;
  margin: 10px auto;
  text-align: center;
  width: 100%;
  max-width: 500px;
}
.highcharts-data-table caption {
  padding: 1em 0;
  font-size: 1.2em;
  color: #555;
}
.highcharts-data-table th {
  font-weight: 600;
  padding: 0.5em;
}
.highcharts-data-table td,
.highcharts-data-table th,
.highcharts-data-table caption {
  padding: 0.5em;
}
.highcharts-data-table thead tr,
.highcharts-data-table tr:nth-child(even) {
  background: #f8f8f8;
}
.highcharts-data-table tr:hover {
  background: #f1f7ff;
}
.badge {
  display: inline-block;
  padding: 0.5em 0.5em;
  font-size: x-large;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 5em;
}
.badge-info {
    color: black;
    background-color: #ececec;
}
.card {
  border: 0;
  margin-bottom: 30px;
  margin-top: 10px;
  border-radius: 6px;
  color: #333333;
  background: #fff;
  width: 97%;
}

fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000 !important;
}

legend.scheduler-border {
  font-size: 1.2em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}
legend {
  display: block;
}

@media (max-width: 600px) {
  .highcharts-figure,
  .highcharts-data-table table {
    width: 100%;
  }
  .highcharts-figure .chart-container {
    width: 300px;
    float: none;
    margin: 0 auto;
  }
}

@import "../../../assets/scss/navbar.scss";
