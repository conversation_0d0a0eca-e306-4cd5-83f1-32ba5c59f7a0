import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatTableDataSource, MatPaginator, MatSort, ThemePalette } from '@angular/material';
import { ChartOptions, ChartDataSets } from 'chart.js';
import * as moment from 'moment';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { KPISummary } from 'src/app/models/kpiSummary';
import { TotalWorkload } from 'src/app/models/totalWorkload';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';

@Component({
  selector: 'app-kpi-summary',
  templateUrl: './kpi-summary.component.html',
  styleUrls: ['./kpi-summary.component.scss']
})
export class KpiSummaryComponent implements OnInit, OnDestroy {

  dataSource = new MatTableDataSource<KPISummary>();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData: KPISummary[];
  subscription: any;
  dates: any;
  kpiForm: FormGroup = new FormGroup({
    fromDate: new FormControl(null),
    toDate: new FormControl(null)
  });
  currentDate: Date;


  public date: moment.Moment;
  public disabled = false;
  public showSpinners = true;
  public showSeconds = false;
  public touchUi = false;
  public enableMeridian = false;
  public minDate: moment.Moment;
  public maxDate: moment.Moment;
  public stepHour = 1;
  public stepMinute = 1;
  public stepSecond = 1;
  public color: ThemePalette = 'primary';
  displayedColumns: string[] = ['Type', 'PatientMoves', 'NonPatientMoves'];
  constructor(
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
    private readonly userService: UserService,
    private readonly inputValidation: InputValidationService,
    private readonly fb: FormBuilder,
  ) {
  }

  ngOnInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.currentDate = new Date();
    this.setDefaultDates();
    this.submitSearchForm();
  }

  setDefaultDates() {
    let firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    this.kpiForm.controls.fromDate.setValue(firstDay);
    this.kpiForm.controls.toDate.setValue(this.currentDate);
  }

  getKpiSummary(fromDate, toDate) {
    this.reportsService.getkpiSummary(fromDate, toDate).subscribe((res) => {
      debugger;
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('kpiSummary', 'kpiSummary');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('kpiSummary', 'kpiSummary');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Type': key.Type,
          'Patient Moves%': key.PatientMoves,
          'Non Patient Moves%': key.NonPatientMoves
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  ngOnDestroy() {
    // this.subscription.unsubscribe();
  }

  submitSearchForm() {
    const formValue = this.kpiForm.value;
    var fromDate = new Date(formValue.fromDate).toISOString();
    var toDate = new Date(formValue.toDate).toISOString();
    this.getKpiSummary(fromDate, toDate);
  }

  resetFilter() {
    this.setDefaultDates();
    this.submitSearchForm();
  }
}

