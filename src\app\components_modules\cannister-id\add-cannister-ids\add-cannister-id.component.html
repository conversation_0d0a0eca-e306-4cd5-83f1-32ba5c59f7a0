<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Cannister Id</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Cannister Id</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="cannisterForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-12">
                                                    <mat-form-field>
                                                        <mat-label>Cannister Id<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Cannister Id"
                                                            formControlName="CannisterId" maxlength="25" #desc>
                                                        <mat-hint style="text-align: end;">{{desc.value.length}} / 25
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="cannisterForm.controls.CannisterId"
                                                                [fieldName]="'Cannister Id'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <mat-radio-group formControlName="Status">
                                                        <mat-radio-button class="example-margin" value=true>Active
                                                        </mat-radio-button>
                                                        <mat-radio-button class="example-margin" value=false>
                                                            Inactive</mat-radio-button>
                                                    </mat-radio-group>
                                                    <mat-error class="pull-left error-css">
                                                        <app-error-message [control]="cannisterForm.controls.Status"
                                                            [fieldName]="'Status'" [fieldType]="'select'">
                                                        </app-error-message>
                                                    </mat-error>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveCannisterId('save')">Submit</button>
                                            <!-- <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveCannisterId('update')">Update</button> -->
                                            <!-- <button mat-raised-button
                                                (click)="cannisterForm.reset(); cannisterForm.controls.status.setValue('true'); getTowerById()"
                                                class="btn btn-white pull-right">Reset</button> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>