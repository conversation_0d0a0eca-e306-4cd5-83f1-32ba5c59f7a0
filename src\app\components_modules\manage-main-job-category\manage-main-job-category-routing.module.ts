import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListMianJobCategoryComponent } from './list-mian-job-category/list-mian-job-category.component';
import { AddUpdateMianJobCategoryComponent } from './add-update-mian-job-category/add-update-mian-job-category.component';


const routes: Routes = [
  { path: '', component: ListMianJobCategoryComponent },
  { path: 'addmainjobcategory', component: AddUpdateMianJobCategoryComponent },
  { path: 'updatemainjobcategory/:id', component: AddUpdateMianJobCategoryComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageMainJobCategoryRoutingModule { }
