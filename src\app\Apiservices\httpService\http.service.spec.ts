import { TestBed } from '@angular/core/testing';

import { HttpService } from './http.service';
import { HttpClient } from '@angular/common/http';
import { JasmineDependencyModule } from 'src/app/jasmine-dependency/jasmine-dependency.module';

describe('HttpService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [JasmineDependencyModule],
    providers: [HttpClient]
  }));

  it('should be created', () => {
    const service: HttpService = TestBed.get(HttpService);
    expect(service).toBeTruthy();
  });
});
