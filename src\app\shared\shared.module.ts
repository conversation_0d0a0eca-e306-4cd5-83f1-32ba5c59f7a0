import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ErrorMessageComponent } from "./error-message/error-message.component";
import { LoaderComponent } from "./loader/loader.component";
import { ChartsModule } from "ng2-charts";
import { MatProgressBarModule } from "@angular/material";
import { LocalDateConversionPipe } from "./pipes/local-date-conversion.pipe";
import { SelectFirstOptionDirective } from "./directives/select-first-option.directive";
import { SafeHtmlPipe } from "./pipes/safehtml.pipe";

@NgModule({
  declarations: [
    ErrorMessageComponent,
    LoaderComponent,
    LocalDateConversionPipe,
    SelectFirstOptionDirective,
    SafeHtmlPipe,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
  ],

  exports: [
    FormsModule,
    ReactiveFormsModule,
    ChartsModule,
    ErrorMessageComponent,
    MatProgressBarModule,
    LoaderComponent,
    LocalDateConversionPipe,
    SelectFirstOptionDirective,
    SafeHtmlPipe,
  ],
})
export class SharedModule {}
