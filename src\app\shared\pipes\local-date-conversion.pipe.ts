import { Pipe, PipeTransform } from "@angular/core";
import * as moment from "moment";
@Pipe({
  name: "localDateConversion",
})
export class LocalDateConversionPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    return moment(value).isValid() && args == "date"
      ? moment(value).format("DD/MM/YYYY")
      : moment(value).isValid() && args == "time"
      ? moment(value).format("HH:mm")
      : moment(value).isValid() && args == "full"
      ? moment(value).format("DD/MM/YYYY HH:mm").toString()
      : value;
  }
}
