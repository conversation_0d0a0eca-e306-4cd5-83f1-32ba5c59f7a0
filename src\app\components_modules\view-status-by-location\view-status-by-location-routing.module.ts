import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { AddUpdateJobRequestsComponent } from "../job-requests/add-update-job-requests/add-update-job-requests.component";
import { ViewStatusByLocationComponent } from "./view-status-by-location.component";

const routes: Routes = [
  {
    path: "",
    component: ViewStatusByLocationComponent,
  },
  { path: "updatejobrequest/:id", component: AddUpdateJobRequestsComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ViewStatusByLocationRoutingModule {}
