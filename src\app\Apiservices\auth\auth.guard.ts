import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanLoad, Route, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { StorageService } from '../stoargeService/storage.service';
import { JwtHelperService } from '@auth0/angular-jwt';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanLoad {
  constructor(
    private readonly router: Router,
    private readonly toastr: ToastrService,
    private readonly storageService: StorageService,
    private readonly jwthelper: JwtHelperService
  ) { }

  canLoad(route: Route): Observable<boolean> | boolean {
    const globalToken = this.storageService.getData('access_token');
    const validToken = this.jwthelper.isTokenExpired(globalToken);
    if (globalToken && !validToken) {
      return true;
    } else {
      this.router.navigateByUrl('/');
      return false;
    }
  }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if(next.data.wildCardRoute){
      const globalToken = this.storageService.getData('access_token');
    const validToken = this.jwthelper.isTokenExpired(globalToken);
    if (globalToken && !validToken) {
      this.toastr.info('This page does not exist');
      this.router.navigateByUrl('app/dashboard')
      return false;
    } else {
      this.router.navigateByUrl('/');
      return false;
    }
      

    }
  }
}
