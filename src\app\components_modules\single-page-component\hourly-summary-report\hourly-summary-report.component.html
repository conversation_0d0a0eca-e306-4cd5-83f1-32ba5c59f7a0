<div class="main-content">
  <div class="container-fluid">
    <app-summary-menu-list [routes]="routes"></app-summary-menu-list>
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('xsls')">
                      xsls
                    </p>
                  </a>
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" (click)="exportTable('pdf')"
                    class="nav-link">
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li style="margin-left: 1%; line-height: 32px">
                  <p>View Hourly Report</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <!-- <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div> -->

              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="hourSummaryForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Hourly Report
                      </legend>
                      <div class="row">
                        <div class="col-4">
                          <app-datep-picker [dateConfiguration]="FromDate"
                            [control]="hourSummaryForm.controls.from_date" [fieldName]="FromDate.label"
                            [fieldType]="'select'" (getDate)="getDate()">
                          </app-datep-picker>
                        </div>
                        <div class="col-4">
                          <app-datep-picker [dateConfiguration]="ToDate" [control]="hourSummaryForm.controls.to_date"
                            [fieldName]="ToDate.label" [fieldType]="'select'" (getDate)="getDate()">
                          </app-datep-picker>
                        </div>
                        <div class="col-4">
                          <mat-form-field class="alignField">
                            <mat-label>Day</mat-label>
                            <mat-select formControlName="day">
                              <mat-option *ngFor="let dayVal of weekType" [value]="dayVal">{{ dayVal }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-4">
                          <mat-form-field class="alignField">
                            <mat-label>Status</mat-label>
                            <mat-select formControlName="status">
                              <mat-option *ngFor="let item of status" [value]="item">{{ item }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-4">
                          <mat-form-field class="alignField">
                            <mat-label>Task Type</mat-label>
                            <mat-select formControlName="task">
                              <mat-option *ngFor="let item of taskType" [value]="item.Value">{{ item.Text }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button mat-raised-button (click)="hourSummaryForm.reset(); createForm()"
                            class="btn btn-white pull-right">
                            Reset
                          </button>
                          <button mat-raised-button type="submit" class="btn btn-primary pull-right"
                            (click)="getHourlyReport()">
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-y: auto">
                    <table id="hourSummaryReport" mat-table [dataSource]="displayData" matSort>
                      <caption></caption>
                      <ng-container [matColumnDef]="column" *ngFor="let column of displayedColumns">
                        <th mat-header-cell *matHeaderCellDef> {{ column }} </th>
                        <ng-container *ngIf="column == -1">
                          <th style="width: 30%;" *matCellDef="let element">
                            {{ getDisplayHeaderName(element[column]) }}
                          </th>
                        </ng-container>
                        <ng-container *ngIf="column != -1">
                          <td mat-cell *matCellDef="let element">
                            {{ element[column] }}
                          </td>
                        </ng-container>
                      </ng-container>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                    </table>
                    <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf [heads]="['Hour', 'Patient Moves', 'Non Patient Moves', 'Total']" [title]="'Hour Summary Report'"
  [datas]="pdfData" [isHourlyReport]="true">
</app-table-for-pdf>