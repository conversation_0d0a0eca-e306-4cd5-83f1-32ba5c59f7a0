import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StandardTimeRoutingModule } from './standard-time-routing.module';
import { ListStandardTimeConfigurationComponent } from './list-standard-time-configuration/list-standard-time-configuration.component';
import {
  AddUpdateStandardTimeConfigurationComponent
} from './add-update-standard-time-configuration/add-update-standard-time-configuration.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListStandardTimeConfigurationComponent, AddUpdateStandardTimeConfigurationComponent],
  imports: [
    CommonModule,
    StandardTimeRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class StandardTimeModule { }
