export interface EnhancedReport {
    order_no: string;
    request_type: string;
    task_time :string;
    assign_time: string;
    start_time: string;
    completion_time: string;
    cancel_time: string;
    requestor: string;
    patient_name: string;
    nric: string;
    order_from: string;
    from_location: string;
    from_room: string,
    from_bed: string;
    to_location: string;
    to_room: string,
    to_bed: string;
    job_status: string;
    main_category: string;
    sub_category: string;
    transport_mode: string;
    req_resp: number;
    resp_comp: number;
    created_date: string;
    //due_time: string;
    smart_assigned:string;
    isolation_precaution: string;
    remarks: string;
    resource1: string;
    resource2: string;
    created_by: string;
    modified_by: string;
    modified_date: string;
    delay_reason: string;
    cancel_reason: string;
    ack: boolean;
}
