import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { AddUpdateJobRequestsComponent } from "../job-requests/add-update-job-requests/add-update-job-requests.component";
import { ViewStatusByJobCategoryComponent } from "./view-status-by-job-category.component";

const routes: Routes = [
  {
    path: "",
    component: ViewStatusByJobCategoryComponent,
  },
  { path: "updatejobrequest/:id", component: AddUpdateJobRequestsComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ViewStatusByJobCategoryRoutingModule {}
