import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListRolesComponent } from './list-roles/list-roles.component';
import { AddUpdateRolesComponent } from './add-update-roles/add-update-roles.component';


const routes: Routes = [
  {path: '', component: ListRolesComponent},
  {path: 'addroles', component: AddUpdateRolesComponent},
  {path: 'updateroles/:id', component: AddUpdateRolesComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RolesRoutingModule { }
