<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <ul class="nav">
                <li class="nav">
                  <p>Active Directory Settings</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="ldapForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div
                          class="col-12"
                          *ngFor="
                            let ad_url of ldapForm.get('ad_settings')[
                              'controls'
                            ];
                            let i = index
                          "
                        >
                          <ng-container [formGroup]="ad_url">
                            <mat-form-field>
                              <mat-label
                                >AD ID URL {{ i }}
                                <span class="error-css"
                                  ><span class="error-css">*</span></span
                                >
                              </mat-label>
                              <input
                                matInput
                                #desc
                                placeholder="Enter AD URL"
                                formControlName="ad_name"
                              />
                              <mat-error class="pull-left error-css">
                                <app-error-message
                                  [control]="ad_url.controls.ad_name"
                                  [fieldName]="'AD URL' + ' ' + [i]"
                                  [fieldType]="'enter'"
                                >
                                </app-error-message>
                              </mat-error>
                            </mat-form-field>
                          </ng-container>
                        </div>
                        <div class="col-12">
                          <app-file-picker
                            [control]="ldapForm.controls.baseUrl"
                            [fieldName]="'Logo'"
                            [fieldType]="'select'"
                            [allowedExtensions]="[
                              'jpeg',
                              'JPEG',
                              'png',
                              'PNG',
                              'jpg',
                              'JPG'
                            ]"
                            [fileupload]="fileupload"
                            [maxMB]="10485760"
                            [maxSize]="10"
                            (selectedFile)="getFile($event)"
                          >
                          </app-file-picker>
                          <span *ngIf="imageUrl">
                            <img
                              class="image-selected"
                              [src]="imageUrl.result"
                              alt="image not found"
                            />
                          </span>
                        </div>
                        <div class="col-7">
                          <br />
                          <mat-label>AD Status</mat-label>
                          <mat-radio-group
                            formControlName="ad_status"
                            class="alignRadioBtn"
                          >
                            <mat-radio-button
                              class="example-margin"
                              value="true"
                              >ON
                            </mat-radio-button>
                            <mat-radio-button
                              class="example-margin"
                              value="false"
                              >OFF
                            </mat-radio-button>
                          </mat-radio-group>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button
                        mat-raised-button
                        class="btn btn-primary pull-right"
                        (click)="adsSubmit()"
                      >
                        Update
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
