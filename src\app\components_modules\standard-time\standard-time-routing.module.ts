import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListStandardTimeConfigurationComponent } from './list-standard-time-configuration/list-standard-time-configuration.component';
import {
  AddUpdateStandardTimeConfigurationComponent
} from './add-update-standard-time-configuration/add-update-standard-time-configuration.component';


const routes: Routes = [
  { path: '', component: ListStandardTimeConfigurationComponent },
  { path: 'addstandardtime', component: AddUpdateStandardTimeConfigurationComponent },
  { path: 'updatestandardtime/:id', component: AddUpdateStandardTimeConfigurationComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StandardTimeRoutingModule { }
