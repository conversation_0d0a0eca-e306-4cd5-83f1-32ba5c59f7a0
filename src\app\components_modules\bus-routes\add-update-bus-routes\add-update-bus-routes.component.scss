@import "../../../../assets/scss/navbar.scss";
.main-content {
  width: 65%;
  margin: 10px auto;
}
.mat-radio-button ~ .mat-radio-button {
  margin-left: 16px;
}

.col-6 {
  flex: 0 0 100% !important;
}

.align-checkbox {
  float: left;
  padding: 1px 0px 0px 10px;
  margin: 0 10px 0 0;
}

.Sequential {
  padding-top: 18px;
  padding-left: 20px;
}

button {
  cursor: pointer;
}
.alignInput {
  float: left;
  display: inline-flex;
  mat-checkbox {
    padding-right: 20px;
  }
}
.alignRadioBtn {
  margin-left: 40px;
}
mat-checkbox-inner-container {
  height: 20px;
  width: 20px;
}
.nav li {
  width: auto;
}
.nav-tabs-wrapper {
  display: inline-flex;
  em {
    float: left;
    cursor: pointer;
    margin-top: 10px;
  }
}

.example-container {
  width: 400px;
  max-width: 100%;
  margin: 0 25px 25px 0;
  display: inline-block;
  vertical-align: top;
}

.example-list {
  border: solid 1px #ccc;
  background: white;
  border-radius: 4px;
  display: block;
  overflow-y: scroll;
  min-height: 300px;
  max-height: 300px;
  margin: 20px;
}

.example-container h4 {
  margin: 15px;
}

.example-list2 {
  border: solid 1px #ccc;
  min-height: 60px;
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.example-box {
  padding: 20px 10px;
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  display: block;
  text-align: left;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: white;
  font-size: 14px;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

::-webkit-scrollbar {
  width: 3px;
  color: gray;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.forward-example {
  margin-top: 60px;

  p {
    padding: 8px 12px;
    width: 60px;
    margin-left: 25px;
    color: #1b2581;
    letter-spacing: -5px;
  }
}

.border-forward {
  border: 1px solid #1b2581;
  border-radius: 3px;
}

.example-margin-btn {
  padding-left: 70px;
}

.red {
  background-color: #FEF8DD;
  color: black;
}

.blue {
  background-color: #CAF1DE;
  color: black;
}
