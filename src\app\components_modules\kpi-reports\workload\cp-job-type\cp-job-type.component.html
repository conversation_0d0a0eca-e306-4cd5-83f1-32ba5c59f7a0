<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div>
              <p style="float: right">
                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                  Export </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('xsls')">
                      xsls
                    </p>
                  </a>
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" (click)="exportTable('pdf')"
                    class="nav-link">
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Central Pool Job Type</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12">
                  <app-searchfilter></app-searchfilter>
                </div>
                <div class="col-md-12 equipmentReport">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table id="centralpooltable">
                      <caption></caption>
                      <thead>
                        <tr>
                          <th id="">Job Type</th>
                          <th id="" *ngFor="let h of headers">
                            {{ h }}-
                            {{ dates && dates.year ? dates.year : currYear }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <th id="middele-header" colspan="14">Urgent</th>
                        </tr>
                        <ng-container *ngIf="pdfData.length && pdfData[0]">
                          <tr *ngFor="let data of pdfData[0]">
                            <th id="">
                              {{ data.item }}
                            </th>
                            <td *ngFor="let r of rows">
                              {{ data[r] }}
                            </td>
                          </tr>
                        </ng-container>
                        <tr>
                          <th id="middele-header" colspan="14">Patient Move</th>
                        </tr>
                        <ng-container *ngIf="pdfData.length && pdfData[1]">
                          <tr *ngFor="let data of pdfData[1]">
                            <th id="">
                              {{ data.item }}
                            </th>
                            <td *ngFor="let r of rows">
                              {{ data[r] }}
                            </td>
                          </tr>
                        </ng-container>
                        <tr>
                          <th id="middele-header" colspan="14">
                            Non Patient Move
                          </th>
                        </tr>
                        <ng-container *ngIf="pdfData.length && pdfData[2]">
                          <tr *ngFor="let data of pdfData[2]">
                            <th id="">
                              {{ data.item }}
                            </th>
                            <td *ngFor="let r of rows">
                              {{ data[r] }}
                            </td>
                          </tr>
                        </ng-container>
                        <tr>
                          <th scope="colgroup" id="middele-header" colspan="14">
                            Grand Total
                          </th>
                        </tr>
                        <ng-container *ngIf="pdfData.length && pdfData[3]">
                          <tr *ngFor="let data of pdfData[3]">
                            <th scope="colgroup" id="">
                              {{ data.item }}
                            </th>
                            <td *ngFor="let r of rows">
                              {{ data[r] }}
                            </td>
                          </tr>
                        </ng-container>
                      </tbody>
                    </table>
                    <div *ngIf="pdfData.length === 0">
                      No records to display.
                    </div>
                  </div>
                </div>
                <br />
                <div class="col-md-12 chart-card">
                  <div class="chart-wrapper">
                    <canvas *ngIf="chartsGraph" baseChart height="100px" width="300px" [datasets]="barChartData"
                      [labels]="['Workload']" [options]="barChartOptions" [plugins]="barChartPlugins"
                      [legend]="barChartLegend" [chartType]="'bar'">
                    </canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-cpjobtypepdf [headers]="headers" [rows]="rows" [title]="
    'Central Poll Job report for ' +
    (dates && dates.year ? dates.year : currYear)
  " [pdfData]="pdfData">
</app-cpjobtypepdf>