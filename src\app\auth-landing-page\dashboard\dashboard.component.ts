import { Component, OnInit } from '@angular/core';
import { MultiDataSet, Label, PluginServiceGlobalRegistrationAndOptions } from 'ng2-charts';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ChartOptions, ChartDataSets } from 'chart.js';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import * as moment from 'moment';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
import {  interval,Subscription } from 'rxjs';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  subscription: Subscription;
  intervalId: number;
  public demodoughnutChartData1 = [[0, 100]];
  public demodoughnutChartData2 = [[0, 100]];
  public demodoughnutChartData3 = [[0, 100]];
  public demodoughnutChartData4 = [[0, 100]];
  public lineChartColors:Array<any> = [
    { // grey
      backgroundColor: '#ffa1b5'
    },
    { // dark grey
      backgroundColor: '#86c7f3'
    },
    { // grey
      backgroundColor: '#ffe29a'
    },
    { // grey
      backgroundColor: '#6a1b9a'
    }
  ];
  semidonut: ChartOptions = {
    circumference: Math.PI,
    rotation: Math.PI,
    legend: { position: 'bottom' }
  };
  public doughnutChartColors: any[] =
    [
      {
        backgroundColor: ['#0669AD', 'rgba(0, 0, 0, 0.15)'],
      }
    ];
  public doughnutChartColorsancelC: any[] =
    [
      {
        backgroundColor: ['#e43144', 'rgba(0, 0, 0, 0.15)'],
      }
    ];
  public barChartLabels: string[] = [];
  public barChartJobcatLabels: string[] = [];
  testVal: any = 60;
  hourlyJobtrend: ChartOptions = {
    legend: { position: 'bottom' },
    //title: { text: 'Hourly Job Trend (Priority)', display: true },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  hourlyJobCattrend: ChartOptions = {
    legend: { position: 'bottom' },
    //title: { text: 'Hourly Job Trend (Job Category)', display: true },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  barChartDataHourlyBased: ChartDataSets[] = [
    { data: [], label: 'URGENT' },
    { data: [], label: 'NON URGENT' },
    //{ data: [], label: 'PM' },
    //{ data: [], label: 'NPM' },
  ];
  barChartDataHourlyJobcatBased: ChartDataSets[] = [
    { data: [], label: 'Patient Move' },
    { data: [], label: 'Non Patient Move' },
    { data: [], label: 'External Move' },
    { data: [], label: 'Mortuary' },
  ];
  dateForm: FormGroup;
  today = new Date();
  currYear = moment(this.today).format('YYYY');
  currMonth = moment(this.today).format('MM');
  currDay = moment(this.today).format('DD');
  currentYear = this.today.getFullYear();
  kpiVal: any;
  patientmoveVal: any;
  nonpatientmove: any;
  cancelledjobs: any;
  kpiResult: any;
  jobporterscountResult : any;
  years = [this.currentYear, this.currentYear - 1];
  months = [{ id: '01', val: 'January' },
  { id: '02', val: 'February' },
  { id: '03', val: 'March' },
  { id: '04', val: 'April' },
  { id: '05', val: 'May' },
  { id: '06', val: 'June' },
  { id: '07', val: 'July' },
  { id: '08', val: 'August' },
  { id: '09', val: 'September' },
  { id: '10', val: 'October' },
  { id: '11', val: 'November' },
  { id: '12', val: 'December' },
  ];
  DateValue = {
    rangePicker: false,
    collumns: 'col-16',
    label: '',
    required: false,
    minDateStart: '2000-12-12',
    maxDateStart: '',
    minDateEnd: '',
    maxDateEnd: '',
  };
  doughnutChartLabels: Label[] = ['BMW'];
  doughnutChartData: MultiDataSet = [
    [55]
  ];
  barChartOptions1: ChartOptions = {
    legend: { position: 'bottom' },
    //title: { text: 'Today’s Workload – Porter Type', display: true,padding :20 },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  barChartOptions2: ChartOptions = {
    legend: { position: 'bottom' },
    //title: { text: 'Workload - Central Pool(Job Priority)', display: true,padding :20 },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  barChartOptions3: ChartOptions = {
    legend: { position: 'bottom' },
    //title: { text: 'Workload - Job category', display: true,padding :20 },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Number of Jobs',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  barChartOptions4: ChartOptions = {
    title: { text: 'Top 3 Cancellation Reasons', display: true },
    responsive: true,
    scales: {
      yAxes: [{
        scaleLabel: {
          labelString: 'Reason(%)',
          display: true,
        },
        ticks: {
          min: 0
        }
      }]
    }
  };
  barChartLegend = true;
  barChartPlugins = [];

  barChartData1: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  barChartData2: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  barChartData3: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  barChartData4: ChartDataSets[] = [
    { data: [0], label: '' }
  ];
  selectedDateInput: any;

  constructor(
    private readonly userService: UserService,
    private readonly fb: FormBuilder,
    public inputValidationService: InputValidationService
  ) {
    this.createForm();
  }

  ngOnInit() {
    this.getCurrentDateTime();
    // This is METHOD 1
    const source = interval(60000);
    //const text = 'Your Text Here';
    this.subscription = source.subscribe(val => this.opensnack());
  }
  opensnack() {
    //alert(text);
    this.getCurrentDateTime();
  }

  ngOnDestroy() {
    // For method 1
    this.subscription && this.subscription.unsubscribe();
  }
  createForm(active?: any) {
    this.dateForm = this.fb.group({
      active: [active ? active : 'dailyTrue'],
      dateInput: [this.selectedDateInput ? this.selectedDateInput : new Date().toISOString()],
      month: [this.currMonth],
      year: [Number(this.currYear)],
      day: [this.currDay]
    });
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe(res => {
      this.today = this.dateForm.value.dateInput || res;
      this.currYear = this.dateForm.get('active').value === 'monthlyTrue' ? this.dateForm.value.year : moment(this.today).format('YYYY');
      this.currMonth = this.dateForm.get('active').value === 'monthlyTrue' ? this.dateForm.value.month : moment(this.today).format('MM');
      this.currDay = this.dateForm.get('active').value === 'monthlyTrue' ? '00' : moment(this.today).format('DD');
      this.currentYear = new Date(this.today).getFullYear();
      this.createForm(this.dateForm.get('active').value);
      this.getAnalytics(this.currYear, this.currMonth, this.currDay);
      this.getJobStaffCounts(this.currYear, this.currMonth, this.currDay);
      this.createHourlylabels(this.currYear, this.currMonth, this.currDay);
      this.createJobcatHourlylabels(this.currYear, this.currMonth, this.currDay);
      this.getBarAnalytucs(this.currYear, this.currMonth, this.currDay);
    });
  }

  getBarAnalytucs(currYear, currMonth, currDay) {
    this.userService.getDashboardData(`api/dashboard/cancelreason/${currYear}/${currMonth}/${currDay}`).subscribe(res => {
      this.barChartData4 = [{ data: [0], label: '' }];
      if (res && res.length) {
        this.barChartData4 = [];
        res.forEach(reason => {
          this.barChartData4.push(
            {
              data: [reason.count ? reason.count : 0],
              label: `${reason.Type}  (${reason.count ? reason.count : 0})`
            }
          );
        });
      }
    });
    this.userService.getDashboardData(`api/dashboard/cpjobtype/${currYear}/${currMonth}/${currDay}`).subscribe(res => {
      this.barChartData2 = [{ data: [0], label: '' }];
      if (res && res.length) {
        this.barChartData2 = [];
        res.forEach(reason => {
          this.barChartData2.push(
            {
              data: [reason.count ? reason.count : 0],
              label: `${reason.type}`
              //label: `${reason.type}  (${reason.count ? reason.count : 0})`
            }
          );
        });
      }
    });
    this.userService.getDashboardData(`api/dashboard/cpjobcategory/${currYear}/${currMonth}/1/${currDay}`).subscribe(res => {
      this.barChartData3 = [{ data: [0], label: '' }];
      if (res && res.length) {
        this.barChartData3 = [];
        res.forEach(reason => {
          this.barChartData3.push(
            {
              data: [reason.count ? reason.count : 0],
              label: `${reason.type}`
              //label: `${reason.type}  (${reason.count ? reason.count : 0})`
            }
          );
        });
      }
    });
    this.userService.getDashboardData(`api/dashboard/workload/${currYear}/${currMonth}/${currDay}`).subscribe(res => {
      this.barChartData1 = [{ data: [0], label: '' }];
      if (res && res.length) {
        this.barChartData1 = [];
        res.forEach(reason => {
          if(reason.type != 'Total' ){
          this.barChartData1.push(
            {
              data: [reason.count ? reason.count : 0],
              //label: `${reason.type}  (${reason.count ? reason.count : 0})`
              label: `${reason.type}`
            }
          
          );
          }
        });
      }
    });
  }

  createHourlylabels(currYear, currMonth, currDay) {
    this.userService.getDashbOrdAnalyticsHourly(currYear, currMonth, currDay).subscribe(res => {
      this.createHourlyGraph(res);
    });
  }

  createHourlyGraph(res) {
    this.barChartLabels = [];
    this.barChartDataHourlyBased = [
      { data: [], label: 'URGENT' },
     // { data: [], label: 'PM' },
      //{ data: [], label: 'NPM' }];
      { data: [], label: 'NON URGENT' }];
    this.barChartLabels.push('12 AM');
    this.crateHourlybasedBarchart();
    for (let i = 1; i <= 11; i++) {
      this.barChartLabels.push(String(i) + ' AM');
      this.crateHourlybasedBarchart();
    }
    this.barChartLabels.push('12 PM');
    this.crateHourlybasedBarchart();
    for (let i = 1; i <= 11; i++) {
      this.barChartLabels.push(String(i) + ' PM');
      this.crateHourlybasedBarchart();
    }
    // this.barChartLabels.push('12 AM');
    // this.crateHourlybasedBarchart();
    // for (let i = 1; i < 7; i++) {
    //   this.barChartLabels.push(String(i) + ' AM');
    //   this.crateHourlybasedBarchart();
    // }
    if (res && res.length) {
      res.forEach(time => {
        const existIngCode = this.barChartDataHourlyBased.find(graph => graph.label === time.type);
        if (existIngCode) {
          const indexOfHour =
            this.barChartLabels.findIndex(ind => ind === (time.hour >= 12 ?
              (time.hour === 12 ? '12 PM' : ((time.hour - 12) + ' PM')) : (time.hour + ' AM')));
          if (indexOfHour !== -1) {
            existIngCode.data[indexOfHour] = time.count;
          }
        }
      });
    }
  }


  crateHourlybasedBarchart() {
    this.barChartDataHourlyBased[0].data.push(0);
    this.barChartDataHourlyBased[1].data.push(0);
    //this.barChartDataHourlyBased[2].data.push(0);
  }

  createJobcatHourlylabels(currYear, currMonth, currDay) {
    this.userService.getDashboardData(`api/dashboard/cpjobcategory/${currYear}/${currMonth}/0/${currDay}`).subscribe(res => {
     
    this.barChartJobcatLabels = [];
    this.barChartDataHourlyJobcatBased = [
      { data: [], label: 'Patient Move' },
      { data: [], label: 'Non Patient Move' },
      { data: [], label: 'External Move' },
      { data: [], label: 'Mortuary' }];
      this.barChartJobcatLabels.push('12 AM');
      this.cratejobcatHourlybasedBarchart();
      for (let i = 1; i <= 11; i++) {
        this.barChartJobcatLabels.push(String(i) + ' AM');
        this.cratejobcatHourlybasedBarchart();
      }
      this.barChartJobcatLabels.push('12 PM');
      this.cratejobcatHourlybasedBarchart();
      for (let i = 1; i <= 11; i++) {
        this.barChartJobcatLabels.push(String(i) + ' PM');
        this.cratejobcatHourlybasedBarchart();
      }
      // this.barChartJobcatLabels.push('12 AM');
      // this.cratejobcatHourlybasedBarchart();
      // for (let i = 1; i < 7; i++) {
      //   this.barChartJobcatLabels.push(String(i) + ' AM');
      //   this.cratejobcatHourlybasedBarchart();
      // }
    if (res && res.length) {
      res.forEach(time => {
        const existIngCode = this.barChartDataHourlyJobcatBased.find(graph => graph.label === time.type);
        if (existIngCode) {
          const indexOfHour =
            this.barChartJobcatLabels.findIndex(ind => ind === (time.hour >= 12 ?
              (time.hour === 12 ? '12 PM' : ((time.hour - 12) + ' PM')) : (time.hour + ' AM')));
          if (indexOfHour !== -1) {
            existIngCode.data[indexOfHour] = time.count;
          }
        }
      });
    }
  });
}


  cratejobcatHourlybasedBarchart() {
    this.barChartDataHourlyJobcatBased[0].data.push(0);
    this.barChartDataHourlyJobcatBased[1].data.push(0);
    this.barChartDataHourlyJobcatBased[2].data.push(0);
    this.barChartDataHourlyJobcatBased[3].data.push(0);
  }
  getAnalytics(year, month, day) {
    this.userService.getDashbOrdAnalytics(year, month, day).subscribe(res => {
      this.kpiResult = res;
      this.kpiVal = Math.round(this.percentage(res.urgent_jobs_kpi, res.urgent_jobs));
      this.patientmoveVal = Math.round(this.percentage(res.pm_jobs_kpi, res.pm_jobs));
      this.nonpatientmove = Math.round(this.percentage(res.npm_jobs_kpi, res.npm_jobs));
      this.cancelledjobs = Math.round(this.percentage(res.cancelled_jobs, res.total_jobs));
      this.demodoughnutChartData1 = [[this.kpiVal, 100 - Number(this.kpiVal ? this.kpiVal : 0)]];
      this.demodoughnutChartData2 = [[this.patientmoveVal, 100 - Number(this.patientmoveVal ? this.patientmoveVal : 0)]];
      this.demodoughnutChartData3 = [[this.nonpatientmove, 100 - Number(this.nonpatientmove ? this.nonpatientmove : 0)]];
      this.demodoughnutChartData4 = [[this.cancelledjobs, 100 - Number(this.cancelledjobs ? this.cancelledjobs : 0)]];
    });
  }
  getJobStaffCounts(year, month, day) {
    this.userService.getDashboardData(`api/dashboard/jobporterscount/${year}/${month}/${day}`).subscribe(res => {
      this.jobporterscountResult = res;
    });
  }
  percentage(num, total) {
    return (num / total) * 100 || 0;
  }

  selectedOption() {
    this.dateForm.get('month').setValue(this.currMonth);
    this.dateForm.get('year').setValue(Number(this.currYear));
    this.dateForm.get('day').setValue(Number(this.currDay));
    this.dateForm.get('dateInput').setValue(new Date().toISOString());
    if (this.dateForm.get('active').value === 'dailyTrue') {
      this.getJobStaffCounts(this.currYear, this.currMonth, this.currDay);
      this.getAnalytics(this.currYear, this.currMonth, this.currDay);
      this.createHourlylabels(this.currYear, this.currMonth, this.currDay);
      this.createJobcatHourlylabels(this.currYear, this.currMonth, this.currDay);
      this.getBarAnalytucs(this.currYear, this.currMonth, this.currDay);
    }
    if (this.dateForm.get('active').value === 'monthlyTrue') {
      this.getJobStaffCounts(this.currYear, this.currMonth, '00');
      this.getAnalytics(this.currYear, this.currMonth, '00');
      this.createHourlylabels(this.currYear, this.currMonth, '00');
      this.createJobcatHourlylabels(this.currYear, this.currMonth, '00');
      this.getBarAnalytucs(this.currYear, this.currMonth, '00');
    }
  }

  selectedCustomDate() {
    this.selectedDateInput = this.dateForm.get('dateInput').value;
    const year = moment(this.selectedDateInput).format('YYYY');
    const month = moment(this.selectedDateInput).format('MM');
    const day = moment(this.selectedDateInput).format('DD');
    this.dateForm.get('year').setValue(year);
    this.dateForm.get('month').setValue(month);
    this.dateForm.get('day').setValue(day);
    this.getJobStaffCounts(year, month, day);
    this.getAnalytics(year, month, day);
    this.createHourlylabels(year, month, day);
    this.createJobcatHourlylabels(year, month, day);
    this.getBarAnalytucs(year, month, day);
  }

  selectedDateValue() {
    const month = this.dateForm.get('month').value;
    const year = this.dateForm.get('year').value;
    this.getJobStaffCounts(this.currYear, this.currMonth, '00');
    this.getAnalytics(year, month, '00');
    this.createHourlylabels(year, month, '00');
    this.createJobcatHourlylabels(year, month, '00');
    this.getBarAnalytucs(year, month, '00');
  }

}



