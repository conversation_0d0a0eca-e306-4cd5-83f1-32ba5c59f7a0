.base-timer {
    position: relative;
    width: 50px;
    height: 50px;
}
 
.base-timer__svg {
    transform: scaleX(1);
}

.base-timer__circle {
    fill: none;
    stroke: none;
}

.base-timer__path-elapsed {
    stroke-width: 7px;
    stroke: grey;
}

.base-timer__path-remaining {
    stroke-width: 7px;
    stroke-linecap: round;
    transform: rotate(90deg);
    transform-origin: center;
    transition: 1s linear all;
    fill-rule: nonzero;
    stroke: currentColor;
}

.base-timer__path-remaining.arc {
    color: #1B2581 !important;
}

.base-timer__label {
    color: #1B2581 !important;
    position: absolute;
    width: 50px;
    height: 50px;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
}

 
.time-text {
    color: #3f51b5 !important;
    font-weight: bolder;
}
.timer-main{
    display: flex;
    gap: 10px;
    align-items:center;
}
.showTimer{
    display: flex;
    align-items: center;
}