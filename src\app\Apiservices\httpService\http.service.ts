import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { JwtHelperService } from "@auth0/angular-jwt";
import { StorageService } from "../stoargeService/storage.service";
import { Router } from "@angular/router";
import { switchMap, map } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { MatDialog } from "@angular/material";
import { UserIdleService } from "angular-user-idle";

@Injectable({
  providedIn: "root",
})
export class HttpService {
  tokenrefreshCalled = false;
  private readonly onLoginStateChange: any[] = [];

  constructor(
    private readonly http: HttpClient,
    private readonly jwtHelper: JwtHelperService,
    private readonly storageService: StorageService,
    private readonly router: Router,
    private matDialog: MatDialog,
    private userIdle: UserIdleService
  ) {}

  public get(url: string, body?): Observable<any> {
    return this.http.get(url, { params: body });
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.get(url, { params: body });
    //   })
    // );
  }

  public getFiles(url: string, body?): Observable<any> {
    return this.http.get(url, {
      responseType: "blob",
      observe: "response",
    });
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.get(url, {
    //       responseType: "blob",
    //       observe: "response",
    //     });
    //   })
    // );
  }

  public post(url: string, body?, headers?: any): Observable<any> {
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.post(url, body);
    //   })
    // );
    return this.http.post(url, body);
  }
  public put(url: string, body?): Observable<any> {
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.put(url, body);
    //   })
    // );
    return this.http.put(url, body);
  }
  public delete(url: string, body?): Observable<any> {
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.delete(url, { params: body });
    //   })
    // );
    return this.http.delete(url, { params: body });
  }
  public patch(url: string, body?): Observable<any> {
    // return this.refresh().pipe(
    //   switchMap((res) => {
    //     return this.http.patch(url, body);
    //   })
    // );
    return this.http.patch(url, body);
  }
  public getHeaders(url, body): Observable<any> {
    let header = new HttpHeaders();
    header = header.set("data", body);
    return this.http.get(url, { headers: header });
  }
  
  refreshToken(): Observable<any> {
    return this.http.get(`${environment.base_url}api/refresh_token`);
  }

  refresh(): Observable<any> {
    const token = localStorage.getItem("access_token");
    if (
      token &&
      this.jwtHelper.isTokenExpired(token) &&
      !this.tokenrefreshCalled
    ) {
      this.tokenrefreshCalled = true;
      // return this.http.get(`${environment.base_url}api/refresh_token`).pipe(
      //   map(
      //     (res: Response) => {
      //       const element: any = res;
      //       this.storageService.setData("access_token", element);
      //       return of(true);
      //     },
      //     (err) => {
      //       // this.stop();
      //       this.storageService.removeAllData();
      //       this.matDialog.closeAll();
      //       this.router.navigate(["/"]);
      //     }
      //   )
      // );
    } else {
      this.tokenrefreshCalled = false;
      return of(true);
    }
  }

}
