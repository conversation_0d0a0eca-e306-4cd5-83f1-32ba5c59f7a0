import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { LocationReport } from "src/app/models/locationReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-location-report",
  templateUrl: "./location-report.component.html",
  styleUrls: ["./location-report.component.scss", "../../../scss/table.scss"],
})
export class LocationReportComponent implements OnInit {
  routes = [
    // { path: './../transportReports', label: 'Task Type Summary' },
    // { path: './../locationSearch', label: 'Location Summary' },
    // { path: './../equipmentmove', label: 'Equipment Summary' },
    // { path: './../hourReport', label: 'Hourly Summary' },
    // { path: './../kpiReport', label: 'KPI Report' },
  ];
  displayedColumns: string[] = [
    "Location",
    // "TotalRequested",
    "TotalComplete",
    "TotalCancelled",
    "TotalJobs",
    "CompletePercentage",
    "CancelledPercentage",
    "Totalpercentage"
  ];
  dataSource: MatTableDataSource<LocationReport>;
  pdfData = [];
  loactionReportForm: FormGroup;
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  filter = {
    value: ''
  }
  constructor(
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    public activatedRoute: ActivatedRoute,
    private readonly reports: ReportsService,
    private readonly toastr: ToastrService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }
  ngOnInit() {
    this.getCurrentDateTime();
  }

  createForm() {
    this.loactionReportForm = this.fb.group({
      from_date: [this.month, Validators.required],
      to_date: [moment().toDate()],
    });
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.searchLocationReport(this.month, this.today);
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "locationReport",
        "locationReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
      // TableUtilPDf.exportArrayToPdfStyled(this.allRecords, "locationReport_list");

    } else {
      // TableUtil.exportToExcel("locationReport", "locationReport_list");
      TableUtil.exportArrayToExcelDynamically2(this.allRecords, "locationReport_list");
    }
  }
  allRecords: any;
  searchLocationReport(fromValue, toValue) {
    this.allRecords = [];
    if (this.loactionReportForm.valid) {
      const fromdate = moment(fromValue).format("YYYY/MM/DD");
      const todate = moment(toValue).format("YYYY/MM/DD");
      this.reports.getSummaryLocationReport(fromdate, todate).subscribe(
        (res) => {
          this.allRecords = res;
          this.dataSource = new MatTableDataSource(res ? res : []);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          this.setPageSizeOptions();
        },
        (err) => {
          this.toastr.warning("Search Failed", "failed!");
        }
      );
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Location: key.Location,
              // Urgent: key.Urgent,
              // "Non Urgent": key.PatientMove,
              // "Non Patient Move": key.NonPatientMove,
              // "Total Requested": key.TotalRequested,
              "Total Completed": key.TotalComplete,
              "Total Cancelled": key.TotalCancelled,
              "Total Jobs": key.TotalJobs,
              "% Completed": key.CompletePercentage,
              "% Cancelled": key.CancelledPercentage,
              "Total %": key.Totalpercentage
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  getDate() {
    if (
      this.loactionReportForm.get("from_date").value &&
      this.loactionReportForm.get("to_date").value
    ) {
      if (
        this.loactionReportForm.get("from_date").value >=
        this.loactionReportForm.get("to_date").value
      ) {
        this.loactionReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
