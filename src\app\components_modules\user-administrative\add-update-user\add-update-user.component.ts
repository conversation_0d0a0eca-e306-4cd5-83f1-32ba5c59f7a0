import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Component, OnInit, OnDestroy, ViewChild } from "@angular/core";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";
import { ValidationService } from "src/app/Apiservices/validation/validation.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { MasterDataService } from "../../../Apiservices/masterData/master-data.service";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { MatOption, MatSelect } from "@angular/material";
import Swal from 'sweetalert2';

@Component({
  selector: "app-add-update-user",
  templateUrl: "./add-update-user.component.html",
  styleUrls: ["./add-update-user.component.scss"],
})
export class AddUpdateUserComponent implements OnInit, OnDestroy {
  towers = {};
  subJobCategories = {};
  usersForm: FormGroup;
  roles: any = [];
  isldap = false;
  locationsList = [];;
  adStatus: any;
  hideConfirmPassword = true;
  hidePassword = true;
  adStatusShowPassword: boolean;
  departmentList = [];
  jobTypeList = [];
  loc = [];
  dept = [];
  jobs = [];
  location_id = [];
  selectedLoc = [];
  selectedJob: any;
  selectedDepartment: any;
  dropdownSettings: IDropdownSettings = {};
  dropdownJobsSettings: IDropdownSettings = {};
  dropdownDeptSettings: IDropdownSettings = {};
  locationsFilteredList = [];
  departmentsFilteredList = [];
  jobTypesFilteredList = [];
  userData: any;
  editLogs: any = [];
  protected _onDestroy = new Subject<void>();
  public locationFilterCtrl: FormControl = new FormControl();
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public JobTypeFilterCtrl: FormControl = new FormControl();
  public filteredJobType: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public departmentFilterCtrl: FormControl = new FormControl();
  public filteredDepartment: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild(MatOption, { static: true }) allSelected: MatOption;
  @ViewChild(MatSelect, { static: true }) select: MatSelect;
  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly validationService: ValidationService,
    private readonly userService: UserService,
    private facilityConfig: FacilityConfigService,
    private masterDataService: MasterDataService
  ) {
    this.usersForm = this.fb.group({
      username: [null, [Validators.required, Validators.pattern("^\\S*$")]],
      roleId: [null, Validators.required],
      password: [null],
      confirm_password: [null],
      deactivateStatus: ["false", Validators.required],
      default_location: [null],
      locations: [null],
      job_types: [],
      departments: [],
      reason: [''],
      approval_status: ['']
    });
    this.getLocations();
  }

  ngOnInit() {
    this.getRole();
    // this.checkUserNamechanges();
    this.isladpUser();
    this.getSubJobCategory();
    this.getDepartments();
    this.checkISldap();
    if (this.locationsList) {
      this.filteredLocations.next(this.locationsList.slice());
      this.locationFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.filterLocations();
        });
    }
    if (this.jobTypeList) {
      this.filteredJobType.next(this.jobTypeList.slice());
      this.JobTypeFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.filterJobTypes();
        });
    }
    if (this.departmentList) {
      this.filteredDepartment.next(this.departmentList.slice());
      this.departmentFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.filterDepartments();
        });
    }

    this.dropdownSettings = {
      singleSelection: false,
      idField: 'location_id',
      textField: 'location_name',
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      showSelectedItemsAtTop: true
    };
    this.dropdownJobsSettings = {
      singleSelection: false,
      idField: 'sj_category_id',
      textField: 'sj_category_name',
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      showSelectedItemsAtTop: true
    };
    this.dropdownDeptSettings = {
      singleSelection: false,
      idField: 'department_id',
      textField: 'department_name',
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      showSelectedItemsAtTop: true
    };
  }

  onItemSelect(item: any) {
    // console.log(item);
  }
  onSelectAll(items: any) {
    // console.log(items);
  }

  checkUserNamechanges() {
    this.usersForm.get("username").valueChanges.subscribe(() => {
      Object.keys(this.usersForm.getRawValue()).forEach((control) => {
        if (control !== "username") {
          if (control !== "deactivateStatus") {
            this.usersForm.get(control).reset();
          }
          this.usersForm.get(control).disable();
        }
      });
    });
  }

  isladpUser() {
    this.userService.isldap.subscribe((res: boolean) => {
      this.isldap = res;
    });
  }

  checkUser() {
    if (this.usersForm.get("username").valid) {
      this.userService
        .checkUserName(this.usersForm.get("username").value)
        .subscribe(
          (res) => {
            if (!res.DBStatus) {
              if (
                (res.ADStatus && !res.DBStatus && this.isldap) ||
                (!res.ADStatus && !res.DBStatus && !this.isldap)
              ) {
                Object.keys(this.usersForm.getRawValue()).forEach((control) => {
                  if (control !== "username") {
                    this.usersForm.get(control).enable();
                  }
                });
              } else {
                this.toastr.error(
                  `${this.isldap ? res.ADMessage : res.DBMessage}`,
                  "Error"
                );
              }
            } else {
              this.toastr.error("User already exsist", "Error");
            }
          },
          (err) => {
            console.log(err);
          }
        );
    }
  }

  checkISldap() {
    this.userService.checkIsLDAP().subscribe((res) => {
      this.adStatus = res;
      this.adStatusShowPassword = this.adStatus.ad_status ? false : true;
      const { confirm_password, password } = this.usersForm.controls;
      if (this.adStatus.ad_status) {
        confirm_password.setValidators([]);
        password.setValidators([]);
        this.usersForm.setValidators([]);
      } else {
        confirm_password.setValidators([Validators.required]);
        password.setValidators([Validators.required]);
        this.usersForm.setValidators(this.validationService.matchPassword);
      }
    });
  }

  getRole() {
    this.userService.getRoles().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.roles = res;
    });
  }

  passwordConfirmCheck() {
    if (
      this.usersForm.get("password").value ===
      this.usersForm.get("confirm_password").value
    ) {
      this.usersForm.get("confirm_password").clearValidators();
      this.usersForm.get("confirm_password").updateValueAndValidity();
    }
  }

  getSingleUser(type?) {
    if (this.activatedRoute.snapshot.params.id) {
      this.usersForm.get("username").disable();
      this.usersForm.get("username").clearValidators();
      this.usersForm.get("username").updateValueAndValidity();
      this.userService
        .getUserbyId(this.activatedRoute.snapshot.params.id)
        .subscribe((res) => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          res.deactivateStatus = res.deactivateStatus ? "true" : "false";
          this.usersForm.patchValue(res);
          this.userData = res;
          if (this.activatedRoute.snapshot.params.id) {
            const filteredData = {
              locations: [],
              job_types: [],
              departments: []
            }
            this.locationsList.filter((loc) => {
              this.userData.locations.some(data => {
                if (loc.location_id == data) {
                  filteredData.locations.push(loc)
                }
              })
            })
            this.jobTypeList.filter((job) => {
              this.userData.job_types.some(data => {
                if (job.sj_category_id == data) {
                  filteredData.job_types.push(job)
                }
              })
            })
            this.departmentList.filter((dept) => {
              this.userData.departments.some(data => {
                if (dept.department_id == data) {
                  filteredData.departments.push(dept)
                }
              })
            })
            this.usersForm.get('job_types').setValue([]);
            this.usersForm.get('locations').setValue([]);
            this.usersForm.get('departments').setValue([]);
            this.usersForm.get('job_types').patchValue(filteredData.job_types)
            this.usersForm.get('locations').patchValue(filteredData.locations)
            this.usersForm.get('departments').patchValue(filteredData.departments)
          }
          if (type == 'reset') {
            this.usersForm.get('job_types').setValue([]);
            this.usersForm.get('locations').setValue([]);
            this.usersForm.get('departments').setValue([]);
          }
          Object.keys(this.usersForm.value).forEach((cont) => {
            if (cont !== "username") {
              this.usersForm.get(cont).enable();
            }
          });
        });
    }
  }

  saveUser(actiontype) {
    const apiData = {
      confirm_password: this.usersForm.getRawValue().confirm_password,
      deactivateStatus: this.usersForm.getRawValue().deactivateStatus,
      default_location: this.usersForm.getRawValue().default_location,
      departments: this.usersForm.getRawValue().departments ? [...this.usersForm.getRawValue().departments.map(item => item.department_id)] : [],
      job_types: this.usersForm.getRawValue().job_types ? [...this.usersForm.getRawValue().job_types.map(item => item.sj_category_id)] : [],
      locations: this.usersForm.getRawValue().locations ? [...this.usersForm.getRawValue().locations.map(item => item.location_id)] : [],
      password: this.usersForm.getRawValue().password,
      roleId: this.usersForm.getRawValue().roleId,
      username: this.usersForm.getRawValue().username,
      reason: this.usersForm.getRawValue().reason,
      approval_status: this.usersForm.getRawValue().approval_status
    }
    if (!this.usersForm.getRawValue().username) {
      delete (apiData.username);
    }
    if (!this.usersForm.get("roleId").disabled) {
      if (this.usersForm.valid) {
        if (apiData.approval_status && apiData.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.addUpadteRoleUserSTff(apiData, actiontype);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.addUpadteRoleUserSTff(apiData, actiontype);
        }
      } else {
        this.toastr.warning(
          "Please enter all highlighted fields",
          "Validation failed!"
        );
        this.usersForm.markAllAsTouched();
      }
    } else {
      this.toastr.warning(
        "Please check user availability",
        "Validation failed!"
      );
    }
  }

  addUpadteRoleUserSTff(apiData, actiontype) {
    if (this.usersForm.value.locations && this.usersForm.value.locations.includes('All')) {
      apiData.locations = this.loc;
    }
    if (this.usersForm.value.job_types && this.usersForm.value.job_types.includes('All')) {
      apiData.job_types = this.jobs;
    }
    if (this.usersForm.value.departments && this.usersForm.value.departments.includes('All')) {
      apiData.departments = this.dept;
    }
    const userId = Number(this.activatedRoute.snapshot.params.id);
    this.userService
      .addUpadteRoleUserSTff(
        apiData,
        actiontype === "save"
          ? "api/users/add"
          : `api/users/edit/${userId}`,
        actiontype
      )
      .subscribe(
        (res) => {
          localStorage.setItem(
            "locationId",
            this.usersForm.get("default_location").value
          );
          this.location.back();
          this.toastr.success(
            `Successfully ${actiontype === "save" ? "added" : "updated"
            } user`,
            "Success"
          );
        },
        (err) => {
          console.log(err);
        }
      );
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }
  async getLocations() {
    // const allLoc = [{'location_id':'All','location_name': 'All'}]
    // this.locationsList.push(allLoc);
    await this.facilityConfig.getLocations().subscribe((res) => {
      if (res) {
        res =
          (res &&
            res.length &&
            res.filter((val) => {
              if (val.status) {
                return val;
              }
            })) ||
          [];
        res.filter(data => {
          this.locationsList.push(data);
          this.loc.push(data.location_id)
        })
        this.locationsFilteredList = this.locationsList;
        this.getSingleUser();
        this.filterLocations();
        this.filteredLocations.next(this.locationsList.slice());
        this.locationFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.filterLocations();
          });
      }
    });
  }

  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  filterJobTypes() {
    if (!this.jobTypeList) {
      return;
    }
    // get the search keyword
    let search = this.JobTypeFilterCtrl.value;
    if (!search) {
      this.filteredJobType.next(<any[]>this.jobTypeList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredJobType.next(
      this.jobTypeList.filter(
        (x) => x.sj_category_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  filterDepartments() {
    if (!this.departmentList) {
      return;
    }
    // get the search keyword
    let search = this.departmentFilterCtrl.value;
    if (!search) {
      this.filteredDepartment.next(<any[]>this.departmentList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredDepartment.next(
      this.departmentList.filter(
        (x) => x.department_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getDepartments() {
    this.userService.getDepartments().subscribe((res) => {
      if (res) {
        // this.departmentList = res;
        res.filter(data => {
          this.departmentList.push(data);
          this.dept.push(data.department_id)
        })
        this.departmentsFilteredList = this.departmentList;
        this.filterDepartments();
        this.filteredDepartment.next(this.departmentList.slice());
        this.departmentFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.filterDepartments();
          });
      }
    });
  }
  getSubJobCategory() {
    this.masterDataService.getSubJobCategory().subscribe(res => {
      if (res) {
        res =
          (res.length &&
            res.filter((val) => {
              if (val.status) {
                return val;
              }
            })) ||
          [];
        // this.jobTypeList = res || [];
        res.filter(data => {
          this.jobTypeList.push(data);
          this.jobs.push(data.sj_category_id)
        })
        this.jobTypesFilteredList = this.jobTypeList
        this.filterJobTypes();
        this.filteredJobType.next(this.jobTypeList.slice());
        this.JobTypeFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.filterJobTypes();
          });
      }
    })
  }

  ngOnDestroy() {
    // this.userService.isldap.unsubscribe();
  }
}
