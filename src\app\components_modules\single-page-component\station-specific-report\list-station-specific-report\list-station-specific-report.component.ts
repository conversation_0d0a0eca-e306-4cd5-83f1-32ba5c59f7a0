import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { StationSpecificReport } from "src/app/models/stationSpecificReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import * as moment from "moment";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-list-station-specific-report",
  templateUrl: "./list-station-specific-report.component.html",
  styleUrls: [
    "./list-station-specific-report.component.scss",
    "../../../../scss/table.scss",
  ],
})
export class ListStationSpecificReportComponent implements OnInit {
  routes = [];
  displayedColumns: string[] = ["name", "count"];
  dataSource = new MatTableDataSource<StationSpecificReport>();
  stationSpecificReport: FormGroup;
  reports = [
    { label: "Transportation Type Report", value: "Transportation" },
    { label: "Staff Productivity Report", value: "StaffProductivity" },
    { label: "Departmentwise Report", value: "Departmentwise" },
  ];
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  exportResult = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  isExcelClicked: any;
  pdfData = [];
  constructor(
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly reportService: ReportsService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }

  ngOnInit() {
    this.getCurrentDateTime();
  }

  createForm() {
    this.stationSpecificReport = this.fb.group({
      report_type: ["Transportation", Validators.required],
      from_date: [this.month, Validators.required],
      to_date: [this.today, Validators.required],
      pageNo: [null],
      pageSize: [null]
    });
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.today = new Date().toISOString();
      this.prevMonth = moment(this.today).set({'date':1})
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.getSpecificReportpor();
    });
  }

  getSpecificReportpor(type?) {
    // const pageData = {
    //   pageNo: 1,
    //   pageSize: 50
    // }
    this.reportService
      .getSpecificPorterReports(
        this.stationSpecificReport.get("report_type").value,
        this.stationSpecificReport.value
      )
      .subscribe((res) => {
        if (type) {
          this.exportResult = res;
          this.mapTableDataforReport(this.exportResult);
        } else {
          this.dataSource = new MatTableDataSource(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.setPageSizeOptions();
        }
      });
  }

  reset() {
    this.stationSpecificReport.get("report_type").setValue("Transportation");
    this.stationSpecificReport.get("from_date").setValue(this.month);
    this.stationSpecificReport.get("to_date").setValue(this.today);
    this.getSpecificReportpor();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "stationSpecificTable",
        "stationSpecificTable_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.getSpecificReportpor('export');
      // TableUtil.exportToExcel(
      //   "stationSpecificTable",
      //   "stationSpecificTable_list"
      // );
    }
  }

  mapTableDataforReport(exportResult) {
    let mappedData;
    let exportObj: any[]=[];
    for(var i=0;i<exportResult.length;i++)
    {
      mappedData = {
        'Name': exportResult[i].name,
        'Count': exportResult[i].count,
      };
      exportObj.push(mappedData);
    }
    TableUtil.exportArrayToExcelDynamically(exportObj, "stationSpecificTable_list");
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Name: key.name,
              Count: key.count,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
  getDate() {
    if (
      this.stationSpecificReport.get("from_date").value &&
      this.stationSpecificReport.get("to_date").value
    ) {
      if (
        this.stationSpecificReport.get("from_date").value >=
        this.stationSpecificReport.get("to_date").value
      ) {
        this.stationSpecificReport.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
