import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MessageStaffRoutingModule } from './message-staff-routing.module';
import { ListMessageStaffComponent } from './list-message-staff/list-message-staff.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddMessageStaffComponent } from './add-message-staff/add-message-staff.component';


@NgModule({
  declarations: [ListMessageStaffComponent, AddMessageStaffComponent],
  imports: [
    CommonModule,
    MessageStaffRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class MessageStaffModule { }
