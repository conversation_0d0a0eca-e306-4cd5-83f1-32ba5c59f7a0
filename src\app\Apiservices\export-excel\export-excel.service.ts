import { Injectable } from '@angular/core';

import * as Excel from "exceljs/dist/exceljs.min.js"
import * as fs from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class ExportExcelService {

  constructor() { }

  async generateExcel(data, name) {
    console.log("data", data);

    const TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Departments');
    const headerRow = worksheet.addRow(data.header);
    headerRow.eachCell((cell, number) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFF7D7D' },
        bgColor: { argb: 'FF000000' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    });

    data.values.forEach(d => {
      worksheet.addRow(d);
    });

    workbook.xlsx.writeBuffer().then((data: any) => {
      const blob = new Blob([data], { type: TYPE });
      fs.saveAs(blob, `${name}.xlsx`);
    });

  }

}
