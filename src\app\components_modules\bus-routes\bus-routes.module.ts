import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BusRoutesRoutingModule } from './bus-routes-routing.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ListBusRoutesComponent } from './list-bus-routes/list-bus-routes.component';
import { AddUpdateBusRoutesComponent } from './add-update-bus-routes/add-update-bus-routes.component';


@NgModule({
  declarations: [ListBusRoutesComponent, AddUpdateBusRoutesComponent],
  imports: [
    CommonModule,
    BusRoutesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class BusRoutesModule { }
