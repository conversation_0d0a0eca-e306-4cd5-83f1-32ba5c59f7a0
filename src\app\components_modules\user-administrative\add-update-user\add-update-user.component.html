<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">Add User</p>
                  <p *ngIf="activatedRoute.snapshot.params.id">Update User</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="usersForm">
                    <fieldset class="">
                      <legend></legend>
                      <div class="row">
                        <div class="col-2" *ngIf="!activatedRoute.snapshot.params.id"></div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>User Name
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput autocomplete="off" placeholder="User Name" formControlName="username" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.username" [fieldName]="'User Name'"
                                [fieldType]="'enter'" [completeError]="'Username cannot have spaces'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <!-- <div class="col-2" *ngIf="!activatedRoute.snapshot.params.id">
                                                    <button mat-raised-button type="button"
                                                        class="btn btn-primary check-user" (click)="checkUser()">Check
                                                        User </button>
                                                </div> -->
                        <div class="col-12" *ngIf="!activatedRoute.snapshot.params.id">
                          <br />
                          <mat-divider></mat-divider>
                          <br />
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Role Name<span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="roleId">
                              <mat-option *ngFor="let rolename of roles" [value]="rolename.role_id">{{
                                rolename.role_name }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.roleId" [fieldName]="'Role Name'"
                                [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6" *ngIf="adStatusShowPassword">
                          <mat-form-field>
                            <mat-label>Password
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput type="password" autocomplete="off" placeholder="Password"
                              formControlName="password" [type]="hidePassword ? 'password' : 'text'" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.password" [fieldName]="'Password'"
                                [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                            <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword"
                              [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hidePassword">
                              <mat-icon>{{
                                hidePassword ? "visibility_off" : "visibility"
                                }}</mat-icon>
                            </button>
                          </mat-form-field>
                        </div>
                        <div class="col-6" *ngIf="adStatusShowPassword">
                          <mat-form-field>
                            <mat-label>Confirm Password
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput type="password" autocomplete="off" (keyup)="passwordConfirmCheck()"
                              placeholder="Confirm Password" formControlName="confirm_password"
                              [type]="hideConfirmPassword ? 'password' : 'text'" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.confirm_password"
                                [fieldName]="'Confirm Password'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                            <button mat-icon-button matSuffix (click)="
                                hideConfirmPassword = !hideConfirmPassword
                              " [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hideConfirmPassword">
                              <mat-icon>{{
                                hideConfirmPassword
                                ? "visibility_off"
                                : "visibility"
                                }}</mat-icon>
                            </button>
                          </mat-form-field>
                        </div>
                        <!-- /////////////////////////////////// -->
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Location </mat-label>
                            <mat-select #singleSelect formControlName="default_location"
                              [disableOptionCentering]="true">
                              <mat-option *ngFor="let location of locationsList" [value]="location.location_id">
                                {{ location.location_name }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.default_location"
                                [fieldName]="'Location'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Reason</mat-label>
                            <input matInput autocomplete="off" placeholder="Reason" formControlName="reason" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="usersForm.controls.reason" [fieldName]="'Reason'"
                                [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <!-- ///////////////////////////// -->
                        <div class="col-6 text-left">
                          <br />
                          <mat-radio-group formControlName="deactivateStatus">
                            <mat-radio-button class="example-margin" value="false">Active
                            </mat-radio-button>
                            <mat-radio-button class="example-margin" value="true">
                              Inactive</mat-radio-button>
                          </mat-radio-group>
                          <mat-error class="pull-left error-css">
                            <app-error-message [control]="usersForm.controls.deactivateStatus" [fieldName]="'Status'"
                              [fieldType]="'select'">
                            </app-error-message>
                          </mat-error>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-12">
                          <fieldset class="scheduler-border">
                            <legend class="scheduler-border">View Status Section</legend>
                            <div class="row">
                              <div class="col-6">
                                <ng-multiselect-dropdown name="locations" [placeholder]="'Select Location'"
                                  [settings]="dropdownSettings" [data]="locationsFilteredList"
                                  formControlName="locations" (onSelect)="onItemSelect($event)"
                                  (onSelectAll)="onSelectAll($event)">
                                </ng-multiselect-dropdown>
                                <!-- <mat-form-field>
                                  <mat-label
                                  >Locations<span class="error-css"></span>
                                  </mat-label>
                                  <mat-select
                                  formControlName="locations"
                                  (ngModelChange)="usersForm.controls.locations"
                                  [(value)]="selectedLoc"
                                  multiple="true">
                                  <mat-option>
                                    <ngx-mat-select-search
                                      [formControl]="locationFilterCtrl"
                                      [placeholderLabel]="'Find Location...'"
                                      [noEntriesFoundLabel]="'no matching location found'">
                                    </ngx-mat-select-search>
                                  </mat-option>
                                  <mat-option #allSelected (click)="toggleAllSelection('location')" [value]="'All'">All</mat-option>
                                  <mat-option
                                    *ngFor="let data of filteredLocations | async"
                                    [value]="data.location_id" (click)="tosslePerOne(allSelected.viewValue)">
                                    {{ data.location_name }}
                                  </mat-option>
                                  </mat-select>
                                  <mat-error class="pull-left error-css">
                                  <app-error-message
                                    [control]="usersForm.controls.locations"
                                    [fieldName]="'Locations'"
                                    [fieldType]="'enter'">
                                  </app-error-message>
                                  </mat-error>
                                </mat-form-field> -->
                              </div>
                              <div class="col-6">
                                <ng-multiselect-dropdown name="job_types" [placeholder]="'Select Job Types'"
                                  [settings]="dropdownJobsSettings" [data]="jobTypesFilteredList"
                                  formControlName="job_types" (onSelect)="onItemSelect($event)"
                                  (onSelectAll)="onSelectAll($event)">
                                </ng-multiselect-dropdown>
                                <!-- <mat-form-field>
                                  <mat-label
                                    >Job Types<span class="error-css"></span>
                                  </mat-label>
                                  <mat-select
                                    formControlName="job_types"
                                    multiple="true"
                                    (ngModelChange)="usersForm.controls.job_types"
                                    [(value)]="selectedJob">
                                    <mat-option>
                                      <ngx-mat-select-search
                                        [formControl]="JobTypeFilterCtrl"
                                        [placeholderLabel]="'Find Job Type...'"
                                        [noEntriesFoundLabel]="'no matching job type found'">
                                      </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option #allSelected (click)="toggleAllSelection('job')" [value]="'All'">All</mat-option>
                                    <mat-option
                                      *ngFor="let data of filteredJobType | async"
                                      [value]="data.sj_category_id" (click)="tosslePerOne(allSelected.viewValue)">
                                      {{ data.sj_category_name }}
                                    </mat-option>
                                  </mat-select>
                                  <mat-error class="pull-left error-css">
                                    <app-error-message
                                      [control]="usersForm.controls.job_types"
                                      [fieldName]="'Job Types'"
                                      [fieldType]="'enter'">
                                    </app-error-message>
                                  </mat-error>
                                </mat-form-field> -->
                              </div>
                            </div>
                          </fieldset>
                        </div>
                        <div class="col-12">
                          <fieldset class="scheduler-border">
                            <legend class="scheduler-border">Bus Route Section</legend>
                            <div class="row">
                              <div class="col-6">
                                <ng-multiselect-dropdown name="departments" [placeholder]="'Select Departments'"
                                  [settings]="dropdownDeptSettings" [data]="departmentsFilteredList"
                                  formControlName="departments" (onSelect)="onItemSelect($event)"
                                  (onSelectAll)="onSelectAll($event)">
                                </ng-multiselect-dropdown>
                                <!-- <mat-form-field>
                                  <mat-label
                                    >Departments<span class="error-css"></span>
                                  </mat-label>
                                  <mat-select
                                    formControlName="departments"
                                    multiple="true"
                                    (ngModelChange)="usersForm.controls.departments"
                                    [(value)]="selectedDepartment">
                                    <mat-option>
                                      <ngx-mat-select-search
                                        [formControl]="departmentFilterCtrl"
                                        [placeholderLabel]="'Find Department...'"
                                        [noEntriesFoundLabel]="'no matching department found'">
                                      </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option #allSelected (click)="toggleAllSelection('department')" [value]="'All'">All</mat-option>
                                    <mat-option
                                      *ngFor="let data of filteredDepartment | async"
                                      [value]="data.department_id" (click)="tosslePerOne(allSelected.viewValue)">
                                      {{ data.department_name }}
                                    </mat-option>
                                  </mat-select>
                                  <mat-error class="pull-left error-css">
                                    <app-error-message
                                      [control]="usersForm.controls.departments"
                                      [fieldName]="'Departments'"
                                      [fieldType]="'enter'">
                                    </app-error-message>
                                  </mat-error>
                                </mat-form-field> -->
                              </div>
                            </div>
                          </fieldset>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="saveUser('save')">
                        Submit
                      </button>
                      <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="saveUser('update')">
                        Update
                      </button>
                      <button mat-raised-button type="button" (click)="
                          usersForm.reset();
                          usersForm.controls.deactivateStatus.setValue('false');
                          getSingleUser('reset')
                        " class="btn btn-white pull-right">
                        Reset
                      </button>
                    </div>
                  </div>
                  <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                        Edit Logs
                    </legend>
                    <div class="row">
                        <div class="col-12">
                            <label class="col-4">Edited By</label>
                            <label class="col-4">Status</label>
                            <label class="col-4">Edited On</label>
                        </div>
                    </div>
                    <div class="row" *ngFor="let log of editLogs">
                        <div class="col-4">
                            {{log.edited_by}}
                        </div>
                        <div class="col-4">
                          {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                        </div>
                        <div class="col-4">
                            {{log.edited_date | localDateConversion: "full"}}
                        </div>
                    </div>
                </fieldset> -->
                  <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                      Audit Logs
                    </legend>
                    <div class="mb-8">
                      <div class="logs-header row">
                        <div style="max-width: 150px;min-width: 150px;" class="text-center">User Name</div>
                        <div class="col-1 text-center">Status</div>
                        <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                        <div class="col-2 text-center">Request By</div>
                        <div class="col-2 text-center">Request Date</div>
                        <div class="col-2 text-center">Approved By</div>
                        <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                      </div>
                    </div>                    <div class="" *ngFor="let log of editLogs; let i = index">
                      <div class="card card-xl-stretch">
                        <div class="card-body card-body-log pt-2 row m-2">
                          <div style="max-width: 150px;min-width: 150px;" class="text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                          <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                            'In-Active'}}</div>
                          <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approvalStatus || '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                          </div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                            (log?.formattedCreatedDate) : '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                          </div>
                          <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approved_Date ?
                            (log?.formattedApprovedDate) : '--'}}</div>
                        </div>
                      </div>
                      <span *ngIf="i !== editLogs.length-1" style='font-size:35px;' class="text-muted">&#8593;</span>
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>