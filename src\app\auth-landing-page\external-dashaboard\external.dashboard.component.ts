import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { InputValidationService } from 'src/app/Apiservices/inputValidation/input-validation.service';
import { interval, Subscription } from 'rxjs';
import { StorageService } from '../../Apiservices/stoargeService/storage.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: './external.dashboard.component.html',
  styleUrls: ['./external.dashboard.component.scss']
})
export class ExternalDashboardComponent implements OnInit {
  subscription: Subscription;
  intervalId: number;

  constructor(
    private readonly userService: UserService,
    private readonly fb: FormBuilder,
    private readonly storageService: StorageService,
    public inputValidationService: InputValidationService
  ) {
    this.createForm();
  }

  ngOnInit() {
    // This is METHOD 1
    const source = interval(60000);
    //const text = 'Your Text Here';
    this.subscription = source.subscribe(val => this.opensnack());
  }
  opensnack() {
    //alert(text);
  }

  ngOnDestroy() {
    // For method 1
    this.subscription && this.subscription.unsubscribe();
  }
  createForm(active?: any) {
    const ls = btoa(JSON.stringify(localStorage));
    const globalToken = this.storageService.getData('access_token');
    
    // window.open("${environment.base_url}"+globalToken+"", "_blank");
    window.open(`${environment.dashboard_url}${globalToken}`, "_blank");
  }
}



