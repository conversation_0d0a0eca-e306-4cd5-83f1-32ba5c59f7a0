<div class="main-content">
  <div class="container-fluid">
    <app-summary-menu-list [routes]="routes"></app-summary-menu-list>
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li style="margin-left: 1%; line-height: 32px">
                  <p>View Equipment Move</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row spacing">
                <div class="col-3">
                  <div class="col">
                    <mat-form-field>
                      <input
                        matInput
                        placeholder="Search..."
                        #filter
                        (keydown)="applyFilter($event.target.value)"
                      />
                      <mat-icon matSuffix>search</mat-icon>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-2">
                  <button
                    class="btn btn-sm btn-default pull-left"
                    (click)="filter.value = ''; applyFilter(filter.value)"
                  >
                    <em class="fa fa-minus-square-o"></em>Reset
                  </button>
                </div>
              </div>

              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="equipmentReportForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Equipment Move
                      </legend>
                      <div class="row">
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="equipmentReportForm.controls.from_date"
                            [fieldName]="FromDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-3">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="equipmentReportForm.controls.to_date"
                            [fieldName]="ToDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-8"></div>
                        <div class="col-4">
                          <button
                            mat-raised-button
                            (click)="
                              equipmentSearch(month, today);
                              equipmentReportForm
                                .get('from_date')
                                .setValue(month);
                              equipmentReportForm
                                .get('to_date')
                                .setValue(today);
                              filter.value = ''
                            "
                            class="btn btn-white pull-right"
                          >
                            Reset
                          </button>
                          <button
                            mat-raised-button
                            type="submit"
                            class="btn btn-primary pull-right"
                            (click)="
                              equipmentSearch(
                                equipmentReportForm.get('from_date').value,
                                equipmentReportForm.get('to_date').value
                              )
                            "
                          >
                            Search
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
                <div class="col-md-12 equipmentReport">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table mat-table [dataSource]="dataSource" matSort>
                      <caption></caption>
                      <ng-container matColumnDef="Equipment">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Equipment
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.Equipment }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="NPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.NPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="NNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Non Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.NNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="NormalTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.NormalTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="EPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.EPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="ENPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Non Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.ENPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="EmergencyTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.EmergencyTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="APM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.APM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="ANPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Non Patient Move
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.ANPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="AdvanceTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.AdvanceTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="header-row-zero-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="1"
                        ></th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-first-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Normal
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-second-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Emergency
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-third-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Advance
                        </th>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="[
                          'header-row-zero-group',
                          'header-row-first-group',
                          'header-row-second-group',
                          'header-row-third-group'
                        ]"
                      ></tr>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-multiheaderpdf
  [pdfData]="pdfData"
  [title]="'Equipment Move'"
  [eq]="true"
></app-multiheaderpdf>
