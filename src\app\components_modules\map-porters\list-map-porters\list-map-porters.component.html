<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <!-- <button mat-raised-button color="primary" routerLink="./addmapporter">Add
                                    Map ON/OFF Porter </button> -->
              </p>
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>View Map ON/OFF Porters</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <app-add-update-assign-porter
                    [hide]="false"
                    (triggerListMapPorter)="triggerMapPorterList($event)"
                    [refreshApis]="refreshApis"
                  >
                  </app-add-update-assign-porter>
                  <div class="col-12">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-3">
                          <div class="col">
                            <mat-form-field>
                              <input
                                matInput
                                placeholder="Search..."
                                #filter
                                (keydown)="applyFilter($event.target.value)"
                              />
                              <mat-icon matSuffix>search</mat-icon>
                            </mat-form-field>
                          </div>
                        </div>
                        <div class="col-2">
                          <button
                            class="btn btn-sm btn-default pull-left"
                            (click)="
                              filter.value = ''; applyFilter(filter.value)
                            "
                          >
                            <em class="fa fa-minus-square-o"></em>Reset
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </div>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="mapportertable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>

                      <ng-container matColumnDef="porter_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Staff Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.porter_name }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="phone_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Staff Mobile
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.phone_no }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="assigned_mobile">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Assigned mobile
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row?.assigned_no }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="holding_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Holding time(mints)
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.holding_time }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="porter_type">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Staff Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.porter_type }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="break_on">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          style="margin: 0 auto"
                        >
                          Break ON
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            *ngIf="!row.break_on; else breakonstart"
                            href="javascript:void(0);"
                            (click)="openDialogAction(row.id);"
                            >Start</a
                          >
                          <ng-template #breakonstart>{{
                            row.break_on
                          }}</ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="break_off">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          style="margin: 0 auto"
                        >
                          Break OFF
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <a
                            *ngIf="row.break_on; else breakonend"
                            href="javascript:void(0);"
                            (click)="break(row.id, 'off')"
                            >End</a
                          >
                          <ng-template #breakonend>NA</ng-template>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="total_break_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Total Break Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.total_break_time }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="station_location">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          style="margin: 0 auto"
                        >
                          Location
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.station_location ? row.station_location : "NA"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="delete_id">
                        <th id="" mat-header-cell *matHeaderCellDef>Delete</th>
                        <td mat-cell *matCellDef="let row">
                          <em
                            class="material-icons"
                            style="cursor: pointer"
                            (click)="delete(row.porter_id)"
                            >delete</em
                          >
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="[
    'Staff Name',
    'Staff Mobile',
    'Phone Tag',
    'Holding time(mints)',
    'Staff Type',
    'Break ON',
    'Break OFF',
    'Total Break Time',
    'Location'
  ]"
  [title]="'Map Porters'"
  [datas]="pdfData"
>
</app-table-for-pdf>

<ng-template #dialogContentRef>
  <div mat-dialog-content>
    <div class="row">
      <!-- Header -->
      <div class="col-12">
        <div class="">
          <p class="text-center bold__display">
            Porter Break Time Duration
          </p>
        </div>
      </div>
      <!-- Body -->
      <div class="col-12">
        <div class="border p-3">
          <form [formGroup]="breakTimeModalForm">
            <div class="row">
              <div class="col-12">
                <mat-form-field>
                  <mat-label>Select Duration
                    <span class="error-css"><span class="error-css">*</span></span>
                  </mat-label>
                  <mat-select #singleSelect formControlName="duration">
                    <!-- <mat-option>
              <ngx-mat-select-search [formControl]="staffFilterCtrl" [placeholderLabel]="'Select duration...'"
                [noEntriesFoundLabel]="'no matching duration found'"></ngx-mat-select-search>
            </mat-option> -->
                    <mat-option *ngFor="let data of durationInMins" [value]="data">
                      {{ data }} mins
                    </mat-option>
                  </mat-select>
                  <mat-error class="pull-left error-css">
                    <app-error-message [control]="breakTimeModalForm.controls.duration" [fieldName]="'Duration'" [fieldType]="'select'"></app-error-message>
                  </mat-error>
                  <!-- <mat-hint>Porter name – Porter number</mat-hint> -->
                </mat-form-field>
              </div>
            </div>
          </form>
        </div>
        <div class="row from-submit">
          <div class="col">
            <button mat-raised-button class="btn btn-primary pull-right" (click)="break(tempId, 'on')">
              Submit
            </button>
            <button mat-raised-button class="btn btn-white pull-right" data-dismiss="modal"
              (click)="breakTimeModalForm.reset();onCancelClick();createModalForm()">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>