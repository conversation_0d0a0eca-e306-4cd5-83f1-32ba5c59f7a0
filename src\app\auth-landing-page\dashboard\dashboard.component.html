<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">

                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li class="nav-item">
                                    <!-- <p *ngIf="dateForm.get('active').value !== 'dailyTrue'">Dashboard for
                                        {{inputValidationService.monthConvert(dateForm.get('month').value)}}-{{dateForm.get('year').value}}
                                    </p>
                                    <p *ngIf="dateForm.get('active').value === 'dailyTrue'">Dashboard for
                                        {{dateForm.get('dateInput').value | date}}</p> -->
                                    <p>
                                        <span *ngIf="dateForm.get('active').value !== 'dailyTrue'; else elseBlock">Dashboard for
                                            {{inputValidationService.monthConvert(dateForm.get('month').value)}}-{{dateForm.get('year').value}}
                                        </span>
                                        <ng-template #elseBlock>
                                            Dashboard for
                                            {{inputValidationService.monthConvert(dateForm.value.month)}} {{dateForm.value.day}}, {{dateForm.value.year}}
                                        </ng-template>
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                           <div class="col-12">
                                    <form [formGroup]="dateForm">
                                        <div class="row">
                                            <div class="col-4">
                                                <mat-radio-group formControlName="active"
                                                    (ngModelChange)="selectedOption()">
                                                    <mat-radio-button class="example-margin" value="dailyTrue">Daily
                                                    </mat-radio-button>
                                                    <mat-radio-button class="example-margin" value="monthlyTrue">
                                                        Monthly</mat-radio-button>
                                                </mat-radio-group>
                                            </div>
                                            <div class="col-2" *ngIf="dateForm.get('active').value === 'dailyTrue'">
                                                <app-datep-picker [dateConfiguration]="DateValue"
                                                    (getDate)="selectedCustomDate()"
                                                    [control]="dateForm.controls.dateInput" [fieldType]="'select'">
                                                </app-datep-picker>
                                            </div>
                                            <div class="col-2" *ngIf="dateForm.get('active').value === 'monthlyTrue'">
                                                <mat-form-field>
                                                    <mat-select formControlName="month"
                                                        (ngModelChange)="selectedDateValue()">
                                                        <mat-option *ngFor="let value of months" [value]="value.id">
                                                            {{value.val}}
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                            <div class="col-2" *ngIf="dateForm.get('active').value === 'monthlyTrue'">
                                                <mat-form-field>
                                                    <mat-select formControlName="year"
                                                        (ngModelChange)="selectedDateValue()">
                                                        <mat-option *ngFor="let val of years" [value]="val">{{val}}
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                            <div class="col-2"></div>
                                        </div>
                                    </form>
                                </div>

                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>KPI - Urgent Jobs</h4>
                                        <p>{{kpiResult && kpiResult.urgent_jobs_kpi}} jobs out of
                                            {{kpiResult && kpiResult.urgent_jobs ? kpiResult.urgent_jobs : 0}}</p>
                                        <canvas baseChart width="100" height="70" [data]="demodoughnutChartData1"
                                            [labels]="['KPI - Met', 'KPI - Non Met']" [chartType]="'doughnut'" 
                                            [options]="semidonut" [colors]="doughnutChartColors" ></canvas>
                                           <!--   <p style="margin-top: -30px;">{{kpiVal}}% jobs met KPI</p>-->
                                    </div>
                                    <p style="margin-top: -100px;padding-bottom: 60px;font-size: x-large;">{{kpiVal}}%</p>
                                </div>
                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>KPI - Non Urgent Jobs</h4>
                                        <p>
                                            {{kpiResult && kpiResult.pm_jobs_kpi ? kpiResult.pm_jobs_kpi : '0'}} jobs
                                            out of {{kpiResult && kpiResult.pm_jobs ? kpiResult.pm_jobs : '0'}}</p>
                                        <canvas baseChart width="100" height="70" [data]="demodoughnutChartData2"
                                            [labels]="['KPI - Met', 'KPI - Non Met']" [chartType]="'doughnut'"
                                            [options]="semidonut" [colors]="doughnutChartColors"></canvas>
                                       <!--  <p style="margin-top: -30px;">{{patientmoveVal}}% jobs met KPI</p>-->
                                    </div>
                                    <p style="margin-top: -100px;padding-bottom: 60px;font-size: x-large;">{{patientmoveVal}}%</p>
                                </div>
                               <!-- <div class="col-3 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>KPI - NON Patient Move</h4>
                                        <p>
                                            {{kpiResult && kpiResult.npm_jobs_kpi ? kpiResult.npm_jobs_kpi : '0'}} jobs
                                            out of {{kpiResult && kpiResult.npm_jobs ? kpiResult.npm_jobs : '0'}}</p>
                                        <canvas baseChart width="100" height="100" [data]="demodoughnutChartData3"
                                            [labels]="['KPI - NON Patient Move', 'Unused Jobs']"
                                            [chartType]="'doughnut'" [options]="semidonut"
                                            [colors]="doughnutChartColors"></canvas>
                                        <p style="margin-top: -30px;">{{nonpatientmove}}% jobs met KPI</p>
                                    </div>
                                </div>-->
                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Cancelled Jobs</h4>
                                        <p>
                                            {{kpiResult && kpiResult.cancelled_jobs ? kpiResult.cancelled_jobs : '0'}}
                                            jobs out of
                                            {{kpiResult && kpiResult.total_jobs ? kpiResult.completed_jobs + kpiResult.cancelled_jobs : '0'}}
                                        </p>
                                        <canvas baseChart width="100" height="70" [data]="demodoughnutChartData4"
                                            [labels]="['Cancelled Jobs', 'Non Cancelled Jobs']" [chartType]="'doughnut'"
                                            [options]="semidonut" [colors]="doughnutChartColorsancelC"></canvas>
                                       <!--  <p style="margin-top: -30px;">{{cancelledjobs}}% jobs met </p>-->
                                    </div>
                                    <p style="margin-top: -100px;padding-bottom: 60px;font-size: x-large;">{{cancelledjobs}}%</p>
                                </div>
                            </div>
                            <br><br>
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">
                                    Live Statistics for Today
                                </legend>
                                <div class="row m-3">
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Ongoing Jobs</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.OngoingJobs ? jobporterscountResult.OngoingJobs : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Urgent Jobs</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.UrgentJobs ? jobporterscountResult.UrgentJobs : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Patient Move</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.OngoingJobs ? jobporterscountResult.OngoingJobs : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Non Patient Move</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.ExternalJobs ? jobporterscountResult.ExternalJobs : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Ongoing Jobs</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.OngoingJobs ? jobporterscountResult.OngoingJobs : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Urgent</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.UrgentJobs ? jobporterscountResult.UrgentJobs : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Patient Move</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.PatientMoveJobs ? jobporterscountResult.PatientMoveJobs : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Non Patient Move</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.ExternalJobs ? jobporterscountResult.ExternalJobs : 0}}</p>
                                    </div> </div> -->
                                </div>
                                <div class="row m-3">
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Onduty Porters</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.OndutyPorters ? jobporterscountResult.OndutyPorters : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Central</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.CPPorters ? jobporterscountResult.CPPorters : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Station</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.SPPorters ? jobporterscountResult.SPPorters : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="card overflow-hidden">
                                            <div class="card-content live-chart-wrapper">
                                                <div class="card-body cleartfix">
                                                    <div class="media align-items-stretch">
                                                        <div class="align-self-center">
                                                            <i class="icon-pencil primary font-large-2 mr-2"></i>
                                                        </div>
                                                        <div class="media-body" style="text-align: left;">
                                                            <h4>Break</h4>
                                                        </div>
                                                        <div class="align-self-center">
                                                            <h1 class="badge badge-info round">
                                                                {{jobporterscountResult && jobporterscountResult.BRPorters ? jobporterscountResult.BRPorters : 0}}
                                                            </h1>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Onduty Porters</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.OndutyPorters ? jobporterscountResult.OndutyPorters : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Central</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.CPPorters ? jobporterscountResult.CPPorters : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper live-chart-wrapper">
                                            <h4>Station</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.SPPorters ? jobporterscountResult.SPPorters : 0}}</p>
                                    </div> </div>
                                    <div class="col-3 chart-card">
                                        <div class="chart-wrapper">
                                            <h4>Break</h4>
                                            <p>{{jobporterscountResult && jobporterscountResult.BRPorters ? jobporterscountResult.BRPorters : 0}}</p>
                                    </div> </div> -->
                                </div>
                            </fieldset>
                            <br><br>
                            <div class="row">
                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Today’s Workload – Porter Type</h4>
                                        <canvas baseChart height="300px" width="300px" [datasets]="barChartData1"
                                            [labels]="['Workload']" [options]="barChartOptions1"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>
                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Workload - Central Pool(Job Priority)</h4>
                                        <canvas baseChart height="300px" width="300px" [datasets]="barChartData2"
                                            [labels]="['Workload']" [options]="barChartOptions2"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>
                                <div class="col-4 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Workload - Job category</h4>
                                        <canvas baseChart height="300px" width="300px" [datasets]="barChartData3" [colors]="lineChartColors"
                                            [labels]="['Job Category']" [options]="barChartOptions3"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>
                               <!-- <div class="col-3 chart-card" *ngIf="dateForm.get('active').value !== 'dailyTrue'">
                                    <div class="chart-wrapper">
                                        <canvas baseChart height="300px" width="300px" [datasets]="barChartData4"
                                            [labels]="['Reasons']" [options]="barChartOptions4"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>-->
                            </div>
                            <br><br>
                            <div class="row">
                                <div class="col-12 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Hourly Job Trend (Priority)</h4>
                                        <canvas baseChart height="50px" [datasets]="barChartDataHourlyBased"
                                            [labels]="barChartLabels" [options]="hourlyJobtrend"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>

                            </div>
                            <br><br>
                            <div class="row">
                                <div class="col-12 chart-card">
                                    <div class="chart-wrapper">
                                        <h4>Hourly Job Trend (Job Category)</h4>
                                        <canvas baseChart height="50px" [datasets]="barChartDataHourlyJobcatBased" [colors]="lineChartColors"
                                            [labels]="barChartJobcatLabels" [options]="hourlyJobCattrend"
                                            [plugins]="barChartPlugins" [legend]="barChartLegend" [chartType]="'bar'">
                                        </canvas>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>