import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListLocationComponent } from './list-location/list-location.component';
import { AddUpdateLocationComponent } from './add-update-location/add-update-location.component';

const routes: Routes = [
  {path: '', component: ListLocationComponent},
  {path: 'addlocation', component: AddUpdateLocationComponent},
  {path: 'updatelocation/:id', component: AddUpdateLocationComponent},
];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LocationsRoutingModule { }
