import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { StorageService } from 'src/app/Apiservices/stoargeService/storage.service';
import { Subject } from 'rxjs';
import { UserService } from 'src/app/Apiservices/userService/user.service';
import { RoutingService } from 'src/app/Apiservices/routingService/routing.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  logoUrl = '';
  isLoading: Subject<boolean> = new Subject<boolean>();
  uniqueName: any = '';
  constructor(
    private readonly router: Router,
    private readonly stoargeService: StorageService,
    private readonly userService: UserService,
    private readonly routingService: RoutingService
  ) { }


  ngOnInit() {
    this.getAdminName();
    this.getLogo();
  }

  getLogo() {
    this.userService.logoUrl.subscribe(res => {
      this.logoUrl = res || '';
    });
  }

  getAdminName() {
    const res = this.routingService.decodeToken();
    this.uniqueName = res.unique_name;
  }

  logout() {
    this.userService.logout().subscribe(() => {
      this.stoargeService.removeAllData();
      this.router.navigateByUrl('/');
    });
  }

}
