import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-transport-modes',
  templateUrl: './add-update-transport-modes.component.html',
  styleUrls: ['./add-update-transport-modes.component.scss']
})
export class AddUpdateTransportModesComponent implements OnInit {
  transportForm: FormGroup;
  icons: any = [];
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly masterDataService: MasterDataService
  ) {
    this.transportForm = this.fb.group({
      department: ['1'],
      transport_mode_name: ['', Validators.required],
      transport_description: ['', Validators.required],
      transport_mode_image: ['', Validators.required],
      status: ['true', Validators.required],
      reason: [''],
      approval_status: ['']
    });
  }
  ngOnInit() {
    this.facilityConfig.getIcons().subscribe(res => {
      this.icons = res;
    });
    this.getTransportbyId();
  }

  getTransportbyId() {
    this.transportForm.get('department').setValue('1');
    if (this.activatedRoute.snapshot.params.id) {
      this.masterDataService.getTransportModeById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          res.transport_mode_image = JSON.parse(res.transport_mode_image);
          res.status = res.status === true ? 'true' : 'false';
          this.transportForm.patchValue(res);
          this.icons.push(res.transport_mode_image);
        });
    }
  }

  saveTransport(action) {
    if (this.transportForm.valid) {
      let transportrId: any;
      const data = this.transportForm.value;
      data.transport_mode_image = JSON.stringify(data.transport_mode_image);
      if (action === 'update') {
        transportrId = Number(this.activatedRoute.snapshot.params.id);
        if (data.approval_status && data.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.addUpdateTransportMode(data, action, transportrId);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.addUpdateTransportMode(data, action, transportrId);
        }
      } else {
        this.addUpdateTransportMode(data, action, transportrId);
      }

    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.transportForm.markAllAsTouched();
    }
  }

  addUpdateTransportMode(data, action, transportrId) {
    this.masterDataService.addUpadteTransportMode(
      data, action === 'save' ?
      'api/transportmodes/add' : `api/transportmodes/edit/${transportrId}`, action)
      .subscribe(res => {
        this.location.back();
      }, err => {
        console.log(err);
      });
  }

}
