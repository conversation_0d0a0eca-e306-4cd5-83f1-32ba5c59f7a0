import { ApprovalStatusModule } from './../components_modules/approval-status/approval-status.module';
import { NgModule, ModuleWithProviders } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { SmartAssignComponent } from '../components_modules/smart-assign/smart-assign.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { RouteSecurityGuard } from '../Apiservices/auth-load-route/route-security.guard';
import { GridViewSettingsComponent } from '../components_modules/grid-view-settings/grid-view-settings.component';
import { ExternalDashboardComponent } from './external-dashaboard/external.dashboard.component';

const routes: Routes = [
  {
    path: '', component: HomeComponent, children: [

      // medicatiion codes route
      {
        path: "medication-codes",
        data: { data: '/app/medication-codes' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/medication-codes/medication-codes.module').then(m => m.MedicationCodesModule)
      },
      {
        path: "view-status-by-location",
        data: { data: '/app/view-status-by-location' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/view-status-by-location/view-status-by-location.module').then(m => m.ViewStatusByLocationModule)
      },
      {
        path: "view-status-by-job-category",
        data: { data: '/app/view-status-by-job-category' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/view-status-by-job-category/view-status-by-job-category.module').then(m => m.ViewStatusByJobCategoryModule)
      },
      {
        path: "station-view-status",
        data: { data: '/app/station-view-status' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/station-view-status/station-view-status.module').then(m => m.StationViewStatusModule)
      },
      {
        path: 'cannister-id',
        data: { data: '/app/cannister-id' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/cannister-id/cannister-id.module').then(m => m.CannisterIdModule)
      },
      {
        path: 'manage-departments',
        data: { data: '/app/manage-departments' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/manage-departments/manage-departments.module')
            .then(m => m.ManageDepartmentsModule)
      },
      {
        path: 'station-departments',
        data: { data: '/app/station-departments' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/station-departments/station-departments.module')
            .then(m => m.StationDepartmentsModule)
      },
      {
        path: 'cosy-departments',
        data: { data: '/app/cosy-departments' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/cosy-departments/cosy-departments.module')
            .then(m => m.CosyDepartmentsModule)
      },
      {
        path: 'grid-view-settings',
        data: { data: '/app/grid-view-settings' },
        canLoad: [RouteSecurityGuard],
        component: GridViewSettingsComponent
      },
      {
        path: 'towers',
        data: { data: '/app/towers' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/tower/tower.module').then(m => m.TowerModule)
      },
      {
        path: 'levels',
        data: { data: '/app/levels' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/levels/levels.module').then(m => m.LevelsModule)
      },

      {
        path: 'locations',
        data: { data: '/app/locations' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/locations/locations.module').then(m => m.LocationsModule)
      },
      {
        path: 'roles',
        data: { data: '/app/roles' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/roles/roles.module').then(m => m.RolesModule)
      },

      {
        path: 'users',
        data: { data: '/app/users' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/user-administrative/user-administrative.module').then(m => m.UserAdministrativeModule)
      },
      {
        path: 'messagetemplates',
        data: { data: '/app/messagetemplates' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/message-templates/message-templates.module').then(m => m.MessageTemplatesModule)
      },
      {
        path: 'canceltemplates',
        data: { data: '/app/canceltemplates' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/cancel-templates/cancel-templates.module').then(m => m.CancelTemplatesModule)
      },
      {
        path: 'delayreasons',
        data: { data: '/app/delayreasons' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/delay-reasons/delay-reasons.module').then(m => m.DelayReasonsModule)
      },
      {
        path: 'transportmodes',
        data: { data: '/app/transportmodes' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/transport-modes/transport-modes.module').then(m => m.TransportModesModule)
      },
      {
        path: 'colorcodes',
        data: { data: '/app/colorcodes' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/color-codes/color-codes.module').then(m => m.ColorCodesModule)
      },
      {
        path: 'standardtime',
        data: { data: '/app/standardtime' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/standard-time/standard-time.module').then(m => m.StandardTimeModule)
      },
      {
        path: 'categorytransportmapping',
        data: { data: '/app/categorytransportmapping' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/category-mode-transport/category-mode-transport.module').then(m => m.CategoryModeTransportModule)
      },
      {
        path: 'subjobcategory',
        data: { data: '/app/subjobcategory' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/sub-jobcategory/sub-jobcategory.module').then(m => m.SubJobcategoryModule)
      },
      {
        path: 'managestaff',
        data: { data: '/app/managestaff' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/manage-staff/manage-staff.module').then(m => m.ManageStaffModule)
      },
      {
        path: 'mainjobcategory',
        data: { data: '/app/mainjobcategory' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/manage-main-job-category/manage-main-job-category.module').then(m => m.ManageMainJobCategoryModule)
      },
      {
        path: 'managehelpfile',
        data: { data: '/app/managehelpfile' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/manage-help-file/manage-help-file.module').then(m => m.ManageHelpFileModule)
      },


      {
        path: 'mapporters',
        data: { data: '/app/mapporters' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/map-porters/map-porters.module').then(m => m.MapPortersModule)
      },

      {
        path: 'stationtransportMapping',
        data: { data: '/app/stationtransportMapping' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/station-transport-mapping/station-transport-mapping.module').
            then(m => m.StationTransportMappingModule)
      },

      {
        path: 'distance',
        data: { data: '/app/distance' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/distance/distance.module').
            then(m => m.DistanceModule)
      },
      {
        path: 'singlepage',
        data: { data: '/app/singlepage' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/single-page-component/single-page-component.module')
            .then(m => m.SinglePageComponentModule)
      },
      {
        path: 'busroutes',
        data: { data: '/app/busroutes' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/bus-routes/bus-routes.module')
            .then(m => m.BusRoutesModule)
      },
      {
        path: 'jobstatus',
        data: { data: '/app/jobstatus' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/job-requests/job-requests.module')
            .then(m => m.JobRequestsModule)
      },
      {
        path: 'search',
        data: { data: '/app/search' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/job-requests/job-requests.module')
            .then(m => m.JobRequestsModule)
      },
      {
        path: 'jobrequests',
        data: { data: '/app/jobrequests' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/job-requests/job-requests.module')
            .then(m => m.JobRequestsModule)
      },
      {
        path: 'messageStaff',
        data: { data: '/app/messageStaff' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/message-staff/message-staff.module')
            .then(m => m.MessageStaffModule)
      },
      {
        path: 'manageMobile',
        data: { data: '/app/manageMobile' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/manage-mobile/manage-mobile.module')
            .then(m => m.ManageMobileModule)
      },
      {
        path: 'kpiReports',
        data: { data: '/app/kpiReports' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () =>
          import('../components_modules/kpi-reports/kpi-reports.module')
            .then(m => m.KpiReportsModule)
      },
      { 
        path: 'approval',
        data: { data: '/app/approval' },
        canLoad: [RouteSecurityGuard],
        loadChildren: () => import('../components_modules/approval-status/approval-status.module').then(m => m.ApprovalStatusModule)
      },
      { path: 'smartassign', component: SmartAssignComponent },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'externaldashboard', component: ExternalDashboardComponent}
    ]
  },

];

// @NgModule({
//   imports: [RouterModule.forChild(routes)],
//   exports: [RouterModule]
// })
export class AuthLandingPageRoutingModule { }
export const Routing: ModuleWithProviders = RouterModule.forChild(routes);
