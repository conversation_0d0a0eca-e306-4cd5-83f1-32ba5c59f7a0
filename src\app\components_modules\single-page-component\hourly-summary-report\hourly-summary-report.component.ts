import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { FormGroup, FormBuilder } from "@angular/forms";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { HourSummary } from "src/app/models/hourSummary";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { debug } from "console";

@Component({
  selector: "app-hourly-summary-report",
  templateUrl: "./hourly-summary-report.component.html",
  styleUrls: [
    "./hourly-summary-report.component.scss",
    "../../../scss/table.scss",
  ],
})
export class HourlySummaryReportComponent implements OnInit {
  displayedColumns: string[] = [
  ];
  inputColumns: string[] = [
    "Hours",
    "PatientMove",
    "NonPatientMove",
    "Total",
  ];
  displayHeaderColumns = [{
    id: "Hours", value: "Hours"
  },
  { id: "PatientMove", value: "Patient Moves" },
  { id: "NonPatientMove", value: "Non Patient Moves" },
  { id: "Total", value: "Total" }];
  dataSource = new MatTableDataSource<HourSummary>([]);
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  routes = [
    // { path: './../transportReports', label: 'Task Type Summary' },
    // { path: './../locationSearch', label: 'Location Summary' },
    // { path: './../equipmentmove', label: 'Equipment Summary' },
    // { path: './../hourReport', label: 'Hourly Summary' },
    // { path: './../kpiReport', label: 'KPI Report' },
  ];
  weekType = ["weekend", "weekday", "all"];
  status = ["Assigned", "Completed", "In Queue", "Cancelled", "Responded"];
  taskType = [{ Value: "Normal", Text: "Normal" }, { Value: "Emergency", Text: "Urgent" }, { Value: "Advance", Text: "Advance" }];
  pdfData = [];
  hourSummaryForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
  };
  inputData: any;
  displayData: {}[];
  constructor(
    private readonly fb: FormBuilder,
    public inputValidation: InputValidationService,
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
    private readonly toastr: ToastrService,
    private readonly userService: UserService
  ) {
    this.createForm();
  }

  ngOnInit() {

    this.getCurrentDateTime();
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.getHourlyReport();
    });
  }

  createForm() {
    this.hourSummaryForm = this.fb.group({
      from_date: [moment().clone().startOf("month").toDate()],
      to_date: [moment().toDate()],
      day: ["weekday"],
      status: [''],
      task: ['']
    });
  }

  getHourlyReport() {
    const fromdate = moment(this.hourSummaryForm.get("from_date").value).format(
      "YYYY/MM/DD"
    );
    const todate = moment(this.hourSummaryForm.get("to_date").value).format(
      "YYYY/MM/DD"
    );
    var status = this.hourSummaryForm.get("status").value;
    if (status) {
      if (status == 'In Queue') {
        status = 'INQ'
      } else if (status == 'Responded') {
        status = 'Started'
      }
    }
    else {
      status = 'All';
    }
    this.reportsService
      .getSummaryHourlyReport({
        from_date: fromdate,
        to_date: todate,
        day: this.hourSummaryForm.get("day").value,
        status: status,
        task: this.hourSummaryForm.get("task").value,
      })
      .subscribe((res) => {
        res =
          res &&
          res.map((val) => {
            val.Total = val.PatientMove + val.NonPatientMove;
            // val.Hours = this.createHours(val.Hours);
            return val;
          });
        this.inputData = res;
        this.displayedColumns = ['-1'].concat(res.map(x => x.Hours.toString()));
        this.displayData = this.inputColumns.map(x => this.formatInputRow(x));
        this.dataSource = new MatTableDataSource<HourSummary>(res ? res : []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.setPageSizeOptions();
      });
  }
  formatInputRow(row) {
    const output = {};
    output[-1] = row;
    for (let i = 0; i < this.inputData.length; ++i) {
      output[this.inputData[i].Hours] = this.inputData[i][row];
    }

    return output;
  }

  createHours(hour) {
    if (hour === 0) {
      return "12 AM";
    } else if (hour < 12) {
      return `${hour} AM`;
    } else if (hour === 12) {
      return `${hour} PM`;
    } else if (hour > 12) {
      return `${hour - 12} PM`;
    }
  }

  getDisplayHeaderName(str: any) {
    return this.displayHeaderColumns.find(x => x.id === str) ? this.displayHeaderColumns.find(x => x.id === str).value : str;
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "hourSummaryReport",
        "hourSummaryReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("hourSummaryReport", "hourSummaryReport_list");
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Hour: key.Hours,
              "Patient Moves": key.PatientMove,
              "Non Patient Moves": key.NonPatientMove,
              Total: key.Total,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
  getDate() {
    if (
      this.hourSummaryForm.get("from_date").value &&
      this.hourSummaryForm.get("to_date").value
    ) {
      if (
        this.hourSummaryForm.get("from_date").value >=
        this.hourSummaryForm.get("to_date").value
      ) {
        this.hourSummaryForm.get("to_date").setValue("");
        this.toastr.error("To date should be less then From date", "Error");
      }
    }
  }
}
