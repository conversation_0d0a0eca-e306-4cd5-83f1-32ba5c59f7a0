import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CancelTemplatesRoutingModule } from './cancel-templates-routing.module';
import { ListCancelTemplatesComponent } from './list-cancel-templates/list-cancel-templates.component';
import { AddUpdateCancelTemplatesComponent } from './add-update-cancel-templates/add-update-cancel-templates.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListCancelTemplatesComponent, AddUpdateCancelTemplatesComponent],
  imports: [
    CommonModule,
    CancelTemplatesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class CancelTemplatesModule { }
