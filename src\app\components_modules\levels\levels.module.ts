import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LevelsRoutingModule } from './levels-routing.module';
import { ListLevelsComponent } from './list-levels/list-levels.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { AddUpdateLevelComponent } from './add-update-level/add-update-level.component';


@NgModule({
  declarations: [ListLevelsComponent, AddUpdateLevelComponent],
  imports: [
    CommonModule,
    LevelsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class LevelsModule { }
