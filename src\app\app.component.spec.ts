import { TestBed, async } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { JasmineDependencyModule } from './jasmine-dependency/jasmine-dependency.module';
import { BlockTemplateComponent } from './block-ui/block-template.component';

describe('AppComponent', () => {
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        JasmineDependencyModule
      ],
      declarations: [
        AppComponent,
        BlockTemplateComponent
      ],
    }).compileComponents();
  }));

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.debugElement.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have as title 'uems'`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.debugElement.componentInstance;
    expect(app.title).toEqual('uems');
  });

  it('should render title', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();
    const compiled = fixture.debugElement.nativeElement;
    expect(compiled.querySelector('.content span').textContent).toContain('uems app is running!');
  });
});
