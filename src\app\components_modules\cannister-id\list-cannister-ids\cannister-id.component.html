<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right;">
                                <button mat-raised-button color="primary" routerLink="./add-cannister-id"> Add Cannister
                                    Id </button>
                            </p>
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="w-100">
                                    <p>View Cannister Ids</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <div class="row">
                                            <div class="col-3">
                                                <div class="col">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2">
                                                <button class="btn btn-sm  btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="cannisterIdTable" mat-table [dataSource]="dataSource" matSort
                                            class="w-100">
                                            <caption></caption>

                                            <ng-container matColumnDef="Id">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> Id </th>
                                                <td mat-cell *matCellDef="let row"> {{row.Id}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="CannisterId">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> Cannister Id </th>
                                                <td mat-cell *matCellDef="let row"> {{row.CannisterId}} </td>
                                            </ng-container>

                                            <!-- <ng-container matColumnDef="Status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row.Status}}
                                                </td>
                                            </ng-container> -->

                                            <ng-container matColumnDef="cannister_id">
                                                <th id="" mat-header-cell *matHeaderCellDef> Delete </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        (click)="deleteTransport(row)">delete</em>
                                                </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>
<app-table-for-pdf [heads]="['Id', 'CannisterId', 'Status']" [title]="'Cannister Id'" [datas]="pdfData">
</app-table-for-pdf>