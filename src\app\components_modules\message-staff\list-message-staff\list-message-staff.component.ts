import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { MessageStaff } from "src/app/models/messageStaff";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";

@Component({
  selector: "app-list-message-staff",
  templateUrl: "./list-message-staff.component.html",
  styleUrls: [
    "./list-message-staff.component.scss",
    "../../../scss/table.scss",
  ],
})
export class ListMessageStaffComponent implements OnInit {
  displayedColumns: string[] = [
    "staff_name",
    "staff_mobile",
    "message",
    "message_date",
  ];
  dataSource: MatTableDataSource<MessageStaff>;

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  isExcelClicked: any;
  pdfData = [];
  constructor(
    private readonly userService: UserService,
    private readonly loader: LoaderService
  ) {}

  ngOnInit() {
    this.getMessageStaff();
  }

  getMessageStaff() {
    const array = [];
    this.userService.getMessageStaff().subscribe((res) => {
      res.forEach((val) => {
        const t = val.message_date.match("T");
        if (t) {
          val.message_date = val.message_date.replace(t, "  ");
          array.push(val);
        }
      });
      this.dataSource = new MatTableDataSource(array);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "messagestaff",
        "list_messagestaff"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("messagestaff", "list_messagestaff");
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              Staff: key.staff_name,
              "Mobile No": key.staff_mobile,
              Message: key.message,
              "Message Date": key.message_date,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  refreshMessageList(value) {
    if (value) {
      this.getMessageStaff();
    }
  }
}
