<div [class]="dateConfiguration.collumns">
  <mat-form-field>
    <mat-label
      >{{ dateConfiguration.label
      }}<span class="error-css" *ngIf="dateConfiguration.required">*</span>
    </mat-label>
    <input
      matInput
      [readonly]="dateConfiguration.readonly"
      [min]="dateConfiguration.minDateStart"
      #date1
      [max]="dateConfiguration.maxDateStart"
      [matDatepicker]="picker1"
      placeholder="Choose a date"
      [formControl]="control"
      (dateChange)="datePatch(date1.value)"
      (focus)="picker1.open()"
    />
    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
    <mat-datepicker
      #picker1
      [disabled]="dateConfiguration.disabledTimePicker"
    ></mat-datepicker>
    <mat-error class="pull-left error-css">
      <div class="errormsg">
        <div *ngIf="errorMessage !== null">{{ errorMessage }}</div>
      </div>
    </mat-error>
  </mat-form-field>
</div>
<div [class]="dateConfiguration.collumns" *ngIf="dateConfiguration.rangePicker">
  <mat-form-field>
    <input
      matInput
      [min]="dateConfiguration.minDateEnd"
      [max]="dateConfiguration.maxDateEnd"
      [matDatepicker]="picker2"
      placeholder="Choose a date"
    />
    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
    <mat-datepicker #picker2 disabled="false"></mat-datepicker>
  </mat-form-field>
</div>
