import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { WorkLoadByLocation } from '../../../../models/workloadbylocation';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import * as moment from 'moment';

@Component({
  selector: 'app-sp-work-load-by-location',
  templateUrl: './sp-work-load-by-location.component.html',
  styleUrls: ['./sp-work-load-by-location.component.scss', '../../../../scss/table.scss']
})
export class SpWorkLoadByLocationComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = ['item', 'YTDPM', 'YTDNPM', 'YTDTotal', 'JanPM', 'JanNPM', 'JanTotal',
    'FebPM', 'FebNPM', 'FebTotal', 'MarPM', 'MarNPM', 'MarTotal', 'AprPM', 'AprNPM', 'AprTotal', 'MayPM',
    'MayNPM', 'MayTotal', 'JunPM', 'JunNPM', 'JunTotal', 'JulPM', 'JulNPM', 'JulTotal', 'AugPM',
    'AugNPM', 'AugTotal', 'SepPM', 'SepNPM', 'SepTotal', 'OctPM', 'OctNPM', 'OctTotal',
    'NovPM', 'NovNPM', 'NovTotal', 'DecPM', 'DecNPM', 'DecTotal'];
  dates;
  pdfData = [];
  subscription: any;
  today = new Date();
  currentYear = this.today.getFullYear();
  years = [this.currentYear, this.currentYear - 1];
  currMonth = moment(this.today).format('MM');
  currYear = moment(this.today).format('YYYY');
  dataSource = new MatTableDataSource<WorkLoadByLocation>();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  constructor(
    private readonly loader: LoaderService,
    private readonly reportsService: ReportsService,
  ) {
  }

  ngOnInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.filterDtaes();
  }


  getspJobType(year, month) {
    this.reportsService.getkpiReports('splocation', year, month).subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  filterDtaes() {
    this.subscription = this.reportsService.searchDates.subscribe(res => {
      this.dates = res;
      if (res.year && res.month) {
        this.getspJobType(res.year, res.month);
      }
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('stationpoolbylocation', 'stationpoolbylocation');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('stationpoolbylocation', 'stationpoolbylocation');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
