import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from './Apiservices/auth/auth.guard';
import { MenuReolverService } from './resolvers/menu-resolver/menu-reolver.service';
import { WildCardRouteComponent } from './wildCardRoute/wild-card-route/wild-card-route.component';


const routes: Routes = [
  { path: '', redirectTo: '/', pathMatch: 'full' },
  { path: '', loadChildren: () => import('./login/login.module').then(m => m.LoginModule) },
  {
    path: 'app',
    canLoad: [AuthGuard],
    resolve: [MenuReolverService],
    data: {
      allowedRole: 'admin',
    }, loadChildren: () => import('./auth-landing-page/auth-landing-page.module').then(m => m.AuthLandingPageModule)
  },
  { path: '**', component: WildCardRouteComponent}
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
