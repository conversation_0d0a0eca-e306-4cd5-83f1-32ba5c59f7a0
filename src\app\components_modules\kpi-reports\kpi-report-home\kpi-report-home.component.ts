import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from "@angular/core";
import { MatSidenav, MatTabChangeEvent } from "@angular/material";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { RoutingService } from "src/app/Apiservices/routingService/routing.service";
import { ReportMenuStatic } from "src/app/config/report-menu.config";

@Component({
  selector: "app-kpi-report-home",
  templateUrl: "./kpi-report-home.component.html",
  styleUrls: ["./kpi-report-home.component.scss"],
})
export class KpiReportHomeComponent implements OnInit, OnDestroy {
  @ViewChild("sidenav", { static: true }) sidenav: MatSidenav;
  sampleSidenav$: any;
  navLink: any = [];
  tabIndex: number = 2;
  routerSub: any;
  reportMenuSub: any;
  constructor(
    private _facilityConfig: FacilityConfigService,
    public routingService: RoutingService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    // this.routerSub = this.router.events.subscribe((val) => {
    //   // see also
    //   if (val instanceof NavigationEnd) {
    //     this.getReportMenu();
    //   }
    // });
    this.getReportMenu();

    this.router.events.subscribe((val) => {
      // see also
      if (val instanceof NavigationEnd) {
        this.getReportMenu();
      }
    });
  }

  getReportMenu() {
    // this.sampleSidenav$ = this._facilityConfig.fetchReportMenu();
    this.sampleSidenav$ = ReportMenuStatic;
    this.reportMenuSub = ReportMenuStatic.subscribe((data: any) => {
      this.navLink = data.filter(
        (menu) =>
          menu.menu == this.activatedRoute.snapshot.firstChild.data["name"]
      );
      this.navLink = this.navLink !== null ? this.navLink[0].submenu_list : [];
    });
  }
  close(value) {
    this.sidenav.toggle();
  }
  public tabChanged(tabChangeEvent: MatTabChangeEvent): void {
    this.tabIndex = tabChangeEvent.index;
  }
  ngOnDestroy() {
    if (this.routerSub) {
      this.routerSub.unsubscribe();
    }
    if (this.reportMenuSub) {
      this.reportMenuSub.unsubscribe();
    }
  }
}
