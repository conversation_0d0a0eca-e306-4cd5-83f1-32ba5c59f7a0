import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AddManageDepartmetsComponent } from './add-manage-departmets/add-manage-departmets.component';
import { ListManageDepartmetsComponent } from './list-manage-departmets/list-manage-departmets.component';


const routes: Routes = [
  {path: '', component: ListManageDepartmetsComponent},
  {path: 'addmanagedepartments', component: AddManageDepartmetsComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ManageDepartmentsRoutingModule { }
