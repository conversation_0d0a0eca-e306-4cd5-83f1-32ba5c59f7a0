import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DistanceRoutingModule } from './distance-routing.module';
import { ListDistanceComponent } from './list-distance/list-distance.component';
import { AddUpdateDistanceComponent } from './add-update-distance/add-update-distance.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListDistanceComponent, AddUpdateDistanceComponent],
  imports: [
    CommonModule,
    DistanceRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class DistanceModule { }
