import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MessageTemplatesRoutingModule } from './message-templates-routing.module';
import { ListMessageTemplatesComponent } from './list-message-templates/list-message-templates.component';
import { AddUpdateMessageTemplatesComponent } from './add-update-message-templates/add-update-message-templates.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [ListMessageTemplatesComponent, AddUpdateMessageTemplatesComponent],
  imports: [
    CommonModule,
    MessageTemplatesRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class MessageTemplatesModule { }
