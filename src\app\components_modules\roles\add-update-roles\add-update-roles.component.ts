import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { UserService } from 'src/app/Apiservices/userService/user.service';


@Component({
  selector: 'app-add-update-roles',
  templateUrl: './add-update-roles.component.html',
  styleUrls: ['./add-update-roles.component.scss']
})
export class AddUpdateRolesComponent implements OnInit {


  selectedMenu = [];

  towers = {};
  subJobCategories = {};
  rolesForm: FormGroup;
  menuList: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly user: UserService
  ) {
    this.rolesForm = this.fb.group({
      role_name: ['', Validators.required],
      subId_list: [[], Validators.required],
      status: ['true', Validators.required]
    });
  }

  ngOnInit() {
    this.getMenu();
  }

  getRole() {
    if (this.activatedRoute.snapshot.params.id) {
      this.user.getSingleRole(this.activatedRoute.snapshot.params.id).subscribe(res => {
        res.status = res.status === true ? 'true' : 'false';
        this.rolesForm.patchValue(res);
        const selectedRoles = res.subId_list;
        selectedRoles.forEach(val => {
          this.menuList.forEach((submenu, i) => {
            if (submenu.submenu_id === val) {
              this.selectedMenu.push(submenu);
              this.menuList.splice(i, 1);
            }
          });
        });
        this.createMenu();
      });
    } else {
      this.selectedMenu.push(this.menuList[0]);
      this.menuList.splice(0, 1);
      this.createMenu();
    }
  }

  getMenu() {
    this.user.fullMenu().subscribe(res => {
      this.menuList.length = 0;
      const menus = res.filter(menu => {
        if (menu.submenu_list && menu.submenu_list.length !== 0) {
          menu.submenu_list.filter(submenu => {
            menu[`${menu.menu}/ ${submenu.name}`]
              = {
              name: `${menu.menu}/ ${submenu.name}`,
              submenu_id: submenu.id, menu_id: menu.id
            };
          });
          delete menu.submenu_list;
          delete menu.menu;
          delete menu.id;
          return menu;
        }
      });
      menus.forEach(element => {
        Object.keys(element).forEach(menuKeys => {
          this.menuList.push(element[menuKeys]);
        });
      });
      this.getRole();
    }, err => {
      console.log(err);
    });
  }

  saveRoles(action) {
    if (this.rolesForm.valid) {
      const data = this.rolesForm.value;
      data.status = data.status === 'true' ? true : false;
      const url = action === 'save' ? 'api/roles/add' : `api/roles/edit/${this.activatedRoute.snapshot.params.id}`;
      this.user.addUpadteRoleUserSTff(this.rolesForm.value, url, action).subscribe(res => {
        this.location.back();
        this.toastr.success(`Successfully ${action === 'save' ? 'added' : 'updated'} role`, 'Success');
      }, err => {
        console.log(err);
      });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.rolesForm.markAllAsTouched();
    }
  }


  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex);
    }
    this.createMenu();
  }

  createMenu() {
    const submenuids = [];
    this.selectedMenu.forEach(menu => {
      submenuids.push(menu.submenu_id);
    });
    this.rolesForm.get('subId_list').setValue(submenuids);
  }

}
