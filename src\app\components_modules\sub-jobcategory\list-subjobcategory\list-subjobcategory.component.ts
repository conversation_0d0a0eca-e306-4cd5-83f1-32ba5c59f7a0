import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { SubJobCategory } from 'src/app/models/subJobCategory';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-list-subjobcategory',
  templateUrl: './list-subjobcategory.component.html',
  styleUrls: ['./list-subjobcategory.component.scss', '../../../scss/table.scss']
})
export class ListSubjobcategoryComponent implements OnInit {

  displayedColumns: string[] = ['mj_category_name', 'sj_category_name', 'skill_level', 'patient_info',
    'patient_move', 'std_code', 'round_trip', 'ack_req', 'status', 'sj_category_id'];
  dataSource: MatTableDataSource<SubJobCategory>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly masterDataSErvice: MasterDataService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }
  ngOnInit() {
    this.subjoblist();
  }
  subjoblist() {
    this.masterDataSErvice.getSubJobCategory().subscribe(res => {
      this.dataSource = new MatTableDataSource(res || []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('subjobtable', 'subjobtable_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('subjobtable', 'subjobtable_list');
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Main Job Category': key.mj_category_name,
          'Sub Job Category Name': key.sj_category_name,
          'Skill Level': key.skill_level,
          'Patient Info': key.patient_info,
          'Patient Move': key.patient_move,
          'Standard Time Code': key.std_code,
          'Round Trip': key.round_trip,
          'Ack Required': key.ack_req,
          Status: key.status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to Continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatesubjobcategory/${data.sj_category_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatesubjobcategory/${data.sj_category_id}`], { relativeTo: this.activatedRoute });
    }
  }
}
