import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserService } from '../userService/user.service';
import { RoutingService } from '../routingService/routing.service';

@Injectable({
  providedIn: 'root'
})
export class RouteSecuritycanGuard implements CanActivate {
  constructor(
    private readonly userService: UserService,
    private readonly router: Router,
    private readonly routingService: RoutingService
  ) { }
  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.userService.menulist) {
      const res: any = this.userService.menulist;
      let submenus = [];
      if (res && res.length !== 0 && ((res[1] && res[1].subMenu) || res[0].path || res[0].subMenu)) {
        res.filter((menu: any) => {
          if (menu.subMenu) {
            menu.subMenu.filter(submneu => {
              submenus.push({ path: submneu.path });
            });
          }
        });
        const isPatchExist = submenus.find((path: any) => next.data.data.includes(path.path) && path.path);
        if (isPatchExist) {
          submenus = [];
          return true;
        } else {
          this.router.navigateByUrl('/');
        }
      }
    }
  }
}
