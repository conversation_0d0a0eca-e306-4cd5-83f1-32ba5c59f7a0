<div *ngIf="!isHourlyReport" class="container-table" id="pdf-download">
    <br>
    <div class="row">
        <div class="col-4">
            <img src="./assets/img/UEMS_Solutions_logo.jpg" alt="" style="height: 40px" />
        </div>
        <div class="col-4" style="text-align: center;">
            <h3> <strong>{{title}}</strong></h3>
        </div>
        <div class="col-4" *ngIf="logoUrl">
            <img [src]="logoUrl" alt="logo" style="max-height: 50px; float: right; margin-right: 20px;" />
        </div>
    </div>
    <table class="responstable" style="table-layout: fixed;">
        <caption></caption>
        <tr>
            <th id="" [class]="fontClass">Sl No</th>
            <th id="" [class]="fontClass" *ngFor="let head of heads">{{head}}</th>
        </tr>
        <tr *ngFor="let data of datas; let i = index">
            <td>{{i+1}}</td>
            <td *ngFor="let head of heads">
                {{data[head]}}
            </td>
        </tr>
    </table>
</div>

<div *ngIf="isHourlyReport" class="container-table" id="pdf-download-hourlyReport">
    <div class="row">
        <div class="col-4">
            <img src="./assets/img/UEMS_Solutions_logo.jpg" alt="" style="height: 40px" />
        </div>
        <div class="col-4" style="text-align: center;">
            <h3> <strong>{{title}}</strong></h3>
        </div>
        <div class="col-4" *ngIf="logoUrl">
            <img [src]="logoUrl" alt="logo" style="max-height: 50px; float: right; margin-right: 20px;" />
        </div>
    </div>
    <table class="responstable" style="table-layout: fixed;">
        <caption></caption>
        <tr>
            <th style="width: 10%;">Sl No</th>
            <td *ngFor="let i of datas; let index = index">{{ index + 1 }}</td> <!-- Serial number -->
        </tr>
        <tr>
            <th style="width: 15%;">{{heads[0]}}</th>
            <td *ngFor="let data of datas">{{ data['Hour'] }}</td>
        </tr>
        <tr>
            <th style="width: 15%;">{{heads[1]}}</th>
            <td *ngFor="let data of datas">{{ data['PatientMove'] }}</td>
        </tr>
        <tr>
            <th style="width: 15%;">{{heads[2]}}</th>
            <td *ngFor="let data of datas">{{ data['NonPatientMove'] }}</td>
        </tr>
        <tr>
            <th style="width: 15%;">{{heads[3]}}</th>
            <td *ngFor="let data of datas">{{ data['Total'] }}</td>
        </tr>
    </table>
</div>