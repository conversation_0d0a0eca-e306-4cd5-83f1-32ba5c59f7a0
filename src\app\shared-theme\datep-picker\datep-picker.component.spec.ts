import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DatepPickerComponent } from './datep-picker.component';
import { JasmineDependencyModule } from 'src/app/jasmine-dependency/jasmine-dependency.module';
import { AppModule } from 'src/app/app.module';

describe('DatepPickerComponent', () => {
  let component: DatepPickerComponent;
  let fixture: ComponentFixture<DatepPickerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [DatepPickerComponent],
      imports: [
        JasmineDependencyModule, AppModule
      ],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DatepPickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
