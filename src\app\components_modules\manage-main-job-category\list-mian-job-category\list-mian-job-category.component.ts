import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { MainJOb } from 'src/app/models/manageMianJob';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import Swal from 'sweetalert2';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-list-mian-job-category',
  templateUrl: './list-mian-job-category.component.html',
  styleUrls: ['./list-mian-job-category.component.scss', '../../../scss/table.scss']
})
export class ListMianJobCategoryComponent implements OnInit {

  displayedColumns: string[] = ['category_name', 'status', 'id'];
  dataSource: MatTableDataSource<MainJOb>;

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];

  constructor(
    private readonly masterDataSErvice: MasterDataService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getmainJobCategory();
  }


  getmainJobCategory() {
    this.masterDataSErvice.getmainJobCategory().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('mainjobcategory', 'manage_main_job_category');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('mainjobcategory', 'manage_main_job_category');
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Category Name': key.category_name,
          Status: key.status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to Continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatemainjobcategory/${data.mj_category_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatemainjobcategory/${data.mj_category_id}`], { relativeTo: this.activatedRoute });
    }
  }

}
