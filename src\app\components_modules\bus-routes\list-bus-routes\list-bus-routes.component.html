<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  routerLink="./addbusroute"
                >
                  Add Bus Route
                </button>
              </p>
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav">
                  <p>View Bus Route</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12" style="margin-bottom: 5px">
                  <fieldset class="scheduler-border">
                    <legend class="scheduler-border">Filter Bus Route</legend>
                    <div class="row">
                      <div class="col-3">
                        <div class="col" style="padding-top: 1%">
                          <mat-form-field>
                            <input
                              matInput
                              placeholder="Search..."
                              #filter
                              (keydown)="applyFilter($event.target.value)"
                            />
                            <mat-icon matSuffix>search</mat-icon>
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="col-3">
                        <mat-form-field>
                          <mat-label
                            >Departments<span class="error-css"></span>
                          </mat-label>
                          <mat-select
                            [(ngModel)]="selectedDepartments"
                            (ngModelChange)="selectedDepartments" 
                            multiple="true">
                            <mat-option #allSelected (click)="toggleAllSelection()" [value]="'All'">All</mat-option>
                            <mat-option
                              *ngFor="let data of departmentList"
                              [value]="data.departmentId" (click)="tosslePerOne(allSelected.viewValue)">
                              {{ data.departmentName }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div class="col-1" style="padding-top: .5%">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="applyFilter(filter.value)">
                          <em class="fa fa-minus-square-o"></em>Search
                        </button>
                      </div>
                      <div class="col-1" style="padding-top: .5%">
                        <button
                          class="btn btn-sm btn-default pull-left"
                          (click)="filter.value = ''; applyFilter(filter.value, 'reset')">
                          <em class="fa fa-minus-square-o"></em>Reset
                        </button>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="busroutetable"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>
                      <ng-container matColumnDef="route_no">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Route No
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.route_no }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="route_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Bus Route Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.route_name }}
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="created_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Creation
                                                    Time
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.created_date}} </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="start_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Start Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.start_time
                              ? row.start_time
                                  .split("")
                                  .splice(0, row.start_time.lastIndexOf(":"))
                                  .join("")
                              : "-"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="week_telly">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Schedule
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <div class="">
                            <span *ngIf="row.monday">Mon, </span>
                            <span *ngIf="row.tuesday">Tue, </span>
                            <span *ngIf="row.wednesday">Wed, </span>
                            <span *ngIf="row.thursday">Thu, </span>
                            <span *ngIf="row.friday">Fri, </span>
                            <span *ngIf="row.saturday">Sat, </span>
                            <span *ngIf="row.sunday">Sun</span>
                          </div>
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="start_location_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Start
                                                    Location
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.start_location_name}} </td>
                                            </ng-container> -->

                      <!-- <ng-container matColumnDef="end_location_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> End
                                                    Location
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.end_location_name}} </td>
                                            </ng-container> -->

                      <ng-container matColumnDef="no_of_locations">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          No of Locations
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <span>
                            <a
                              href="{{ row.no_of_locations }}"
                              data-toggle="modal"
                              data-target="#exampleModal"
                              (click)="
                                locationDisplay(
                                  row.rel_locations,
                                  row.is_sequential
                                )
                              "
                            >
                              {{ row.no_of_locations }}</a
                            ></span
                          >
                          <span *ngIf="row.is_sequential">*</span>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="staff_name">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Porter Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.staff_name }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="remarks">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Remarks
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.remarks }}
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="requestor">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Requestor
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.requestor }}
                        </td>
                      </ng-container> -->

                      <ng-container matColumnDef="status">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Status
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.active == true ? "Active" : "Inactive" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="action">
                        <th id="" mat-header-cell *matHeaderCellDef>Action</th>
                        <td mat-cell *matCellDef="let row">
                          <span>
                            <button
                              mat-button
                              color="primary"
                              (click)="
                                activateBusRoute(row.route_id, row.active)
                              "
                            >
                              Activate
                            </button>
                          </span>
                          <span>
                            <button
                              mat-button
                              color="primary"
                              routerLink="./updatebusroute/{{ row.route_id }}"
                            >
                              Edit
                            </button>
                          </span>
                        </td>
                      </ng-container>

                      <!-- <ng-container matColumnDef="route_id">
                                                <th id="" mat-header-cell *matHeaderCellDef> Edit </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        routerLink="./updatebusroute/{{row.route_id}}">edit</em>
                                                </td>
                                            </ng-container> -->

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-table-for-pdf
  [heads]="[
    'Route No',
    'Bus Route Name',
    'Creation Time',
    'Start Time',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
    'Start Location',
    'End Location',
    'No of Locations',
    'Porter Name',
    'Remarks',
    'Requestor',
    'Status'
  ]"
  [title]="'Bus Route'"
  [datas]="pdfData"
  [fontClass]="'font-class'"
>
</app-table-for-pdf>

<!-- Modal -->
<div class="modal fade" id="exampleModal" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <p class="modal-title">Locations</p>
      </div>
      <div class="modal-body">
        <ul>
          <li *ngFor="let location of locationNames">
            {{ location.location_name }}
          </li>
        </ul>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-dismiss="modal">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
