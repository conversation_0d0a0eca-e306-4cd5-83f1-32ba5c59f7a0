@import "../../../scss/table.scss";
.toolbar__css {
  background-color: rgb(236, 236, 236);
  span {
    font-size: 14px;
    color: #3f51b6;
    font-weight: 600;
  }
}

label {
  background-color: #3f51b5;
  color: white;
  width: 11%;
  padding: 0.5rem;
  font-family: sans-serif;
  border-radius: 0.3rem;
  cursor: pointer;
  margin-top: 1rem;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.dot {
  height: 25px;
  width: 25px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
}
.proceed_btn {
  float: right;
}

table td {
  color: #17123b;
  margin: 10px 0px;
  font-size: 16px;
  height: 19px;
  font-weight: 400;
  text-align: left;
  word-break: break-word;
  background-color: white;
}

::ng-deep .mat-tab-body-wrapper {
  height: 80% !important;
}
