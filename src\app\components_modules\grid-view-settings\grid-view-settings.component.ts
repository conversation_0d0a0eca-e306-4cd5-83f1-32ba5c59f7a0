import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from "@angular/cdk/drag-drop";
import { Component, OnInit } from "@angular/core";
import { Location } from "@angular/common";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";

@Component({
  selector: "app-grid-view-settings",
  templateUrl: "./grid-view-settings.component.html",
  styleUrls: ["./grid-view-settings.component.scss"],
})
export class GridViewSettingsComponent implements OnInit {
  selectedGrids: [];
  SelectedRole: 0;

  gridForm: FormGroup;
  roleList: [];
  gridList: any[];
  roleChangeGrid: any;

  constructor(
    private readonly fb: FormBuilder,
    private readonly user: UserService,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    public location: Location
  ) {
    this.gridForm = this.fb.group({
      // role_name: ['', Validators.required],
      grid_ids: [[], Validators.required],
    });
  }

  ngOnInit() {
    this.getRoles();
    this.getGrids();
  }

  getRoles() {
    this.user.getRoles().subscribe((res) => {
      this.roleList =
        res &&
        res.filter((role) => {
          role.status = role.status ? "Active" : "Inactive";
          return role;
        });
    });
  }

  getGrids() {
    this.user.getViewStatusGrids().subscribe((res) => {
      this.gridList =
        res &&
        res.filter((grid) => {
          grid.status = grid.status ? "Active" : "Inactive";

          return grid;
        });
      this.roleChangeGrid = this.gridList;
    });
  }

  onRoleSelect(event) {
    this.SelectedRole = event.value;
    this.gridList = [...this.roleChangeGrid];

    this.user.getViewStatusGridsByRole(event.value).subscribe((res) => {
      this.selectedGrids = res.sort((a, b) => a.order - b.order);
      this.selectedGrids.forEach((val) => {
        this.gridList.forEach((grid, i) => {
          if (grid["id"] === val["id"]) {
            this.gridList.splice(i, 1);
          }
        });
      });
      this.createGridList();
    });
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    this.createGridList();
  }

  createGridList() {
    const gridIds = [];
    this.selectedGrids.forEach((grid, i) => {
      gridIds.push({ grid_id: grid["id"], order: i });
    });
    this.gridForm.get("grid_ids").setValue(gridIds);
  }

  saveGridViewSettings() {
    if (this.gridForm.valid && this.SelectedRole !== 0) {
      const { grid_ids } = this.gridForm.value;
      this.user
        .updateViewStatusGrids(this.SelectedRole, { grids: grid_ids })
        .subscribe(
          (res) => {
            //this.location.back();
            this.toastr.success(`Successfully updated`, "Success");
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.gridForm.markAllAsTouched();
    }
  }
}
