@import "../../../../assets/scss/navbar.scss";

td,
th {
  width: 10%;
  text-align: center !important;
}
fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000 !important;
  height: 152px;
}

legend.scheduler-border {
  font-size: 1em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}
legend {
  display: block;
}
.btn-primary {
  margin-right: 10px;
}
tbody tr:nth-child(odd) {
  background-color: white !important;
}
table,
th,
td {
  border: 1px solid #ccc !important;
}
.spacing {
  margin-top: 22px;
}
table {
  width: 95% !important;
  margin: 22px 26px !important;
}
