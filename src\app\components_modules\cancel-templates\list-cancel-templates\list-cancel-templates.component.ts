import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';

export interface UserData {
  cancel_template: string;
  cancel_template_id: string;
  allow_remarks: string;
}

@Component({
  selector: 'app-list-cancel-templates',
  templateUrl: './list-cancel-templates.component.html',
  styleUrls: ['./list-cancel-templates.component.scss', '../../../scss/table.scss']
})
export class ListCancelTemplatesComponent implements OnInit {

  displayedColumns: string[] = ['cancel_template', 'allow_remarks', 'status', 'cancel_template_id'];
  dataSource: MatTableDataSource<UserData>;
  isExcelClicked: any;
  pdfData = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly settingService: SettingsService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    this.getCancelTemplates();
  }


  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('cancelMessage', 'cancel_template');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      this.isExcelClicked = true;
      setTimeout(() => {
        this.isExcelClicked = TableUtil.exportToExcel('cancelMessage', 'cancel_template');
      }, 0);
    }
  }


  getCancelTemplates() {
    this.settingService.getCanceltemplates().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Cancel Message': key.cancel_template,
          'Allow Remarks': key.allow_remarks ? 'Yes' : 'No',
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatecanceltemplate/${data.cancel_template_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatecanceltemplate/${data.cancel_template_id}`], { relativeTo: this.activatedRoute });
    }
  }

}
