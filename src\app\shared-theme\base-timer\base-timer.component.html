<div class="timer-main" [ngClass]="{'showTimer': !showTimer}" style="height: 50px;">

    <div class="base-timer" [hidden]="!showTimer">
        <svg class="base-timer__svg" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <g class="base-timer__circle">
                <circle class="base-timer__path-elapsed" cx="50" cy="50" r="45">
                </circle>
                <path id="base-timer-path-remaining" #baseTimerPathRemaining stroke-dasharray="283" class="base-timer__path-remaining arc" d="
               M 50, 50
               m -45, 0
               a 45,45 0 1,0 90,0
               a 45,45 0 1,0 -90,0
               "></path>
            </g>
        </svg>
        <span id="base-timer-label" #baseTimerLabel class="base-timer__label"></span>
    </div>

    <mat-slide-toggle [(ngModel)]="showTimer" (click)="toggleChanged(showTimer,$event)">Auto Refresh</mat-slide-toggle>
</div>