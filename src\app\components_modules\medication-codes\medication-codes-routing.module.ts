import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AddUpdateMedicationCodesComponent } from './add-update-medication-codes/add-update-medication-codes.component';
import { MedicationCodesComponent } from './medication-codes.component';


const routes: Routes = [
  {
    path: "",
    component: MedicationCodesComponent
  },
  {
    path: "add-medication-code",
    component: AddUpdateMedicationCodesComponent
  },
  {
    path: "add-medication-code/:id",
    component: AddUpdateMedicationCodesComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MedicationCodesRoutingModule { }
