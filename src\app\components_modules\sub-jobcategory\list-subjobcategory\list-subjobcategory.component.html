<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right;">
                                <button mat-raised-button color="primary" routerLink="./addsubjobcategory">Add Job type
                                </button>
                            </p>
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export</button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li>
                                    <p>View Job Type</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <legend class="scheduler-border">Filter Job Type</legend>
                                        <div class="row">

                                            <div class="col-3">
                                                <div class="col">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2">
                                                <button class="btn btn-sm  btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="subjobtable" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>

                                            <ng-container matColumnDef="mj_category_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> Job Category</th>
                                                <td mat-cell *matCellDef="let row"> {{row.mj_category_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="sj_category_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Job Type
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.sj_category_name}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="skill_level">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Skill Level
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.skill_level}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patient_info">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Patient
                                                    Info</th>
                                                <td mat-cell *matCellDef="let row"> {{row.patient_info ? 'Yes' : 'No'}}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patient_move">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Patient
                                                    Move</th>
                                                <td mat-cell *matCellDef="let row"> {{row.patient_move ? 'Yes' : 'No'}}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="std_code">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Standard
                                                    Time
                                                    Code</th>
                                                <td mat-cell *matCellDef="let row"> {{row.std_code}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="round_trip">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Round Trip
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.round_trip ? 'Yes' : 'No'}}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="ack_req">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Ack
                                                    Required
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.ack_req ? 'Yes' : 'No'}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Status</th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{row?.approval_status}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="sj_category_id">
                                                <th id="" mat-header-cell *matHeaderCellDef> Edit</th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons" style="cursor: pointer;"
                                                        routerLink="./updatesubjobcategory/{{row.sj_category_id}}">edit</em>
                                                        <!-- (click)="navigateToUpdate(row)" -->
                                                </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50">
                </mat-paginator>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf
    [heads]="['Main Job Category', 'Sub Job Category Name', 'Skill Level', 'Patient Info', 'Patient Move', 'Standard Time Code', 'Round Trip', 'Ack Required', 'Status']"
    [title]="'SubJob Category'" [datas]="pdfData">
</app-table-for-pdf>