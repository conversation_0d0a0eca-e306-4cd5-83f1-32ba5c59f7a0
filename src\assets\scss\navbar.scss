// navbar css all page

.card__header__grey {
  background: #ececec !important;
  height: 55px !important;
  width: 98.3%;
  margin-left: 0.8%;
  p {
    color: #3f51b5 !important;
    font-weight: 600;
    font-size: 14px;
  }
  p {
    button {
      margin: -5px 0;
    }
  }
  .nav-tabs-wrapper {
    button:first-child {
      margin: 0 21px;
    }
    p {
      margin: -5px 0;
    }
  }
  .nav-tabs-navigation {
    p {
      button {
        margin-right: 21px;
      }
    }
  }
  .nav-item:hover {
    background: transparent;
  }
  .nav-link {
    pointer-events: none;
  }
  .nav-tabs-wrapper {
    .material-icons {
      color: #3f51b5;
      margin: -3px 11px 0 0;
    }
  }
}

.nav li {
  width: auto;
  line-height: 26px;
  height: 55px !important;
}

.card__header__grey {
  background: #ececec !important;
  a {
    color: #000000 !important;
    font-weight: 600;
  }
  .nav-item:hover {
    background: transparent;
  }
  .nav-link {
    pointer-events: none;
  }
}
