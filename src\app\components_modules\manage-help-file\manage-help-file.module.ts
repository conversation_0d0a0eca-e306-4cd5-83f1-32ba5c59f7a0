import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageHelpFileRoutingModule } from './manage-help-file-routing.module';
import { AddUpdateManggeHelpFileComponent } from './add-update-mangge-help-file/add-update-mangge-help-file.component';
import { ListManggeHelpFileComponent } from './list-mangge-help-file/list-mangge-help-file.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { MatVideoModule } from 'mat-video';


@NgModule({
  declarations: [AddUpdateManggeHelpFileComponent, ListManggeHelpFileComponent],
  imports: [
    CommonModule,
    ManageHelpFileRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatVideoModule
  ]
})
export class ManageHelpFileModule { }
