import { async, ComponentFixture, TestBed } from "@angular/core/testing";
import { FormsModule } from "@angular/forms";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";

import { AddUpdateManageStaffComponent } from "./add-update-manage-staff.component";

describe("AddUpdateManageStaffComponent", () => {
  let component: AddUpdateManageStaffComponent;
  let fixture: ComponentFixture<AddUpdateManageStaffComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [AddUpdateManageStaffComponent],
      imports: [SharedThemeModule, FormsModule],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddUpdateManageStaffComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
