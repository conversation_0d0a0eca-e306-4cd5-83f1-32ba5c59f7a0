<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Tower</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Tower</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="towersForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-12">
                                                    <mat-form-field>
                                                        <mat-label>Tower Name <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Tower Name"
                                                            formControlName="towername">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="towersForm.controls.towername"
                                                                [fieldName]="'Tower Name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Description <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <textarea matInput #desc maxlength="300"
                                                            placeholder="Descripton" formControlName="shortdescription"
                                                            rows="1"></textarea>
                                                        <mat-hint style="text-align: end;">{{desc.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="towersForm.controls.shortdescription"
                                                                [fieldName]="'Description'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <mat-radio-group formControlName="status">
                                                        <mat-radio-button class="example-margin" value=true>Active
                                                        </mat-radio-button>
                                                        <mat-radio-button class="example-margin" value=false>
                                                            Inactive</mat-radio-button>
                                                    </mat-radio-group>
                                                    <mat-error class="pull-left error-css">
                                                        <app-error-message [control]="towersForm.controls.status"
                                                            [fieldName]="'Status'" [fieldType]="'select'">
                                                        </app-error-message>
                                                    </mat-error>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Reason <span class="error-css">
                                                                <!-- <span class="error-css">*</span> -->
                                                            </span>
                                                        </mat-label>
                                                        <textarea matInput #reason maxlength="300" placeholder="Reason"
                                                            formControlName="reason" rows="1"></textarea>
                                                        <mat-hint style="text-align: end;">{{reason.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="towersForm.controls.reason"
                                                                [fieldName]="'Reason'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveTower('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveTower('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="towersForm.reset(); towersForm.controls.status.setValue('true'); getTowerById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                    <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Edit Logs
                                        </legend>
                                        <div class="row">
                                            <div class="col-12">
                                                <label class="col-4">Edited By</label>
                                                <label class="col-4">Status</label>
                                                <label class="col-4">Edited On</label>
                                            </div>
                                        </div>
                                        <div class="row" *ngFor="let log of editLogs">
                                            <div class="col-4">
                                                {{log.edited_by}}
                                            </div>
                                            <div class="col-4">
                                                {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ?
                                                'Approved' : 'Rejected'}}
                                            </div>
                                            <div class="col-4">
                                                {{log.edited_date | localDateConversion: "full"}}
                                            </div>
                                        </div>
                                    </fieldset> -->
                                    <!-- <div class="card card-xl-stretch mb-xl-8" *ngIf="editLogs && editLogs.length > 0">
                                        <div class="card-body pt-2">
                                            <div class="d-flex align-items-center mb-8 row"
                                                *ngFor="let log of editLogs">
                                                <div class="date-margin col-2">
                                                    <span class="text-link fw-bolder fs-6">Updated On</span>
                                                    <span class="text-muted fw-bold d-block">{{log.edited_date |
                                                        localDateConversion: "full"}}</span>
                                                </div>
                                                <span class="bullet bullet-vertical h-60px bg-primary"></span>
                                                <div class="text-muted fw-bold d-block flex-grow-1 col-6">
                                                    <div class="col-12">Tower1
                                                        <span style='font-size:20px;'>&#8594;</span>
                                                        Tower2
                                                    </div>
                                                    <div class="col-12">{{log?.approval_status == 0 ? 'Pending' :
                                                        log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                                                        <span style='font-size:20px;'>&#8594;</span>Approved
                                                    </div>
                                                    <div class="col-12">Created On:
                                                        {{log.edited_date | localDateConversion: "full"}}</div>
                                                    <div class="col-12">Created By:
                                                        {{log.edited_by}}
                                                    </div>
                                                </div>
                                                <div class="col-2">
                                                    <span class="text-link fw-bolder fs-6">Updated By</span>
                                                    <span
                                                        class="badge badge-light-primary fs-8 fw-bolder badge-align">{{log.edited_by}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->

                                    <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Audit Logs
                                        </legend>
                                        <div class="mb-8">
                                            <div class="logs-header row">
                                                <div style="max-width: 150px;min-width: 150px;" class="text-center">Tower Name</div>
                                                <div class="col-1 text-center">Status</div>
                                                <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                                                <div class="col-2 text-center">Request By</div>
                                                <div class="col-2 text-center">Request Date</div>
                                                <div class="col-2 text-center">Approved By</div>
                                                <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                                            </div>
                                        </div>
                                        <div class="" *ngFor="let log of editLogs; let i = index">
                                            <div class="card card-xl-stretch">
                                                <div class="card-body card-body-log pt-2 row m-2">
                                                    <div style="max-width: 150px;min-width: 150px;" class="text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                                                    <div class="col-1 text-muted text-center fw-bold"
                                                        style="white-space: nowrap;">{{log?.status == "true" ? 'Active' : 'In-Active'}}</div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">
                                                        {{log?.approvalStatus || '--'}}
                                                    </div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                                                    </div>
                                                    <div class="col-2 text-muted text-center fw-bold">
                                                        {{log?.formattedCreatedDate ? (log.formattedCreatedDate) : '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                                                    </div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">
                                                        {{log?.formattedApprovedDate ? (log?.formattedApprovedDate) : '--'}}
                                                    </div>
                                                </div>
                                            </div>
                                            <span *ngIf="i !== editLogs.length-1" style='font-size:35px;'
                                                class="text-muted">&#8593;</span>
                                        </div>

                                        <!-- <table class="table">
                                            <thead class="mb-3">
                                                <tr class="logs-header">
                                                    <th>Tower Name</th>
                                                    <th>Status</th>
                                                    <th>Approved</th>
                                                    <th>Created By</th>
                                                    <th>Created Date</th>
                                                    <th>Updated By</th>
                                                    <th>Updated Date</th>
                                                </tr>
                                            </thead>
                                            <tr style="height: 20px;"> </tr>
                                            <tbody *ngFor="let log of editLogs; let i = index">
                                                <tr class="table-card m-3" >
                                                    <td class="text-muted fw-bold">{{log?.name || '--'}}
                                                    </td>
                                                    <td class="text-muted fw-bold">{{log?.status ?
                                                        'Active' :
                                                        'In-Active'}}</td>
                                                    <td class="text-muted fw-bold">
                                                        {{log?.approved_Status}}</td>
                                                    <td class="text-muted fw-bold">{{log?.created_By ||
                                                        '--'}}
                                                    </td>
                                                    <td class="text-muted fw-bold">{{log?.created_Date ?
                                                        (log?.created_Date |
                                                        localDateConversion: "date") : '--'}}</td>
                                                    <td class="text-muted fw-bold">{{log?.approved_By ||
                                                        '--'}}
                                                    </td>
                                                    <td class="text-muted fw-bold">{{log?.approved_Date ?
                                                        (log?.approved_Date |
                                                        localDateConversion: "date") : '--'}}</td>
                                                </tr>
                                                <tr *ngIf="i !== editLogs.length-1">
                                                    <td style='font-size:35px;padding: 3px;' colspan="7" class="text-muted text-center">&#8595;</td>
                                                </tr>
                                            </tbody>
                                        </table> -->
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>