import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { Location } from "@angular/common";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { ActionsService } from "src/app/Apiservices/actions/actions.service";
import { MatSelect } from "@angular/material";
import { MasterDataService } from "src/app/Apiservices/masterData/master-data.service";
import { map } from "rxjs/operators";

@Component({
  selector: "app-add-update-assign-porter",
  templateUrl: "./add-update-assign-porter.component.html",
  styleUrls: ["./add-update-assign-porter.component.scss"],
})
export class AddUpdateAssignPorterComponent implements OnInit, AfterViewInit {
  showDomCondition: boolean = true;
  activeIndex: any;
  escortTypeList = [
    {
      escortType: "Central",
      id: 1,
    },
    {
      escortType: "Station",
      id: 2,
    },
    {
      escortType: "Express",
      id: 3,
    },
    {
      escortType: "Taskbased",
      id: 4,
    },
    {
      escortType: "BusRoute",
      id: 5,
    },
  ];
  activeIndexDutyPorter: any;
  activeIndexEscortType: any;
  activeIndexStationLocation: any[] = [];
  activeIndexStationLocationStation: any;
  subJobCategory$: any;
  activeIndexTaskBasedSub: any[] = [];
  taskSubJob: any;
  filteredMobileNumber: any = [];
  filteredOffDutyPorter: any = [];
  filteredStationLocation: any[] = [];
  filteredJobType$: any;

  @Input("hide")
  public set showDom(v: boolean) {
    this.showDomCondition = v;
  }

  @Input("refreshApis")
  public set refreshApis(v: number) {
    if (v) {
      this.getUnassignedPhonesPorter();
      this.getLocations();
      this.getUnassignedPorter();
    }
  }

  @Output()
  triggerListMapPorter: EventEmitter<number> = new EventEmitter<number>();

  mapPorterForm: FormGroup;
  mobileNumbers: any = [];
  offDutyPorters: any = [];
  stationLocations = [];
  locations = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly actionService: ActionsService,
    private readonly facilityConfig: FacilityConfigService,
    private masterDataService: MasterDataService
  ) {
    this.initialForm();
  }

  initialForm() {
    this.mapPorterForm = this.fb.group({
      mobile_number: ["", Validators.required],
      porter_name: [""],
      phone_tag: [""],
      phone_no: [""],
      porter_id: ["", Validators.required],
      porter_type: ["", Validators.required],
      loc_id_list: [[], Validators.required],
      sub_id_list: [[], Validators.required],
      selectAll: [false],
    });
  }

  ngOnInit() {
    this.getMapPorterById();
    this.getUnassignedPhonesPorter();
    this.getLocations();
    this.getUnassignedPorter();
    this.getSubJobCategories();
  }

  ngAfterViewInit() {}

  getLocations(mappedType?) {
    let paramData = false;
    if (mappedType == 'Station') {
      paramData = true
    } else if (mappedType == 'Express') {
      paramData = false
    }
    this.facilityConfig.getPorterMapLocations(paramData).subscribe((res) => {
      if (res) {
        // res =
        // (res &&
        //   res.length &&
        //   res.filter((val) => {
        //     if (val.status) {
        //       return val;
        //     }
        //   })) ||
        // [];
      this.locations = res;
      this.filteredStationLocation = res;
      }
    });
  }

  getSubJobCategories() {
    this.masterDataService.getSubJobCategory().subscribe((data) => {
      this.taskSubJob = data;
      this.filteredJobType$ = data;
      this.subJobCategory$ = data;
    });
  }

  porterChange(porter) {
    const existPorter = this.offDutyPorters.find(
      (por) => por.staff_id === porter
    );
    this.mapPorterForm.get("porter_name").setValue(existPorter.staff_name);
    this.mapPorterForm.get("porter_type").setValue(existPorter.staff_type);
    this.stationLocationChange(existPorter.staff_type);
  }

  phoneChange(numb) {
    const numberSplit = numb.split("-");
    if (numberSplit.length === 2) {
      this.mapPorterForm.get("phone_no").setValue(numberSplit[1].trim());
      this.mapPorterForm.get("phone_tag").setValue(numberSplit[0].trim());
    }
  }

  getMapPorterById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.actionService
        .getPorterById(this.activatedRoute.snapshot.params.id)
        .subscribe((res) => {
          this.mapPorterForm.patchValue({
            location_id: res.loc_id,
            transport_type_subIds: res.transport_type_subIds,
          });
        });
    }
  }

  stationLocationChange(val) {
    if (val === "Central" || val === "Bus Route") {
      this.mapPorterForm.get("loc_id_list").setValue([]);
      this.mapPorterForm.get("loc_id_list").setValidators([]);
      this.mapPorterForm.get("loc_id_list").updateValueAndValidity();

      this.mapPorterForm.get("sub_id_list").setValue([]);
      this.mapPorterForm.get("sub_id_list").setValidators([]);
      this.mapPorterForm.get("sub_id_list").updateValueAndValidity();
    } else if (val == "Taskbased") {
      this.mapPorterForm.get("loc_id_list").reset();
      this.mapPorterForm.get("sub_id_list").reset();
      this.mapPorterForm.get("loc_id_list").setValidators([]);
      this.mapPorterForm.get("loc_id_list").updateValueAndValidity();

      this.mapPorterForm
        .get("sub_id_list")
        .setValidators([Validators.required]);
      this.mapPorterForm.get("sub_id_list").updateValueAndValidity();
    } else {
      this.mapPorterForm.get("loc_id_list").reset();
      this.mapPorterForm
        .get("loc_id_list")
        .setValidators([Validators.required]);
      this.mapPorterForm.get("loc_id_list").updateValueAndValidity();

      this.mapPorterForm.get("sub_id_list").reset();
      this.mapPorterForm.get("sub_id_list").setValidators([]);
      this.mapPorterForm.get("sub_id_list").updateValueAndValidity();
    }
    if (val == 'Station' || val == 'Express') {
      this.getLocations(val)
    }
  }

  getUnassignedPhonesPorter() {
    this.actionService.getUnassignedPhonesPorter().subscribe((res) => {
      this.mobileNumbers = res;
      this.filteredMobileNumber = res;
    });
  }

  getUnassignedPorter() {
    this.actionService.getUnassignedPorter().subscribe((res) => {
      this.offDutyPorters = res;
      this.filteredOffDutyPorter = res;
    });
  }

  save(action) {
    if (this.mapPorterForm.valid) {
      if (this.mapPorterForm.get("porter_type").value === "Station") {
        this.mapPorterForm.value.loc_id_list = [
          this.mapPorterForm.value.loc_id_list,
        ];
      }
      const url = this.activatedRoute.snapshot.params.id
        ? `api/portermaps/edit/${this.activatedRoute.snapshot.params.id}`
        : "api/portermaps/add";
      this.actionService
        .addUpadtePorter(this.mapPorterForm.value, url, action)
        .subscribe(
          (res) => {
            // this.location.back();
            this.triggerListMapPorter.emit(Math.random());
            this.toastr.success(
              `Successfully ${action === "save" ? "added" : "updated"} porter`,
              "Success"
            );
            this.initialForm();
            this.initializeIndexValue();
            this.ngOnInit();
            document.getElementById("mobileNumberRef")["value"] = "";
            document.getElementById("onOffDutyPorterRef")["value"] = "";
            document.getElementById("locationStation")["value"] = "";
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.mapPorterForm.markAllAsTouched();
    }
  }

  initializeIndexValue() {
    this.activeIndex = null;
    this.activeIndexDutyPorter = null;
    this.activeIndexEscortType = null;
    this.activeIndexStationLocationStation = null;
    this.activeIndexStationLocation = [];
    this.activeIndexTaskBasedSub = [];
  }

  setActiveList(index, value) {
    this.activeIndex = index;
    this.mapPorterForm.get("mobile_number").setValue(value);
    this.phoneChange(value);
  }

  setActiveListDutyPorter(index, value) {
    this.activeIndexDutyPorter = index;
    this.mapPorterForm.get("porter_id").setValue(value.staff_id);
    this.porterChange(value.staff_id);
    const indexOfEscort = this.escortTypeList.findIndex(
      (data: any) =>
        data.escortType.toLowerCase() == value.staff_type.toLowerCase()
    );
    this.activeIndexEscortType = indexOfEscort;
    this.setActiveListEscortType(indexOfEscort, value.staff_type);
  }

  setActiveListEscortType(index, value) {
    this.activeIndexEscortType = index;
    this.mapPorterForm
      .get("porter_type")
      .setValue(this.escortTypeList[index].escortType);
    this.activeIndexStationLocation = [];
    this.activeIndexTaskBasedSub = [];
    this.stationLocationChange(value);
  }

  setActiveListStationLocation(index, val) {
    this.activeIndexStationLocationStation = index;
    this.mapPorterForm.get("loc_id_list").setValue(val.locationId);
  }
  
  changeCheckbox() {
    const data = this.mapPorterForm.value;
    if (data.selectAll == false) {
    this.filteredStationLocation.forEach((ele) => {
      this.activeIndexStationLocation = [
        ...this.activeIndexStationLocation,ele.locationId,
      ];
   });  
   
    this.mapPorterForm
      .get("loc_id_list")
      .setValue([...this.activeIndexStationLocation]);
    this.mapPorterForm.get("sub_id_list").setValue([]);
    }
    else{
      this.activeIndexStationLocation = [];
      this.mapPorterForm
        .get("loc_id_list")
        .setValue(this.activeIndexStationLocation);
      this.mapPorterForm.get("sub_id_list").setValue([]);
    }
}
  setActiveListStationLocationExpress(index, location_name) {
    if (this.activeIndexStationLocation.includes(location_name.locationId)) {
      const filterLocation = this.activeIndexStationLocation.filter(
        (data) => data !== location_name.locationId
      );
      this.activeIndexStationLocation = filterLocation;
      this.mapPorterForm
        .get("loc_id_list")
        .setValue(this.activeIndexStationLocation);
      this.mapPorterForm.get("sub_id_list").setValue([]);
    } else {
      this.activeIndexStationLocation = [
        ...this.activeIndexStationLocation,
        this.locations[index].locationId,
      ];
      this.mapPorterForm
        .get("loc_id_list")
        .setValue([...this.activeIndexStationLocation]);
      this.mapPorterForm.get("sub_id_list").setValue([]);
    }
  }

  setActiveListTaskBased(index, val) {
    if (this.activeIndexTaskBasedSub.includes(val.sj_category_id)) {
      const filterLocation = this.activeIndexTaskBasedSub.filter(
        (data) => data !== val.sj_category_id
      );
      this.activeIndexTaskBasedSub = filterLocation;
      this.mapPorterForm
        .get("sub_id_list")
        .setValue(this.activeIndexTaskBasedSub);
      this.mapPorterForm.get("loc_id_list").setValue([]);
    } else {
      this.activeIndexTaskBasedSub = [
        ...this.activeIndexTaskBasedSub,
        this.taskSubJob[index].sj_category_id,
      ];
      this.mapPorterForm
        .get("sub_id_list")
        .setValue([...this.activeIndexTaskBasedSub]);
      this.mapPorterForm.get("loc_id_list").setValue([]);
    }
  }

  filterMobileNumber(key) {
    this.filteredMobileNumber = this.mobileNumbers.filter((data) => {
      return data.phone_no.toString().toLowerCase().includes(key.toLowerCase());
    });
  }
  filterOffDutyPorter(key) {
    this.filteredOffDutyPorter = this.offDutyPorters.filter((data) => {
      return (
        data.staff_id.toString().toLowerCase().includes(key.toLowerCase()) ||
        data.staff_name.toString().toLowerCase().includes(key.toLowerCase()) ||
        `${data.staff_id} - ${data.staff_name}`
          .toString()
          .toLowerCase()
          .includes(key.toLowerCase())
      );
    });
  }
  filterStationLocation(key) {
    this.filteredStationLocation = this.locations.filter((data) => {
      return data.locationName
        .toString()
        .toLowerCase()
        .includes(key.toLowerCase());
    });
  }

  filteredJobTypeList(key) {
    this.filteredJobType$ = this.subJobCategory$.filter((data) => {
      return (
        data.mj_category_name
          .toString()
          .toLowerCase()

          .includes(key.toLowerCase()) ||
        data.sj_category_name
          .toString()
          .toLowerCase()

          .includes(key.toLowerCase()) ||
        `${data.mj_category_name} - ${data.sj_category_name}`
          .toString()
          .toLowerCase()
          .includes(key.toLowerCase())
      );
    });
  }
  

}
