<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Location</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Location</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="locationForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Location Type<span class="error-css"></span>
                                                        </mat-label>
                                                        <mat-select formControlName="location_type">
                                                            <mat-option *ngFor="let location of locationType"
                                                                [value]="location.id">{{location.location_type}}
                                                            </mat-option>
                                                        </mat-select>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <div class="alignInput">
                                                        <mat-checkbox formControlName="potering"></mat-checkbox>
                                                        <mat-label>Portering<span class="error-css"></span>
                                                        </mat-label>
                                                    </div>

                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Location Name<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Location Name"
                                                            formControlName="location_name">
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="locationForm.controls.location_name"
                                                                [fieldName]="'location name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Short Description<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <textarea matInput #desc maxlength="300"
                                                            placeholder="Short Description"
                                                            formControlName="short_description"></textarea>
                                                        <mat-hint style="text-align: end;">{{desc.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="locationForm.controls.short_description"
                                                                [fieldName]="'Description'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Location SAP Code<span class="error-css"></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Descripton"
                                                            formControlName="location_sap_code">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Tower Name<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="tower_id"
                                                            (ngModelChange)="towerSelected($event)">
                                                            <mat-option *ngFor="let tower of towerName"
                                                                [value]="tower.tower_id">{{tower.tower_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="locationForm.controls.tower_id"
                                                                [fieldName]="'Tower Name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Level Name<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="level_id">
                                                            <mat-option *ngFor="let level of towerLevels"
                                                                [value]="level.level_id">{{level.level_name}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-hint *ngIf="!towerLevels.length">No levels exists
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="locationForm.controls.level_id"
                                                                [fieldName]="'level Name'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Priority<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <mat-select formControlName="priority">
                                                            <mat-option *ngFor="let data of locationPriority"
                                                                [value]="data.id">{{data.priority}}
                                                            </mat-option>
                                                        </mat-select>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="locationForm.controls.priority"
                                                                [fieldName]="'Priority'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Contact No
                                                        </mat-label>
                                                        <input matInput maxlength="10" placeholder="Contact no"
                                                            (keydown)="inputValidation.onlyNumbers($event)"
                                                            formControlName="contact_no">
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Location Password<span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput placeholder="Location Password" [value]=randomNo
                                                            maxlength="4" formControlName="location_password" readonly>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <div class="alignInput">
                                                        <mat-checkbox formControlName="virtual_location">
                                                        </mat-checkbox>
                                                        <mat-label>External Location<span class="error-css"></span>
                                                        </mat-label>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <div class="alignInput">
                                                        <mat-checkbox formControlName="has_station_porter">
                                                        </mat-checkbox>
                                                        <mat-label>Has Station Porter<span class="error-css"></span>
                                                        </mat-label>
                                                    </div>
                                                </div>

                                                <div class="col-6">
                                                    <br>
                                                    <div class="alignInput">
                                                        <mat-radio-group formControlName="status">
                                                            <mat-radio-button class="example-margin" value="1">Active
                                                            </mat-radio-button>
                                                            <mat-radio-button class="example-margin" value="0">
                                                                Inactive</mat-radio-button>
                                                        </mat-radio-group>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="locationForm.controls.status"
                                                                [fieldName]="'Status'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Reason<span class="error-css">
                                                                <!-- <span class="error-css">*</span> -->
                                                            </span>
                                                        </mat-label>
                                                        <textarea matInput #reason maxlength="300" placeholder="Reason"
                                                            formControlName="reason"></textarea>
                                                        <mat-hint style="text-align: end;">{{reason.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="locationForm.controls.reason"
                                                                [fieldName]="'Reason'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveLocation('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveLocation('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="locationForm.reset();generateRandomNo();locationForm.controls.status.setValue('1'); getLocationById()"
                                                class="btn btn-white  pull-right">Reset</button>
                                        </div>
                                    </div>
                                    <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Edit Logs
                                        </legend>
                                        <div class="row">
                                            <div class="col-12">
                                                <label class="col-4">Edited By</label>
                                                <label class="col-4">Status</label>
                                                <label class="col-4">Edited On</label>
                                            </div>
                                        </div>
                                        <div class="row" *ngFor="let log of editLogs">
                                            <div class="col-4">
                                                {{log.edited_by}}
                                            </div>
                                            <div class="col-4">
                                                {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                                            </div>
                                            <div class="col-4">
                                                {{log.edited_date | localDateConversion: "full"}}
                                            </div>
                                        </div>
                                    </fieldset> -->
                                    <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Audit Logs
                                          </legend>
                                          <div class="mb-8">
                                            <div class="logs-header row">
                                              <div style="max-width: 150px;min-width: 150px;" class="text-center">Location Name</div>
                                              <div class="col-1 text-center">Status</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                                              <div class="col-2 text-center">Request By</div>
                                              <div class="col-2 text-center">Request Date</div>
                                              <div class="col-2 text-center">Approved By</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                                            </div>
                                          </div>
                                        <div class="" *ngFor="let log of editLogs; let i = index">
                                            <div class="card card-xl-stretch">
                                                <div class="card-body card-body-log pt-2 row m-2">
                                                    <div style="max-width: 150px;min-width: 150px;" class="text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                                                    <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                                                        'In-Active'}}</div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approvalStatus || '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                                                    </div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                                                        (log?.formattedCreatedDate) : '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                                                    </div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approved_Date ?
                                                        (log?.formattedApprovedDate) : '--'}}</div>
                                                </div>
                                            </div>
                                            <span *ngIf="i !== editLogs.length-1" style='font-size:35px;'
                                                class="text-muted">&#8593;</span>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>