import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageMobileRoutingModule } from './manage-mobile-routing.module';
import { ListManageMobileComponent } from './list-manage-mobile/list-manage-mobile.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddUpdateManageMobileComponent } from './add-update-manage-mobile/add-update-manage-mobile.component';


@NgModule({
  declarations: [ListManageMobileComponent, AddUpdateManageMobileComponent],
  imports: [
    CommonModule,
    ManageMobileRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class ManageMobileModule { }
