/* You can add global styles to this file, and also import other style files */
// @import "~font-awesome/scss/font-awesome.scss";
// @import "~material-design-icons-iconfont/src/material-design-icons";

.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #fff;
  background-color: #29c3e6 !important;
}
@import "~material-design-icons/iconfont/material-icons.css";
.flexslider .slides img {
  max-height: 789px;
}

@media (max-width: 1080px) {
  .logo h1 a {
    font-size: 0.6em;
  }
}

@media (max-width: 480px) {
  .logo h1 a {
    font-size: 0.6em;
  }
}

.header {
  background: #1a4c54eb;
}

#header-admin {
  background: #4d0627eb;
}

.specials-section {
  background: #1a4c54eb;
}

#specials-section-admin {
  background: #4d0627eb;
}

.feature-icon {
  border: 2px solid#1a4c54eb;
}

.glyphicon-wrench,
.glyphicon-dashboard,
.glyphicon-cog,
.glyphicon-record {
  color: #1a4c54eb;
}

.navbar-default .navbar-nav > li > a {
  padding: 1em 1.5em;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
app-base-timer{
  display: flex;
  align-items: center;
}