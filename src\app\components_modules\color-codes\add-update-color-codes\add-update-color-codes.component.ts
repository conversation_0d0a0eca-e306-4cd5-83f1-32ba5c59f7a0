import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { MasterDataService } from 'src/app/Apiservices/masterData/master-data.service';

@Component({
  selector: 'app-add-update-color-codes',
  templateUrl: './add-update-color-codes.component.html',
  styleUrls: ['./add-update-color-codes.component.scss']
})
export class AddUpdateColorCodesComponent implements OnInit {
  departments: any = [];
  jobstatus: any = [];
  colorCodesForm: FormGroup;
  colorId: any;
  color: any;
  toggle: any;
  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly masterdataService: MasterDataService
  ) {
    this.colorCodesForm = this.fb.group({
      color_id: [this.activatedRoute.snapshot.params.id],
      condition: ['', Validators.required],
      color_code: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.getColorCodeById();
  }

  getJobStatus() {
    this.masterdataService.getJobStatus().subscribe(res => {
      this.jobstatus = res || [];
    });
  }

  setColorCode(color) {
    this.colorCodesForm.get('color_code').setValue(color);
  }

  colrdIdSet(id) {
    this.colorCodesForm.get('color_id').setValue(id);
    this.colorId = id;
  }

  getColorCodeById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.masterdataService.getColorCodeById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.jobstatus = [res];
          this.colorCodesForm.get('condition').disable();
          this.colorCodesForm.patchValue(res);
        });
    } else {
      this.getJobStatus();
    }
  }

  saveLevel() {
    if (this.colorCodesForm.valid) {
      this.masterdataService.addUpdateColorCode(
        this.colorCodesForm.getRawValue(), this.activatedRoute.snapshot.params.id ?
        this.activatedRoute.snapshot.params.id :
        this.colorId)
        .subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully updated color code`, 'Success');
        }, err => {
         console.log(err);
        });
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.colorCodesForm.markAllAsTouched();
    }
  }

}
