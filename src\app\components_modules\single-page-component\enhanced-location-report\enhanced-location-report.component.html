<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export
                                </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xlsx')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555"
                                        (click)="exportTable('csv')" class="nav-link">
                                        <p style="display: inline-block">csv</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="nav" style="margin-left: 1%; line-height: 35px">
                                    <p>View Location Report</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">
                                            Filter Location Reports
                                        </legend>
                                        <div class="row">
                                            <div class="col-8">
                                                <form [formGroup]="locationReportForm">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <app-datep-picker [dateConfiguration]="FromDate"
                                                                [control]="locationReportForm.controls.from_date"
                                                                [fieldName]="FromDate.label" [fieldType]="'select'"
                                                                (getDate)="getDate()">
                                                            </app-datep-picker>
                                                        </div>
                                                        <div class="col-6">
                                                            <app-datep-picker [dateConfiguration]="ToDate"
                                                                [control]="locationReportForm.controls.to_date"
                                                                [fieldName]="ToDate.label" [fieldType]="'select'"
                                                                (getDate)="getDate()">
                                                            </app-datep-picker>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="col-4">
                                                <mat-form-field class="alignSelectBox">
                                                    <mat-label>Location</mat-label>
                                                    <mat-select multiple="true" [(ngModel)]="location_id"
                                                        [disableOptionCentering]="true" (ngModelChange)="location_id"
                                                        [(value)]="selected">
                                                        <!-- (selectionChange)="getjobRequestsById()" -->
                                                        <mat-option>
                                                            <ngx-mat-select-search [formControl]="locationFilterCtrl"
                                                                [placeholderLabel]="'Find Location...'"
                                                                [noEntriesFoundLabel]="'no matching location found'">
                                                            </ngx-mat-select-search>
                                                        </mat-option>
                                                        <mat-option #allSelected (click)="toggleAllSelection()"
                                                            [value]="'All'">All</mat-option>
                                                        <mat-option *ngFor="let location of filteredLocations | async"
                                                            [value]="location.locationId"
                                                            (click)="tosslePerOne(allSelected.viewValue)">
                                                            {{ location.locationName }}</mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <!-- <mat-form-field class="alignSelectBox">
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('location');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Location<span class="error-css"></span>
                                                        </mat-label>
                                                        <mat-select disableOptionCentering formControlName="location">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="locationCtrl"
                                                                    [placeholderLabel]="'Find Location...'"
                                                                    [noEntriesFoundLabel]="'no matching location found'">
                                                                </ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option
                                                                *ngFor="let location of filteredLocation | async"
                                                                [value]="location.location_id">{{ location.location_name
                                                                }}
                                                            </mat-option>
                                                        </mat-select>
                                                    </mat-form-field> -->
                                            </div>
                                            <div class="col-4" [formGroup]="locationReportForm">
                                                <mat-form-field style="margin-top: .9%;">
                                                    <mat-label>Patient Name/NRIC </mat-label>
                                                    <input matInput placeholder="Patient Name/NRIC"
                                                        formControlName="nric" />
                                                </mat-form-field>
                                            </div>
                                            <div class="col-8"></div>
                                            <div class="col-10"></div>
                                            <div class="col-2">
                                                <button mat-raised-button type="submit"
                                                    class="btn btn-primary pull-right" (click)="searchByData()">
                                                    Search
                                                </button>
                                                <button mat-raised-button (click)="restForm()"
                                                    class="btn btn-white pull-right">
                                                    Reset
                                                </button>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <!-- </form> -->
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto;"
                                        [ngStyle]="{'height': (dataSource && dataSource.filteredData.length === 0) ? '150px' : '600px'}">
                                        <table id="enhancedReport" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="order_no">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Job No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.order_no ? row?.order_no : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="request_type">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Request Type
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.request_type ? row?.request_type == "Emergency" ? "Urgent" :
                                                    row?.request_type : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="order_from">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Order From
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.order_from ? row?.order_from : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="task_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Task Time
                                                </th>

                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.task_time && row?.task_time !== '' ? (row.task_time) :
                                                    '--'}}
                                                </td>

                                            </ng-container>

                                            <ng-container matColumnDef="assign_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Assigned Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.assign_time ? (row?.assign_time) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="start_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Start Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.start_time ? (row?.start_time) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="completion_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Completion Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.completion_time ? (row?.completion_time) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancel_time">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancel_time ? (row?.cancel_time) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="requestor">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Requestor
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.requestor ? row?.requestor : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="patient_name">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Patient
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.patient_name ? row?.patient_name : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="nric">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    NRIC
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.nric ? row?.nric : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="from_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    From
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.from_location ? row?.from_location : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="from_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.from_bed ? row?.from_bed : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="to_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    To
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.to_location ? row?.to_location : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="to_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.to_bed ? row?.to_bed : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="return_location">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Return
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.return_location ? row?.return_location : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="return_bed">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Bed No
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.return_bed ? row?.return_bed : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="remarks">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Remarks
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.remarks ? row?.remarks : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="job_status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Job Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.job_status ? row?.job_status : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="main_category">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Main Category
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.main_category ? row?.main_category : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="sub_category">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Sub Category
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.sub_category ? row?.sub_category : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="transport_mode">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Transport Mode
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.transport_mode ? row?.transport_mode : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="std_kpi">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Std KPI
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.std_kpi ? row?.std_kpi : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="req_resp">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Req-Resp(Mins)
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.req_resp ? row?.req_resp : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resp_comp">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resp-Comp(Mins)
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resp_comp ? row?.resp_comp : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="within_kpi">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Within KPI
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.within_kpi ? "Yes" : "No" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="isolation_precaution">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Isolation Precaution
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.isolation_precaution ? row?.isolation_precaution : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resource1">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 1
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource1 ? row?.resource1 : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="resource2">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Resource 2
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.resource2 ? row?.resource2 : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="created_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Creation Time
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.created_date ? (row?.created_date) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="created_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Created By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.created_by ? row?.created_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="modified_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Modified By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.modified_by ? row?.modified_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="modified_date">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Modified Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.modified_date ? (row?.modified_date) : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="assigned_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Assigned By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.assigned_by ? row?.assigned_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="delay_reason">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Delay Reason
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.delay_reason ? row?.delay_reason : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancel_reason">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Reason
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancel_reason ? row?.cancel_reason : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancel_remarks">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancel Remarks
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancel_remarks ? row?.cancel_remarks : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="cancelled_by">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Cancelled By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.cancelled_by ? row?.cancelled_by : "--" }}
                                                </td>
                                            </ng-container>

                                            <!-- <ng-container *ngIf="showAck" matColumnDef="ack">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    ACK
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <a *ngIf="row?.ack; else noack" [href]="row.ack"
                                                        target="_blank"></a>
                                                    <p *ngIf="row?.ack; else noack"
                                                        (click)="downloadHelpFile(row.ack, 'image')"
                                                        style="cursor: pointer">
                                                        View Image
                                                    </p>
                                                    <ng-template #noack> -- </ng-template>
                                                </td>
                                            </ng-container> -->

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                                        </table>
                                        <div style="text-align: center;"
                                            *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons [pageSize]=50
                    (page)="pageChanged($event)"></mat-paginator>
            </div>
        </div>
    </div>
</div>

<!-- <ng-template #ackImageRef>
    <div mat-dialog-content>
        <mat-toolbar class="row mb-2">
            <p class="col-11">Ack image view</p>
            <div class="col-1">
                <span class="material-icons" style="cursor: pointer" (click)="dialogRef.close()">
                    close
                </span>
            </div>
        </mat-toolbar>

        <div>
            <img [src]="mediaUrl.url" alt="ack image" class="img-fluid" />
        </div>
    </div>
</ng-template> -->