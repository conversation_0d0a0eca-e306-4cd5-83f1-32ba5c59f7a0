import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { ViewStatusByJobCategoryRoutingModule } from "./view-status-by-job-category-routing.module";
import { ViewStatusByJobCategoryComponent } from "./view-status-by-job-category.component";
import { ReactiveFormsModule } from "@angular/forms";
import { SharedModule } from "src/app/shared/shared.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { JobRequestsModule } from "../job-requests/job-requests.module";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";

@NgModule({
  declarations: [ViewStatusByJobCategoryComponent],
  imports: [
    CommonModule,
    ViewStatusByJobCategoryRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    JobRequestsModule,
    NgxMatSelectSearchModule,
    SharedThemeModule
  ],
})
export class ViewStatusByJobCategoryModule {}
