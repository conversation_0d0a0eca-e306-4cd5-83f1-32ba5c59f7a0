<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()"
                >keyboard_backspace</em
              >
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">
                    Add Manage Help File
                  </p>
                  <p *ngIf="activatedRoute.snapshot.params.id">
                    Update Manage Help File
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="fileForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-12">
                          <mat-form-field class="example-full-width">
                            <mat-label>Topic</mat-label>
                            <input matInput formControlName="topic" />

                            <mat-error class="pull-left error-css">
                              <app-error-message
                                [control]="fileForm.controls.topic"
                                [fieldName]="'Topic'"
                                [fieldType]="'select'"
                              >
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-12 mt-3">
                          <app-file-picker
                            [control]="fileForm.controls.file"
                            [fieldName]="'PDF/ PPT'"
                            [fieldType]="'select'"
                            [allowedExtensions]="['pdf', 'ppt', 'pptx']"
                            [fileupload]="fileupload"
                            [maxMB]="52428800"
                            [maxSize]="50"
                            (selectedFile)="getFile($event)"
                            [required]="false"
                          >
                          </app-file-picker>
                        </div>
                        <div class="col-12 mt-5">
                          <app-file-picker
                            [control]="fileForm.controls.video"
                            [fieldName]="'Media'"
                            [fieldType]="'select'"
                            [allowedExtensions]="['mp4', 'jpeg']"
                            [fileupload]="mediaFile"
                            [maxMB]="52428800"
                            [maxSize]="50"
                            (selectedFile)="getMediaFile($event)"
                            [required]="false"
                          >
                          </app-file-picker>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button
                        mat-raised-button
                        *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right"
                        (click)="save()"
                      >
                        Submit
                      </button>
                      <button
                        mat-raised-button
                        (click)="
                          fileForm.reset(); fileupload = null; mediaFile = null
                        "
                        class="btn btn-white pull-right"
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
