import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListDistanceComponent } from './list-distance/list-distance.component';
import { AddUpdateDistanceComponent } from './add-update-distance/add-update-distance.component';


const routes: Routes = [
  { path: '', component: ListDistanceComponent },
  {path: 'adddistance', component: AddUpdateDistanceComponent},
  {path: 'updatedistance/:id', component: AddUpdateDistanceComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DistanceRoutingModule { }
