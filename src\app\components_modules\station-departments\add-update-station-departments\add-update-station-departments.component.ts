import { filter, takeUntil } from 'rxjs/operators';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../Apiservices/userService/user.service';
import { Location } from '@angular/common';
import { ReplaySubject, Subject } from 'rxjs';

@Component({
  selector: 'app-add-update-station-departments',
  templateUrl: './add-update-station-departments.component.html',
  styleUrls: ['./add-update-station-departments.component.scss']
})
export class AddUpdateStationDepartmentsComponent implements OnInit {
  departmentForm: FormGroup;
  locationsList: any;
  stationLocations = [];
  protected _onDestroy = new Subject<void>();
  public locationFilterCtrl: FormControl = new FormControl();
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  constructor(
    private readonly fb: FormBuilder,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    public location: Location
  ) {
    this.getLocations();
   }

  ngOnInit() {
    this.addDepartmentForm();
    if (this.activatedRoute.snapshot.params.id) {
      this.getStationDepartmentById(this.activatedRoute.snapshot.params.id);
    }
    if(this.locationsList) {
      this.filteredLocations.next(this.locationsList.slice());
      this.locationFilterCtrl.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe(() => {
          this.filterLocations();
        });
    }
  }

  addDepartmentForm() {
    this.departmentForm = this.fb.group({
      department_name: ['', Validators.required],
      status: [true, Validators.required],
      locations: []
    });
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      if (res) {
        // res =
        //   (res &&
        //     res.length &&
        //     res.filter((val) => {
        //       if (val.status) {
        //         return val;
        //       }
        //     })) ||
        //   [];
        this.locationsList = res;
        this.filterLocations();
        this.filteredLocations.next(this.locationsList.slice());
        this.locationFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.filterLocations();
          });
      }      
    });
  }

  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getStationDepartmentById(id) {
    this.userService.getStationDepartmentById(id).subscribe(res => {
      if (res) {
        this.departmentForm.patchValue(res[0]);
        res[0].station_locations.filter(loc => {
          this.stationLocations.push(loc.location_id)
        })
        this.departmentForm.get('locations').setValue(this.stationLocations);
      }
    })
  }

  saveDepartment(actiontype?) {
    let Id: any;
    if (this.departmentForm.valid) {
      if (actiontype === 'update') {
        Id = Number(this.activatedRoute.snapshot.params.id);
        this.userService.updateStationDepartments(Id, this.departmentForm.value).subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully updated Station Department`, 'Success');
        }, err => {
          console.log(err);
        });
      } else {
        this.userService.addStationDepartments(this.departmentForm.value).subscribe(res => {
          this.location.back();
          this.toastr.success(`Successfully added Station Department`, 'Success');
        }, err => {
          console.log(err);
        });
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.departmentForm.markAllAsTouched();
    }
  }

}
