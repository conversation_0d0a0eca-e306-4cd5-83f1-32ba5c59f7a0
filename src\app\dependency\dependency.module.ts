import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import {
  MatPaginatorModule,
  MatRadioModule,
  MatCheckboxModule,
  MatIconModule,
  MatDividerModule,
  MatAutocompleteModule
} from '@angular/material';
import { ColorPickerModule } from 'ngx-color-picker';
import { MdePopoverModule } from '@material-extended/mde';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
  ],
  exports: [
    MatPaginatorModule,
    MatRadioModule,
    MatCheckboxModule,
    MatIconModule,
    DragDropModule,
    MdePopoverModule,
    MatDividerModule,
    MatAutocompleteModule,
    ColorPickerModule
  ]
})
export class DependencyModule { }
