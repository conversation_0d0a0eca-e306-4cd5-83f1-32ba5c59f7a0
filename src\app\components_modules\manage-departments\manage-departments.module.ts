import { SharedModule } from './../../shared/shared.module';
import { SharedThemeModule } from './../../shared-theme/shared-theme.module';
import { DependencyModule } from './../../dependency/dependency.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageDepartmentsRoutingModule } from './manage-departments-routing.module';
import { ListManageDepartmetsComponent } from './list-manage-departmets/list-manage-departmets.component';
import { AddManageDepartmetsComponent } from './add-manage-departmets/add-manage-departmets.component';


@NgModule({
  declarations: [ListManageDepartmetsComponent, AddManageDepartmetsComponent],
  imports: [
    CommonModule,
    ManageDepartmentsRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule
  ]
})
export class ManageDepartmentsModule { }
