import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ListJobRequestsComponent } from "./list-job-requests/list-job-requests.component";
import { AddUpdateJobRequestsComponent } from "./add-update-job-requests/add-update-job-requests.component";
import { SearchJobStatusComponent } from "./search-job-status/search-job-status.component";
import { UploadJobsComponent } from "./upload-jobs/upload-jobs.component";

const routes: Routes = [
  { path: "", component: ListJobRequestsComponent },
  { path: "searchjob", component: SearchJobStatusComponent },
  { path: "addjobrequest", component: AddUpdateJobRequestsComponent },
  { path: "updatejobrequest/:id", component: AddUpdateJobRequestsComponent },
  { path: "upload-jobs", component: UploadJobsComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class JobRequestsRoutingModule {}
