
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { PatientLogs } from 'src/app/models/patientLogs';
import { ReportsService } from 'src/app/Apiservices/reports/reports.service';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';

@Component({
  selector: 'app-list-adt-patientlog',
  templateUrl: './list-adt-patientlog.component.html',
  styleUrls: ['./list-adt-patientlog.component.scss', '../../../scss/table.scss']
})
export class ListAdtPatientlogComponent implements OnInit {
  displayedColumns: string[] = ['nric_no', 'patient_name', 'bed_no', 'loc_sap_code', 'status', 'old_nric', 'creation_date'];
  dataSource: MatTableDataSource<PatientLogs>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly reportService: ReportsService,
    private readonly loader: LoaderService
  ) {
  }
  ngOnInit() {
    this.PatientLog();
  }
  PatientLog() {
    this.reportService.getPatientLog().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  datereplace(date: string) {
    if (date.includes('T')) {
      return date.replace('T', ' ');
    } else {
      return date;
    }
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('patientLogtable', 'patientLog_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('patientLogtable', 'patientLog_list');
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'NRIC Number': key.nric_no,
          'Patient Name': key.patient_name,
          'Bed No': key.bed_no,
          'Location Sap Code': key.loc_sap_code,
          Status: key.status ? 'Active' : 'Inactive',
          'Old NRIC': key.old_nric,
          'Creation Date/Time': key.creation_date
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }


}
