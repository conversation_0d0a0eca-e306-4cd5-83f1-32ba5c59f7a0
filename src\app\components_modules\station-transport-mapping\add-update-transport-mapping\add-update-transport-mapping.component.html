<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Location to job-category map 
                                    </p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Location to job-category map</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="transportmappingForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-6">
                                                    <!-- <mat-form-field> -->
                                                        <!-- <mat-label>Location <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label> -->
                                                        <label
                                                            class="col-3 pull-left make_label"
                                                            style="color: black;">Location
                                                            <span class="error-css">*</span>
                                                        </label>
                                                        <span class="col-3"></span>
                                                        <ng-multiselect-dropdown 
                                                          name="location_id" [placeholder]="'Select Location'" 
                                                          [settings]="dropdownSettings"
                                                          [data]="locationVal"
                                                          [disabled]="disabled"
                                                          formControlName="location_id"
                                                          (onSelect)="onItemSelect($event)"
                                                          (onSelectAll)="onSelectAll($event)">
                                                        </ng-multiselect-dropdown>
                                                        <!-- <mat-select formControlName="location_id">
                                                            <mat-option *ngFor="let val of locationVal"
                                                                [value]="val.location_id">
                                                                {{val.location_name}}</mat-option>
                                                        </mat-select> -->
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="transportmappingForm.controls.location_id"
                                                                [fieldName]="'Location'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    <!-- </mat-form-field> -->
                                                </div>
                                                <div class="col-6">
                                                    <!-- <mat-form-field> -->
                                                        <!-- <mat-label>Job Category <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label> -->
                                                        <label
                                                            class="col-4 pull-left make_label"
                                                            style="color: black;">Job Category
                                                            <span class="error-css">*</span>
                                                        </label>
                                                        <span class="col-3"></span>
                                                        <ng-multiselect-dropdown 
                                                          name="jobs" [placeholder]="'Select Job Category'" 
                                                          [settings]="dropdownJobCategorySettings"
                                                          [data]="subListTransport"
                                                          formControlName="transport_type_subIds"
                                                          (onSelect)="onItemSelect($event)"
                                                          (onSelectAll)="onSelectAll($event)">
                                                        </ng-multiselect-dropdown>
                                                        <!-- <mat-select formControlName="transport_type_subIds"
                                                            multiple=true>
                                                            <mat-option *ngFor="let val of subListTransport"
                                                                [value]="val.sj_category_id">{{val.sub_main_cat}}
                                                            </mat-option>
                                                        </mat-select> -->
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="transportmappingForm.controls.transport_type_subIds"
                                                                [fieldName]="'Transport Type'" [fieldType]="'select'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    <!-- </mat-form-field> -->
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveTransportMapping('save')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveTransportMapping('update')">Update</button>
                                            <button mat-raised-button
                                                (click)="transportmappingForm.reset(); getStationTransportById()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>