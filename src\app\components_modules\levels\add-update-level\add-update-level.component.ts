import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-level',
  templateUrl: './add-update-level.component.html',
  styleUrls: ['./add-update-level.component.scss']
})
export class AddUpdateLevelComponent implements OnInit {
  towers: any = [];
  levelsForm: FormGroup;
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) {
    this.levelsForm = this.fb.group({
      levelname: ['', Validators.required],
      shortdescription: ['', Validators.required],
      towerid: ['', Validators.required],
      status: ['true', Validators.required],
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getTowers();
    this.getLevelsById();
  }
  getTowers() {
    this.facilityConfig.getTowers().subscribe(res => {
      res = res && res.length && res.filter(val => {
        if (val.status) {
          return val;
        }
      }) || [];
      this.towers = res;
    });
  }
  getLevelsById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getLevelsById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          this.levelsForm.patchValue({
            levelname: res.level_name,
            shortdescription: res.short_description,
            towerid: res.tower_id,
            status: res.status === true ? 'true' : 'false',
            reason: res.reason,
            approval_status: res.approval_status
          });
        });
    }
  }

  saveLevel(actiontype) {
    let levelId: any;
    if (this.levelsForm.valid) {
      const data = this.levelsForm.value;
      data.status = data.status === 'true' ? true : false;
      if (actiontype === 'update') {
        levelId = Number(this.activatedRoute.snapshot.params.id);
        if (data.approval_status && data.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.updateLevel(data, actiontype, levelId);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.updateLevel(data, actiontype, levelId);
        }
      } else {
        this.updateLevel(data, actiontype, levelId);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.levelsForm.markAllAsTouched();
    }
  }

  updateLevel(data, actiontype, levelId) {
    this.facilityConfig.addUpadteLevels(
      data, actiontype === 'save' ?
      'api/levels/add' : `api/levels/edit/${levelId}`, actiontype)
      .subscribe(res => {
        this.location.back();
        this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} level`, 'Success');
      }, err => {
        console.log(err);
      });
  }

}
