import { Component, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';

@Component({
  selector: 'app-loader',
  templateUrl: './loader.component.html',
  styleUrls: ['./loader.component.scss']
})
export class LoaderComponent implements OnInit {
  color = 'primary';
  mode = 'indeterminate';
  value = 50;
  isLoading: Subject<boolean> = new Subject<boolean>();

  constructor(private readonly loaderService: LoaderService) {
    this.isLoading = this.loaderService.isLoading;
  }

  ngOnInit() {
  }

}
