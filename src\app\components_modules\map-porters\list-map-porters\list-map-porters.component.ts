import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import Swal from "sweetalert2";
import { ToastrService } from "ngx-toastr";
import { MapPorter } from "src/app/models/mapPorters";
import { ActionsService } from "src/app/Apiservices/actions/actions.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { FormBuilder, FormGroup } from "@angular/forms";

@Component({
  selector: "app-list-map-porters",
  templateUrl: "./list-map-porters.component.html",
  styleUrls: ["./list-map-porters.component.scss", "../../../scss/table.scss"],
})
export class ListMapPortersComponent implements OnInit {
  displayedColumns: string[] = [
    "porter_name",
    "phone_no",
    "assigned_mobile",
    "holding_time",
    "porter_type",
    "break_on",
    "break_off",
    "total_break_time",
    "station_location",
    "delete_id",
  ];
  dataSource: MatTableDataSource<MapPorter>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild("dialogContentRef", { static: false })
  dialogContentRef: TemplateRef<unknown>;
  pdfData = [];
  refreshApis: number;
  durationInMins = ['15', '30', '45', '60','75', '90']
  breakTimeModalForm: FormGroup;
  tempId: any
  constructor(
    private readonly toaster: ToastrService,
    private readonly actionService: ActionsService,
    private readonly loader: LoaderService,
    private dialog: MatDialog,
    private readonly fb: FormBuilder,
  ) {}

  ngOnInit() {
    this.getPorter();
    this.createModalForm();
  }

  createModalForm(){
    this.breakTimeModalForm = this.fb.group({
      duration: ['']
    });
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "mapportertable",
        "mapportertable_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("mapportertable", "mapportertable_list");
    }
  }

  break(id, action) {
    // this.openDialogAction();
    const data = {
      duration: ''
    };
    if(action == 'on') {
      data['duration'] = this.breakTimeModalForm.value.duration;
    }
    this.actionService.portersBreakOnOff(id, action, data).subscribe(
      () => {
        this.toaster.success("Success");
        this.getPorter();
        this.dialog.closeAll();
      },
      (err) => {
        console.log(err);
      }
    );
  }

  openDialogAction(id) {
    this.tempId = id
    const dialogRef = this.dialog.open(this.dialogContentRef, {
      width: "40%",
      height: "auto",
      disableClose: true,
    })
  }

  onCancelClick(): void {
    this.dialog.closeAll();
  }


  getPorter() {
    this.actionService.getPorters().subscribe((res) => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Staff Name": key.porter_name,
              "Staff Mobile": key.phone_no,
              "Phone Tag": key.phone_tag,
              "Holding time(mints)": key.holding_time,
              "Staff Type": key.porter_type,
              "Break ON": key.break_on ? key.break_on : "Start",
              "Break OFF": key.break_on ? "End" : "NA",
              "Total Break Time": key.total_break_time,
              Location: key.station_location ? key.station_location : "--",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  delete(id) {
    Swal.fire({
      title: "Are you sure?",
      text: "You want to delete!",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No, keep it",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.value) {
        this.actionService.deletePorter(id).subscribe(
          (res) => {
            if (res) {
              this.toaster.success("Successfully deleted porter", "Success");
              this.refreshApis = Math.random();
              this.getPorter();
            } else {
              this.toaster.error(
                "Specified porter is currently active ",
                "Error"
              );
            }
          },
          (err) => {
            console.log(err);
          }
        );
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        return;
      }
    });
  }

  triggerMapPorterList(value: number) {
    if (value) {
      this.getPorter();
    }
  }
}
