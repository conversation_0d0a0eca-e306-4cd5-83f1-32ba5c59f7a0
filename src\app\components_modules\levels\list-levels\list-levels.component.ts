import { Router, ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';
import Swal from 'sweetalert2';
export interface UserData {
  tower_name: string;
  level_name: string;
  short_description: string;
  status: string;
  level_id: string;
}

@Component({
  selector: 'app-list-levels',
  templateUrl: './list-levels.component.html',
  styleUrls: ['./list-levels.component.scss', '../../../scss/table.scss']
})
export class ListLevelsComponent implements OnInit {
  displayedColumns: string[] = ['tower_name', 'level_name', 'short_description', 'status', 'level_id'];
  dataSource: MatTableDataSource<UserData>;
  pdfData = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private readonly facilityConfig: FacilityConfigService,
    private readonly loader: LoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
  }

  ngOnInit() {
    this.getLevels();
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('leveltable', 'level_list');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('leveltable', 'level_list');
    }
  }

  getLevels() {
    this.facilityConfig.getLevels().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }


  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Tower Name': key.tower_name,
          'Level Name': key.level_name,
          'Short Description': key.short_description,
          Status: key.status ? 'Active' : 'Inactive'
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  navigateToUpdate(data: any) {
    if (data.approval_status && data.approval_status == 'Pending') {
      Swal.fire({
        title: 'Are you sure?',
        text: `You want to continue!`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes'
      }).then((result) => {
        if (result.value) {
          this.router.navigate([`./updatelevel/${data.level_id}`], { relativeTo: this.activatedRoute });
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          return;
        }
      });
    } else {
      this.router.navigate([`./updatelevel/${data.level_id}`], { relativeTo: this.activatedRoute });
    }
  }
}
