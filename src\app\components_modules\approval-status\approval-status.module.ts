import { DependencyModule } from './../../dependency/dependency.module';
import { SharedThemeModule } from './../../shared-theme/shared-theme.module';
import { SharedModule } from './../../shared/shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ApprovalStatusRoutingModule } from './approval-status-routing.module';
import { ListApprovalStatusComponent } from './list-approval-status/list-approval-status.component';


@NgModule({
  declarations: [ListApprovalStatusComponent],
  imports: [
    CommonModule,
    ApprovalStatusRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class ApprovalStatusModule { }
