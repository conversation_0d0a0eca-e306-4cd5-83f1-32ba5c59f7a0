import { Component, OnInit, ViewChild } from '@angular/core';
import { CaTegoruTransPortMapping } from 'src/app/models/manageCategoryTransportModes';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import { TableUtil } from 'src/app/utuilities/tablexlsxutility/tableXslxUtility';
import { TableUtilPDf } from 'src/app/utuilities/tablePdfUtility';
import { LoaderService } from 'src/app/Apiservices/loader/loader.service';

@Component({
  selector: 'app-list-category-mode-transport',
  templateUrl: './list-category-mode-transport.component.html',
  styleUrls: ['./list-category-mode-transport.component.scss', '../../../scss/table.scss']
})
export class ListCategoryModeTransportComponent implements OnInit {


  displayedColumns: string[] = ['mj_category_name', 'sj_category_name', 'transport_mode_count', 'category_id'];
  dataSource: MatTableDataSource<CaTegoruTransPortMapping>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  pdfData = [];
  constructor(
    private readonly faciltyConfigService: FacilityConfigService,
    private readonly loader: LoaderService
  ) { }

  ngOnInit() {
    this.getCategoryTranportMapping();
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === 'pdf') {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf('categoryMappingTransport', 'categoryMappingTransportMode');
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel('categoryMappingTransport', 'categoryMappingTransportMode');
    }
  }

  getCategoryTranportMapping() {
    this.faciltyConfigService.getCategoryTranportMappingList().subscribe(res => {
      this.dataSource = new MatTableDataSource(res ? res : []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.setPageSizeOptions();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe(data => {
      this.pdfData = data && data.filter(key => {
        const a = {
          'Category Name': key.mj_category_name,
          'Sub Category Name': key.sj_category_name,
          'No.of Transport Mode': key.transport_mode_count
        };
        Object.assign(key, a);
        return key;
      }) || [];
    });
  }


}
