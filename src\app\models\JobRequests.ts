export interface JobRequests {
    order_id: 0;
    orderNo: string;
    RequestType: string;
    From: string;
    FromBed: string;
    request_time: string;
    order_no: string;
    from_location: string;
    to_location: string;
    transport_mode_name: string;
    task: string;
    request_type: string;
    patient_name: string;
    To: string;
    Return: string;
    TransportMode: string;
    MainCategory: string;
    SubCategory: string;
    RequestTime: string;
    AssignTime: string;
    AskTime: string;
    ArrivalTime: string;
    StartTime: string;
    CompleteTime: string;
    DueTime: string;
    duedate: string;
    CancelTime: string;
    patient: string;
    NRIC: string;
    Remarks: string;
    Resource1: string;
    Porterid: 0;
    porter_name: string;
    cancel_status: false;
    end_status: false;
    start_status: false;
    arrival_status: false;
    assign_status: false;
    returnjob: 0;
    JobStatus: string;
    sendback: string;
    color_code: string;
}
