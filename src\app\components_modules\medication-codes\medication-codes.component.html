<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right;">
                                <button mat-raised-button color="primary" routerLink="./add-medication-code">Add
                                    Medication </button>
                            </p>
                            <p style="float: right;">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export </button>
                            </p>
                            <!-- <p style="float: right;">
                                <a mat-raised-button class="template_btn"
                                    href="assets/file/Sample medication codes sheet.xlsx" color="primary">Download
                                    Template
                                </a>
                            </p> -->
                            <mat-menu #sub_menu_language="matMenu">
                                <br>
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">xsls</p>
                                    </a>
                                    <a style="margin-top:-5px; cursor: pointer; color: #555555;"
                                        (click)="exportTable('pdf')" class="nav-link">
                                        <p style="display: inline-block">PDF</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="w-100">
                                    <p>View Medication Codes</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <fieldset class="scheduler-border">
                                        <legend></legend>
                                        <div class="row">
                                            <div class="col-3">
                                                <div class="col" style="padding-top: 0.5em">
                                                    <mat-form-field>
                                                        <input matInput placeholder="Search..." #filter
                                                            (keydown)="applyFilter($event.target.value)">
                                                        <mat-icon matSuffix>search</mat-icon>
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                            <div class="col-2" style="padding-top: 1em">
                                                <button class="btn btn-sm btn-default pull-left"
                                                    (click)="filter.value = ''; applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Reset</button>
                                            </div>
                                            <div class="col-3"></div>
                                            <div class="col-2" style="padding-top: 0.5em">
                                                <button class="btn btn-default" style="background: #1b2581;"
                                                    (click)="clearMedications()"><em
                                                        class="fa fa-minus-square-o"></em>Clear Medication Code</button>
                                            </div>
                                            <div class="col-2" style="padding-top: 0.5em">
                                                <!-- <button class="btn btn-default" style="background: #1b2581;"
                                                    (click)="applyFilter(filter.value)"><em
                                                        class="fa fa-minus-square-o"></em>Upload Medication
                                                    Code</button> -->
                                                <a mat-raised-button class="btn btn-default"
                                                    style="background: #1b2581;" data-toggle="modal"
                                                    data-target="#exampleModal" (click)="applyFilter('')">Upload
                                                    Medication Code</a>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x:auto;">
                                        <table id="Medication_codes_table" mat-table [dataSource]="dataSource" matSort
                                            class="w-100">
                                            <caption></caption>

                                            <ng-container matColumnDef="MedicationName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header
                                                    style="margin: 0 auto;"> Medication Name </th>
                                                <td mat-cell *matCellDef="let row"> {{row.MedicationName}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="MedicationCode">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Medication
                                                    Codes
                                                </th>
                                                <td mat-cell *matCellDef="let row"> {{row.MedicationCode}}</td>
                                            </ng-container>

                                            <ng-container matColumnDef="Status">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header> Status
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row.Status}} </td>
                                            </ng-container>

                                            <ng-container matColumnDef="medication_action">
                                                <th id="" mat-header-cell *matHeaderCellDef> Action </th>
                                                <td mat-cell *matCellDef="let row">
                                                    <em class="material-icons mr-3" matTooltip="Edit"
                                                        style="cursor: pointer;" matTooltipClass="tool_tip_custom_css"
                                                        routerLink="./add-medication-code/{{row.Id}}">edit</em>
                                                    <em class="material-icons" matTooltip="Delete"
                                                        matTooltipClass="tool_tip_custom_css" style="cursor: pointer;"
                                                        (click)="deleteMedication(row)">delete</em>
                                                </td>
                                            </ng-container>

                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                                            </tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" pageSize="50"></mat-paginator>
            </div>
        </div>
    </div>
</div>
<app-table-for-pdf [heads]="['MedicationName', 'MedicationCode', 'Status']" [title]="'Medications'" [datas]="pdfData">
</app-table-for-pdf>

<!-- Upload Modal -->
<div class="modal fade" id="exampleModal" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">Upload File</p>
            </div>
            <div class="modal-body row">
                <div class="col-4">
                    <a mat-raised-button class="template_btn" style="margin-top: 2.1em;"
                        href="assets/file/Sample medication codes sheet.xlsx" color="primary">Download
                        Template
                    </a>
                </div>
                <div class="col-8" style="float: right; margin-bottom: 1em;">
                    <app-file-picker [control]="fileForm.controls.file" [fieldName]="'CSV'" [fieldType]="'select'"
                        [allowedExtensions]="['csv', 'text/csv']" [fileupload]="xlsxFile" [maxMB]="52428800"
                        [maxSize]="50" (selectedFile)="getMediaFile($event)" [required]="false">
                    </app-file-picker>
                </div>
                <div class="offset-8">
                    <button type="button" class="btn btn-primary" data-dismiss="modal"
                        (click)="uploadFile()">
                        Upload
                    </button>
                    <button type="button" class="btn btn-primary" data-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-alignment">
                    <div class="col-12">
                        <dl class="important__note font-italic pt-4" style="margin-bottom: -.5rem;">
                            <dt style="color: red">
                                To upload in bulk, you have to follow these steps:
                            </dt>
                        </dl>
                        <div class="row">
                            <div class="col-12">
                                <div class="table-responsive table-full-width"></div>
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th colspan="7">Step 1: Download the sample template here.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <td colspan="7">Download sample file</td>
                                    </tbody>
                                </table>
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th colspan="7">Step 2: Fill out the information in respective columns and
                                                save
                                                the tab into a separate file with .csv extension.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container>
                                            <tr>
                                                <td colspan="7">For eg: Sample_Medication_CodesFile.csv</td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th colspan="7">Step 3: Upload the CSV file here.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container>
                                            <td colspan="7"><span>CSV Upload</span><br>
                                            </td>
                                        </ng-container>
                                    </tbody>
                                </table>
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th colspan="7">Step 4: You have successfully uploaded the file.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container>
                                            <td colspan="7">CSV - Upload</td>
                                        </ng-container>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>