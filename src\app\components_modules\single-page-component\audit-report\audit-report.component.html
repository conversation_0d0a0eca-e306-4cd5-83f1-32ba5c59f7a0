<div class="main-content">
  <div class="container-fluid">
    <!-- <app-summary-menu-list [routes]="routes"></app-summary-menu-list> -->
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <p style="float: right">
                <button
                  mat-raised-button
                  color="primary"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    (click)="exportTable('pdf')"
                    class="nav-link"
                  >
                    <p style="display: inline-block">PDF</p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li class="nav" style="margin-left: 1%; line-height: 35px">
                  <p>Audit Report</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin">
                  <form [formGroup]="auditReportForm">
                    <fieldset class="scheduler-border">
                      <legend class="scheduler-border">
                        Filter Audit Reports
                      </legend>
                      <div class="row">
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Category</mat-label>
                            <mat-select
                              formControlName="category"
                              disableOptionCentering
                            >
                              <mat-option
                                *ngFor="let category of categoryList"
                                [value]="category"
                              >
                                {{ category }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field class="example-full-width">
                            <input
                              matInput
                              placeholder="Event Type"
                              formControlName="event_type"
                            />
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <app-datep-picker
                            [dateConfiguration]="FromDate"
                            [control]="auditReportForm.controls.from_date"
                            [fieldName]="FromDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6">
                          <app-datep-picker
                            [dateConfiguration]="ToDate"
                            [control]="auditReportForm.controls.to_date"
                            [fieldName]="ToDate.label"
                            [fieldType]="'select'"
                            (getDate)="getDate()"
                          >
                          </app-datep-picker>
                        </div>
                        <div class="col-6">
                          <mat-form-field class="example-full-width">
                            <input
                              matInput
                              placeholder="Username"
                              formControlName="username"
                            />
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field class="example-full-width">
                            <input
                              matInput
                              placeholder="Request ip"
                              formControlName="request_ip"
                            />
                          </mat-form-field>
                        </div>
                        <div class="col-12">
                          <button
                            mat-raised-button
                            type="submit"
                            class="btn btn-primary"
                            (click)="searchByData()"
                          >
                            Search
                          </button>
                          <button
                            mat-raised-button
                            (click)="restForm()"
                            class="btn btn-white"
                          >
                            Reset
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>

                <!-- table start-->
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="enhancedReport"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>
                      <ng-container matColumnDef="date_time">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Date/Time
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.request_date
                              ? (row.request_date | localDateConversion: "full")
                              : "--"
                          }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="user_id">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          User ID
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.username ? row.username : "--" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="category">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Category
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.category ? row.category : "--" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="requested_url">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Requested URL
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.request_url ? row.request_url : "--" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="terminal_identity">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Terminal Identify
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{
                            row.terminal_identity ? row.terminal_identity : "--"
                          }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="event_type">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Event Type
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.event_type ? row.event_type : "--" }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="event_details">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Event Details
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.event_details ? row.event_details : "--" }}
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="exception">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Exception
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.exception ? row.exception : "--" }}
                        </td>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="
                        dataSource && dataSource['filteredData'].length === 0
                      "
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
                <!-- table end -->
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

<app-multitablepdf
  [tables]="tables"
  [title]="'Summary KPI Report'"
></app-multitablepdf>
