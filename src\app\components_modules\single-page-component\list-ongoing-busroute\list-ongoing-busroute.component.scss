@import "../../../../assets/scss/navbar.scss";
td,
th {
  width: auto !important;
  max-width: auto;
  min-width: auto;
}

.modal-body ul li {
  list-style: none;
}

.modal-dialog {
  width: 32%;
}

.modal-footer {
  border-top: none;
}

.modal-header {
  text-align: center;
  display: inline;
  border-bottom: none;
  color: #16078a;
  font-size: 18px;
}

::ng-deep .cdk-overlay-container {
  z-index: 99999 !important;
}

.modal.show .modal-dialog {
  margin-top: 150px !important;
}
// this is ngx timepicker css
::ng-deep .ngx-timepicker-control__arrow {
  display: none;
}

::ng-deep .ngx-timepicker {
  position: relative;
  top: -15px;
  align-items: flex-end !important;
}

::ng-deep .ngx-timepicker-control {
  width: 32px !important;
  height: 23px !important;
  padding: 0 0px !important;
}
::ng-deep .ngx-timepicker__time-colon {
  margin-left: 0px !important;
}
::ng-deep .period-control__button {
  width: auto !important;
  font-size: 0.9rem !important;
}

.scheduler-border {
  padding: 1em !important;
}
