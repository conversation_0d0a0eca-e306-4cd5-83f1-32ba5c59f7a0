import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AddUpdateCosyDepartmentsComponent } from './add-update-cosy-departments/add-update-cosy-departments.component';
import { ListCosyDepartmentsComponent } from './list-cosy-departments/list-cosy-departments.component';

const routes: Routes = [
  {path: '', component: ListCosyDepartmentsComponent},
  {path: 'addupdateCoSyDepartments', component: AddUpdateCosyDepartmentsComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CosyDepartmentsRoutingModule { }
