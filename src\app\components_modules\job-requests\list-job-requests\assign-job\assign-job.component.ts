import { Component, Inject, OnInit, ViewChild } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialogRef,
  MatPaginator,
  MatSort,
  MatTableDataSource,
  MAT_DIALOG_DATA,
} from "@angular/material";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { of, ReplaySubject, Subject } from "rxjs";
import { map, takeUntil } from "rxjs/operators";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { JobRequests } from "src/app/models/JobRequests";
import * as $ from "q";
@Component({
  selector: "app-assign-job",
  templateUrl: "./assign-job.component.html",
  styleUrls: ["./assign-job.component.scss"],
})
export class AssignJobComponent implements OnInit {
  allowReamrks = false;
  actionStatus = { value: "INQ", color_code: "" };
  displayedColumns: string[] = [
    "order_no",
    "request_time",
    "from_location",
    "to_location",
    "task",
    "patient_name",
    "porter_name",
    "order_id",
  ];
  public staffFilterCtrl: FormControl = new FormControl();
  pdfData = [];
  colorCodes = [];
  filter = "";
  isEditDelayReason = false;
  reasons = [];
  delayReasonsofJob = [];
  editableDelayReasinId = "";
  staffs = [];
  staffStatus: any = "";
  toatlJobsStaff: any;
  imageUrl: any;
  reCalldata: any;
  hoveredJob: any;
  actionForm: FormGroup;

  dataSource: MatTableDataSource<JobRequests>;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  htmlContent: string = "<p>Content goes here</p>";
  todaydate = moment().format("DD/MM/YYYY HH:mm");
  loggedInUserId: string;
  isButtonList: any[] = [];
  taskRequest = {
    patient: null,
    location_id: null,
    date: "",
    job_category: null,
  };
  locationsList: any;
  protected _onDestroy = new Subject<void>();
  public filteredStaffs: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  skillSet: any;
  jobNumber: any;
  staffName$ = of({ staff_name: "" });
  sub_category_id: any;
  constructor(
    private readonly jobService: JobsService,
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    public facilityConfig: FacilityConfigService,
    public dialogRef: MatDialogRef<AssignJobComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.createActionForm();
    this.loggedInUserId = atob(localStorage.getItem("user_id"));
    this.jobNumber = data.jobNumber;
    this.sub_category_id = data.sub_category_id
    if (this.jobNumber.actionType == "Cancel") {
      this.getReasons("cancel");
    } else if (this.jobNumber.actionType == "Delay Reason") {
      this.getReasons("delay");
      this.getDelayReasons(this.jobNumber.order_no);
    }
  }

  createActionForm() {
    this.actionForm = this.fb.group({
      reason: ["", Validators.required],
      time: [{ value: moment().format("DD/MM/YYYY HH:mm"), disabled: true }],
      staff: ["", Validators.required],
      remarks: [""],
    });
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  ngOnInit() {
    this.getCurrentDateTime();

    // listen for search field value changes
    this.staffFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterStaff();
      });
    this.getStaff();
  }

  protected filterStaff() {
    if (!this.staffs) {
      return;
    }
    // get the search keyword
    let search = this.staffFilterCtrl.value;
    if (!search) {
      this.filteredStaffs.next(this.staffs.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the staffs
    this.filteredStaffs.next(
      this.staffs.filter(
        (staff) => staff.staff_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.todaydate = res;
      this.createActionForm();
    });
  }

  getStaff() {
    this.staffs = [];
    this.jobService.getAssignstaff(this.sub_category_id).subscribe(
      (res) => {
        this.staffs = res || [];
        this.filteredStaffs.next(this.staffs.slice());
      },
      (error) => {
        this.dialogRef.close(true);
      }
    );
  }

  getReasons(reason) {
    this.reasons = [];
    this.jobService
      .getReasons(
        reason === "cancel"
          ? "api/viewstatus/cancel/reason"
          : "api/viewstatus/delayreasons"
      )
      .subscribe(
        (res) => {
          res =
            res &&
            res.filter((vals) => {
              if (vals.status) {
                return vals;
              }
            });
          this.reasons = res || [];
        },
        (error) => {
          this.dialogRef.close(true);
        }
      );
  }

  viewJobDetailsHover(data) {
    this.hoveredJob = data;
  }

  getDelayReasons(jobNumber) {
    this.delayReasonsofJob = [];
    if (jobNumber) {
      this.jobService.getDelayReasonsForJob(jobNumber).subscribe(
        (res) => {
          this.delayReasonsofJob = res || [];
        },
        (error) => {
          this.dialogRef.close(true);
        }
      );
    }
  }

  staffChanged(event) {
    if (event) {
      this.jobService.getStaffForJobrequestbyid(event).subscribe((res) => {
        this.staffStatus = res.status ? res.status : "0 Job(s) in hand";
        this.toatlJobsStaff = res.total_jobs;
        this.skillSet = res.skillset;

        if (res.staff_photo) {
          this.userService.downloadImages(res.staff_photo).subscribe((img) => {
            const blob = new Blob([img.body], {
              type: "image/png",
            });
            const oFReader = new FileReader();
            oFReader.readAsDataURL(blob);
            oFReader.onload = (oFREvent) => {
              this.imageUrl = oFREvent && oFREvent.target;
            };
          });
        }
      });
      this.staffName$ = this.filteredStaffs.pipe(
        map((data) => data.find((d) => d.staff_id == event))
      );
    }
  }

  actionTaken() {
    switch (this.jobNumber.actionType) {
      case "Assign":
        this.assignReassign("assign");
        break;
      case "Cancel":
        if (this.actionForm.get("reason").valid) {
          const x = this.actionForm.value;
          this.jobService
            .takeAction(`api/viewstatus/cancel/${this.jobNumber.order_id}`, x)
            .subscribe(
              () => {
                this.toastr.success("Job cancelled successfully", "Success");
                this.actionFormSubmiited();
                this.dialogRef.close(true);
              },
              (err) => {
                console.log(err);
                this.dialogRef.close(true);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Respond":
        this.startComplete("start", "startdate");
        break;
      case "Reassign":
        this.assignReassign("reassign");
        break;
      case "Delay Reason":
        if (this.actionForm.get("reason").valid) {
          const x = Object.assign({}, this.actionForm.value);
          const y = {
            job_no: this.jobNumber.order_no,
            delay_reason: x.reason,
            remark: x.remarks,
          };
          this.jobService
            .jobRequest(
              y,
              this.isEditDelayReason
                ? `api/viewstatus/delayreason/edit/${this.editableDelayReasinId}`
                : `api/viewstatus/delayreason/add`,
              this.isEditDelayReason ? "up" : "add"
            )
            .subscribe(
              () => {
                this.toastr.success(
                  "Delay reason added successfully",
                  "Success"
                );
                this.actionFormSubmiited();
                this.dialogRef.close(true);
              },
              (err) => {
                console.log(err);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Complete":
        this.startComplete("complete", "completion_date");
        break;
      case "Return Equipment":
        this.jobService
          .takeAction(
            `api/viewstatus/returnEquipment/${this.jobNumber.order_id}`
          )
          .subscribe(
            () => {
              this.toastr.success("Equipment returned successfully", "Success");
              this.actionFormSubmiited();
              this.dialogRef.close(true);
            },
            (err) => {
              console.log(err);
              this.dialogRef.close(true);
            }
          );
        break;
      case "Fetchback":
        this.jobService
          .takeAction(`api/viewstatus/fetchback/${this.jobNumber.order_no}`)
          .subscribe(
            () => {
              this.toastr.success("Fetched back successfully", "Success");
              this.actionFormSubmiited();
              this.dialogRef.close(true);
            },
            (err) => {
              console.log(err);
              this.dialogRef.close(true);
            }
          );
        break;
      default:
        this.actionFormSubmiited();
        this.dialogRef.close(true);
    }
  }

  startComplete(action, actionType) {
    const x = this.todaydate;
    const formattedDate = moment(x).format("YYYY-MM-DD HH:mm ");
    this.jobService
      .takeAction(
        `api/viewstatus/${action}/${this.jobNumber.order_id}?${actionType}=${formattedDate}`
      )
      .subscribe(
        () => {
          const status = action && action == 'complete' ? `${action}d` : `${action}ed`
          this.toastr.success(`Job ${status} successfully`, "Success");
          this.actionFormSubmiited();
          this.dialogRef.close(true);
        },
        (err) => {
          console.log(err);
          this.dialogRef.close(true);
        }
      );
  }

  cancelReasonChange(reason) {
    if (reason) {
      const exist = this.reasons.find(
        (resn) => resn.cancel_template_id === Number(reason)
      );
      if (exist) {
        this.allowReamrks = exist.allow_remarks;
      }
      const existdelay = this.reasons.find(
        (resn) => resn.delay_id === Number(reason)
      );
      if (existdelay) {
        this.allowReamrks = existdelay.allow_remarks;
      }
    }
  }

  assignReassign(action) {
    if (this.actionForm.get("staff").valid) {
      const x = this.actionForm.get("staff").value;
      this.jobService
        .takeAction(
          `api/viewstatus/${action}/${this.jobNumber.order_id}?porterid=${x}`
        )
        .subscribe(
          () => {
            this.toastr.success(`Job ${action}ed successfully`, "Success");
            this.actionFormSubmiited();
            this.dialogRef.close(true);
          },
          (err) => {
            console.log(err);
            this.dialogRef.close(true);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.actionForm.markAllAsTouched();
    }
  }

  editDelayReason(data) {
    this.isEditDelayReason = true;
    this.editableDelayReasinId = data.delay_rsn_id;
    this.actionForm.get("reason").setValue(Number(data.delay_reason));
    this.actionForm.get("remarks").setValue(data.remark);
  }

  actionFormSubmiited() {
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
    this.ngOnInit();
    this.actionForm.reset();
    this.createActionForm();
    this.isEditDelayReason = false;
    this.editableDelayReasinId = "";
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  dateStructure(date) {
    return date.replace("T", " ");
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locationsList = res;
    });
  }
  onNoClick(): void {
    this.dialogRef.close();
  }
  ngOnDestroy() {
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
  }
}
