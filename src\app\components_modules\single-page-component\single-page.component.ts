import { Component, OnInit, ViewChild } from "@angular/core";
import { MatSidenav, MatTabChangeEvent } from "@angular/material";
import {
  ActivatedRoute,
  NavigationEnd,
  NavigationStart,
  Router,
} from "@angular/router";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { RoutingService } from "src/app/Apiservices/routingService/routing.service";
import { ReportMenuStatic } from "src/app/config/report-menu.config";

@Component({
  selector: "app-single-page",
  templateUrl: "./single-page.component.html",
  styleUrls: ["./single-page.component.scss"],
})
export class SinglePageComponent implements OnInit {
  @ViewChild("sidenav", { static: true }) sidenav: MatSidenav;
  sampleSidenav$: any;
  navLink: any = [];
  tabIndex: number = 2;
  constructor(
    private _facilityConfig: FacilityConfigService,
    public routingService: RoutingService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.getReportMenu();

    this.getRouterEvent();
  }

  getRouterEvent() {
    this.router.events.subscribe((val) => {
      // see also
      if (val instanceof NavigationEnd) {
        this.getReportMenu();
      }
    });
  }

  getReportMenu() {
    // this.sampleSidenav$ = this._facilityConfig.fetchReportMenu();
    this.sampleSidenav$ = ReportMenuStatic;
    ReportMenuStatic.subscribe((data: any) => {
      this.navLink = data.filter(
        (menu) =>
          menu.menu == this.activatedRoute.snapshot.firstChild.data["name"]
      );
      this.navLink = this.navLink.length ? this.navLink[0].submenu_list : [];
    });
  }
  close(value) {
    this.sidenav.toggle();
  }
  public tabChanged(tabChangeEvent: MatTabChangeEvent): void {
    this.tabIndex = tabChangeEvent.index;
  }
}
