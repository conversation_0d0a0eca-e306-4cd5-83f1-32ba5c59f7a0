import { Injectable } from "@angular/core";
import { HttpService } from "../httpService/http.service";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class JobsService {
  constructor(private readonly httpService: HttpService) {}

  getJobRequests(status?, jobCategory = null, method?): Observable<any> {
   return this.httpService.post(`${environment.base_url}api/viewstatus`, status);
  }

  getScanNric(nric, paramData?): Observable<any> {
    const queryParam = `?${paramData.name ? 'name='+paramData.name : ''}${paramData.from ? 'from='+paramData.from : ''}${paramData.to ? '&to='+paramData.to : ''}`;
    return this.httpService.get(
      `${environment.base_url}api/patientlogs/${nric}${queryParam}`
    );
  }

  getJobRequestByid(id, status): Observable<any> {
    const body = {
      'jobStatus': status,
      'orderid': id,
      'pageNo' : null,
      'pageSize': null
    }
    // ?jobStatus=${status}&orderid=${id}
    return this.httpService.post(
      `${environment.base_url}api/viewstatus`, body
    );
  }

  getJobRequestByidSearch(order): Observable<any> {
    const body = order;
    return this.httpService.post(
      `${environment.base_url}api/viewstatus/search`,
      { ...body }
    );
  }

  getReasons(url): Observable<any> {
    return this.httpService.get(`${environment.base_url + url}`);
  }

  getAssignstaff(id?: any): Observable<any> {
    const queryParam = id ? `?jobtype=${id}` : ''
    return this.httpService.get(
      `${environment.base_url}api/viewstatus/assign/staff${queryParam}`
    );
  }

  getDelayReasonsForJob(jobno): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/viewstatus/delayreasons/list?job_no=${jobno}`
    );
  }

  takeAction(url, data?): Observable<any> {
    return this.httpService.put(
      `${environment.base_url + url}`,
      data ? data : null
    );
  }

  getmjcatogiresByid(type): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/jobrequests/mjcategories/${type}`
    );
  }

  getModesOfTransport(mjcategoryid, sjcategoryid): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/categorymappings/${mjcategoryid}/sjcategories/${sjcategoryid}`
    );
  }

  getColorCodes(data?): Observable<any> {
    // const paramData = {
    //   'filterType': data && data.filterType ? data.filterType : null,
    //   value
    // }
    return this.httpService.post(
      `${environment.base_url}api/viewstatus/jobstatus`, data || {'filterType': null}
    );
    // ?${key}=${value}
  }

  getStaffForJobrequestbyid(staffid): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/viewstatus/assign/staff/${staffid}`
    );
  }

  jobRequest(data, url, action): Observable<any> {
    if (action === "add") {
      return this.httpService.post(`${environment.base_url + url}`, data);
    } else {
      return this.httpService.put(`${environment.base_url + url}`, data);
    }
  }

  getDelayReasonBasedOnListDelayById(id): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/viewstatus/delayreasons/${id}`
    );
  }
}
