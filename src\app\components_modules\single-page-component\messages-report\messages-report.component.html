<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export
                                </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xlsx')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555"
                                        (click)="exportTable('csv')" class="nav-link">
                                        <p style="display: inline-block">csv</p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li class="nav" style="margin-left: 1%; line-height: 35px">
                                    <p>Message Reports</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin">
                                    <form [formGroup]="messagesReportForm">
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">
                                                Filter Message Reports
                                            </legend>
                                            <div class="row">
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="FromDate"
                                                        [control]="messagesReportForm.controls.from_date"
                                                        [fieldName]="FromDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-4">
                                                    <app-datep-picker [dateConfiguration]="ToDate"
                                                        [control]="messagesReportForm.controls.to_date"
                                                        [fieldName]="ToDate.label" [fieldType]="'select'"
                                                        (getDate)="getDate()">
                                                    </app-datep-picker>
                                                </div>
                                                <div class="col-4">
                                                    <mat-form-field>
                                                        <span matSuffix style="cursor: pointer"
                                                            (click)="clearFormValue('staffName');$event.stopPropagation()">
                                                            <mat-icon>clear</mat-icon>
                                                        </span>
                                                        <mat-label>Staff Name </mat-label>
                                                        <mat-select disableOptionCentering formControlName="staffName">
                                                            <mat-option>
                                                                <ngx-mat-select-search [formControl]="staffCtrl"
                                                                    [placeholderLabel]="'Find Staff...'"
                                                                    [noEntriesFoundLabel]="'no matching staff found'">
                                                                </ngx-mat-select-search>
                                                            </mat-option>
                                                            <mat-option *ngFor="let staff of filteredStaff | async"
                                                                [value]="staff.staff_id">{{ staff.staff_name }}
                                                            </mat-option>
                                                        </mat-select>
                                                    </mat-form-field>
                                                </div>

                                                <!-- <div class="col-4">
                                                    <mat-form-field>
                                                        <mat-label>Staff Name </mat-label>
                                                        <input matInput placeholder="Staff Name"
                                                            formControlName="staffName" />
                                                    </mat-form-field>
                                                </div> -->
                                                <div class="col-8"></div>
                                                <div class="col-4">
                                                    <button mat-raised-button type="submit"
                                                        class="btn btn-primary pull-right" (click)="searchByData()">
                                                        Search
                                                    </button>
                                                    <button mat-raised-button (click)="resetForm()"
                                                        class="btn btn-white pull-right">
                                                        Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                </div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto;"
                                        [ngStyle]="{'height': (dataSource && dataSource.filteredData.length === 0) ? '150px' : '600px'}">
                                        <table id="enhancedReport" mat-table [dataSource]="dataSource" matSort>
                                            <caption></caption>
                                            <ng-container matColumnDef="staffName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Staff Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.staffName ? row?.staffName : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="locationName">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Location Name
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.locationName }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="message">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Message
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.message ? row?.message : "--" }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="messageDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Message Date
                                                </th>

                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.messageDate && row?.messageDate !== '' ? (row.messageDate) :
                                                    '--'}}
                                                </td>

                                            </ng-container>

                                            <ng-container matColumnDef="ackDate">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Acknowledged Date
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{
                                                    row?.ackDate
                                                    ? (row?.ackDate)
                                                    : "--"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <ng-container matColumnDef="ackBy">
                                                <th id="" mat-header-cell *matHeaderCellDef mat-sort-header>
                                                    Acknowledged By
                                                </th>
                                                <td mat-cell *matCellDef="let row">
                                                    {{ row?.ackBy ? row?.ackBy : "--" }}
                                                </td>
                                            </ng-container>
                                            ]
                                            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                                        </table>
                                        <div *ngIf="dataSource && dataSource.filteredData.length === 0">
                                            No records to display.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons [pageSize]=50
                    (page)="pageChanged($event)"></mat-paginator>
            </div>
        </div>
    </div>
</div>