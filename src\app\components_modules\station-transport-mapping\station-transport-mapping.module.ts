import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StationTransportMappingRoutingModule } from './station-transport-mapping-routing.module';
import { ListTransportMappingComponent } from './list-transport-mapping/list-transport-mapping.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddUpdateTransportMappingComponent } from './add-update-transport-mapping/add-update-transport-mapping.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';


@NgModule({
  declarations: [ListTransportMappingComponent, AddUpdateTransportMappingComponent],
  imports: [
    CommonModule,
    StationTransportMappingRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    NgMultiSelectDropDownModule
  ]
})
export class StationTransportMappingModule { }
