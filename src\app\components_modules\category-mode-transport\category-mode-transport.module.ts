import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CategoryModeTransportRoutingModule } from './category-mode-transport-routing.module';
import { AddUpdateCategoryModeTransportComponent } from './add-update-category-mode-transport/add-update-category-mode-transport.component';
import { ListCategoryModeTransportComponent } from './list-category-mode-transport/list-category-mode-transport.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    AddUpdateCategoryModeTransportComponent,
    ListCategoryModeTransportComponent],
  imports: [
    CommonModule,
    CategoryModeTransportRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
  ]
})
export class CategoryModeTransportModule { }
