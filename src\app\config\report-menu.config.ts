import { of } from "rxjs";

export const ReportMenuStatic = of([
  {
    id: 38,
    menu: "ADT Patient Logs",
    submenu_list: null,
  },
  {
    id: 35,
    menu: "Feedback",
    submenu_list: null,
  },
  {
    id: 49,
    menu: "Enhanced Reports",
    submenu_list: [
      {
        id: 1,
        name: "Search(ACK)",
      },
      {
        id: 2,
        name: "Bus Route Reports",
      },
      {
        id: 3,
        name: "Porter Workload",
      },
      {
        id: 4,
        name: "PTS Reports",
      },
      // {
      //   id: 5,
      //   name: "Location Reports"
      // }
    ],
  },
  {
    id: 56,
    menu: "UAM reports",
    submenu_list: [
      {
        id: 14,
        name: "Roles",
      },
      {
        id: 15,
        name: "Role-Modules",
      },
      {
        id: 16,
        name: "Users",
      },
      {
        id: 17,
        name: "Disabled-Users",
      },
      {
        id: 18,
        name: "Active-Users",
      },
    ],
  },
  {
    id: 45,
    menu: "Summary Reports",
    submenu_list: [
      {
        id: 4,
        name: "Task Type Summary",
      },
      {
        id: 5,
        name: "Location Summary",
      },
      {
        id: 6,
        name: "Equipment Summary",
      },
      {
        id: 7,
        name: "Hourly Summary",
      },
      {
        id: 8,
        name: "KPI Report",
      },
    ],
  },
  {
    id: 46,
    menu: "KPI reports",
    submenu_list: [
      {
        id: 9,
        name: "Workload",
        submenu: [
          {
            id: 1,
            name: "Total Workload",
          },
          {
            id: 2,
            name: "CP Job Type",
          },
          {
            id: 3,
            name: "SP Job Type",
          },
          {
            id: 4,
            name: "SP Workload by Location",
          },
        ],
      },
      {
        id: 10,
        name: "Performance KPI",
        submenu: [
          {
            id: 1,
            name: "Performance KPI",
          },
        ],
      },
      {
        id: 11,
        name: "Cancellation",
        submenu: [
          // {
          //   id: 1,
          //   name: "Total Cancellation",
          // },
          {
            id: 1,
            name: "Cancellation Reasons",
          },
        ],
      },
      {
        id: 12,
        name: "KPI Summary",
        submenu: [
          {
            id: 1,
            name: "KPI Summary",
          },
        ],
      },
    ],
  },
  {
    id: 50,
    menu: "Station Reports",
    submenu_list: [
      {
        id: 12,
        name: "Station Porter Reports",
      },
      {
        id: 13,
        name: "Station Porter Specific Reports",
      },
    ],
  },
  {
    id: 61,
    menu: "Monthly Reports",
    submenu_list: null
  },
  {
    id: 64,
    menu: "Location Reports",
    submenu_list: null
  },
  {
    id: 66,
    menu: "Message Reports",
    submenu_list: [
      {
        id: 1,
        name: "Message Reports",
      },
      {
        id: 2,
        name: "RTLS Message Reports",
      }
    ],
  },
]);
