import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FacilityConfigService } from 'src/app/Apiservices/facilityConfig/facility-config.service';
import Swal from 'sweetalert2';
import * as moment from "moment";

@Component({
  selector: 'app-add-update-tower',
  templateUrl: './add-update-tower.component.html',
  styleUrls: ['./add-update-tower.component.scss']
})
export class AddUpdateTowerComponent implements OnInit {
  towersForm: FormGroup;
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService
  ) {
    this.towersForm = this.fb.group({
      towername: ['', Validators.required],
      shortdescription: ['', Validators.required],
      status: ['true', Validators.required],
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getTowerById();
  }

  getTowerById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.facilityConfig.getTowerById(this.activatedRoute.snapshot.params.id).
        subscribe(res => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          this.towersForm.patchValue({
            towername: res.tower_name,
            shortdescription: res.short_description,
            status: res.status === true ? 'true' : 'false',
            reason: res.reason,
            approval_status: res.approval_status
          });
        });
    }
  }

  saveTower(actiontype) {
    let towerId: any;
    if (this.towersForm.valid) {
      const data = this.towersForm.value;
      data.status = data.status === 'true' ? true : false;
      if (actiontype === 'update') {
        towerId = Number(this.activatedRoute.snapshot.params.id);
        if (data.approval_status && data.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.updateTower(data, actiontype, towerId);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.updateTower(data, actiontype, towerId);
        }
      } else {
        this.updateTower(data, actiontype, towerId);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.towersForm.markAllAsTouched();
    }
  }

  updateTower(data, actiontype, towerId) {
    this.facilityConfig.addUpadteTower(
      data, actiontype === 'save' ?
      'api/towers/add' : `api/towers/edit/${towerId}`, actiontype)
      .subscribe(res => {
        this.location.back();
        this.toastr.success(`Successfully ${actiontype === 'save' ? 'added' : 'updated'} tower`, 'Success');
      }, err => {
        console.log(err);
      });
  }

}
