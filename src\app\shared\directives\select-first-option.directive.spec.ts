import { NgModule } from "@angular/core";
import { TestBed } from "@angular/core/testing";
import { NgModel } from "@angular/forms";
import { SelectFirstOptionDirective } from "./select-first-option.directive";

describe("SelectFirstOptionDirective", () => {
  it("should create an instance", () => {
    TestBed.configureTestingModule({
      providers: [NgModule],
    });
    let newValue: NgModel;
    const directive = new SelectFirstOptionDirective(newValue);
    expect(directive).toBeTruthy();
  });
});
