import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UserAdministrativeRoutingModule } from './user-administrative-routing.module';
import { ListUsersComponent } from './list-users/list-users.component';
import { AddUpdateUserComponent } from './add-update-user/add-update-user.component';
import { DependencyModule } from 'src/app/dependency/dependency.module';
import { SharedThemeModule } from 'src/app/shared-theme/shared-theme.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

@NgModule({
  declarations: [ListUsersComponent, AddUpdateUserComponent],
  imports: [
    CommonModule,
    UserAdministrativeRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    NgxMatSelectSearchModule,
    NgMultiSelectDropDownModule
  ]
})
export class UserAdministrativeModule { }
