export interface SubJobCategory {
    sj_category_id: string;
    sj_category_name: string;
    mj_category_id: string;
    mj_category_name: string;
    sj_short_discr: string;
    std_time_code: string;
    std_code: string;
    arrival_time: string;
    ack_time: string;
    start_time: string;
    end_time: string;
    total_time: string;
    priority: string;
    skill_level: string;
    required_sex: string;
    display_in: string;
    no_of_porters: string;
    external_move: boolean;
    status: boolean;
    patient_info: boolean;
    round_trip: boolean;
    visible: string;
    ack_req: boolean;
    patient_move: boolean;
    SAGroup: boolean;
    SAAllowed: boolean;
    SAStack: boolean;
    SAStacktothis: boolean;
    role_id_list: string;
}
