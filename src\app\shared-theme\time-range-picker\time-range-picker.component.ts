import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ValidationService } from "src/app/Apiservices/validation/validation.service";

@Component({
  selector: "app-time-range-picker",
  templateUrl: "./time-range-picker.component.html",
  styleUrls: ["./time-range-picker.component.scss"],
})
export class TimeRangePickerComponent implements OnInit {
  @Input() rangePicker = false;
  @Input() label = "";
  @Input() required = true;
  @Input() placeholder = "Select Time";
  @Input() control: FormControl;
  @Input() fieldName: string;
  @Input() fieldType: string;
  @Input() completeError: string;
  @Input() disable: boolean = false;

  @Output() timeChanged = new EventEmitter<any>();

  constructor(private readonly validationService: ValidationService) {}

  ngOnInit() {
    this.control.valueChanges.subscribe((res) => {
      this.timeChanged.emit(res);
    });
  }
  get errorMessage() {
    for (const propertyName in this.control && this.control.errors) {
      if (
        (this.control && this.control.errors).hasOwnProperty(propertyName) &&
        this.control.touched
      ) {
        return this.validationService.getValidatorErrorMessage(
          propertyName,
          this.fieldType,
          this.fieldName,
          (this.control && this.control.errors)[propertyName],
          this.completeError
        );
      }
    }
    return null;
  }
}
